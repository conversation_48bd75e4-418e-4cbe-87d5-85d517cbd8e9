import{C as _,ft as v,fu as d,F as P,A as R,y as re,S as ae,gU as G,r as F,K as j,gs as N,fL as O,V as H,ga as I,d as M,q as y,fz as ie,z as D,hh as K,U as V,fA as $,fv as L,W,fM as de,fN as se,a6 as le}from"./index-pY9FjpQW.js";const ue=_("radio",`
 line-height: var(--n-label-line-height);
 outline: none;
 position: relative;
 user-select: none;
 -webkit-user-select: none;
 display: inline-flex;
 align-items: flex-start;
 flex-wrap: nowrap;
 font-size: var(--n-font-size);
 word-break: break-word;
`,[v("checked",[d("dot",`
 background-color: var(--n-color-active);
 `)]),d("dot-wrapper",`
 position: relative;
 flex-shrink: 0;
 flex-grow: 0;
 width: var(--n-radio-size);
 `),_("radio-input",`
 position: absolute;
 border: 0;
 border-radius: inherit;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 opacity: 0;
 z-index: 1;
 cursor: pointer;
 `),d("dot",`
 position: absolute;
 top: 50%;
 left: 0;
 transform: translateY(-50%);
 height: var(--n-radio-size);
 width: var(--n-radio-size);
 background: var(--n-color);
 box-shadow: var(--n-box-shadow);
 border-radius: 50%;
 transition:
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
 `,[R("&::before",`
 content: "";
 opacity: 0;
 position: absolute;
 left: 4px;
 top: 4px;
 height: calc(100% - 8px);
 width: calc(100% - 8px);
 border-radius: 50%;
 transform: scale(.8);
 background: var(--n-dot-color-active);
 transition: 
 opacity .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 transform .3s var(--n-bezier);
 `),v("checked",{boxShadow:"var(--n-box-shadow-active)"},[R("&::before",`
 opacity: 1;
 transform: scale(1);
 `)])]),d("label",`
 color: var(--n-text-color);
 padding: var(--n-label-padding);
 font-weight: var(--n-label-font-weight);
 display: inline-block;
 transition: color .3s var(--n-bezier);
 `),P("disabled",`
 cursor: pointer;
 `,[R("&:hover",[d("dot",{boxShadow:"var(--n-box-shadow-hover)"})]),v("focus",[R("&:not(:active)",[d("dot",{boxShadow:"var(--n-box-shadow-focus)"})])])]),v("disabled",`
 cursor: not-allowed;
 `,[d("dot",{boxShadow:"var(--n-box-shadow-disabled)",backgroundColor:"var(--n-color-disabled)"},[R("&::before",{backgroundColor:"var(--n-dot-color-disabled)"}),v("checked",`
 opacity: 1;
 `)]),d("label",{color:"var(--n-text-color-disabled)"}),_("radio-input",`
 cursor: not-allowed;
 `)])]),ce={name:String,value:{type:[String,Number,Boolean],default:"on"},checked:{type:Boolean,default:void 0},defaultChecked:Boolean,disabled:{type:Boolean,default:void 0},label:String,size:String,onUpdateChecked:[Function,Array],"onUpdate:checked":[Function,Array],checkedValue:{type:Boolean,default:void 0}},q=re("n-radio-group");function be(o){const e=ae(q,null),t=G(o,{mergedSize(n){const{size:s}=o;if(s!==void 0)return s;if(e){const{mergedSizeRef:{value:u}}=e;if(u!==void 0)return u}return n?n.mergedSize.value:"medium"},mergedDisabled(n){return!!(o.disabled||e?.disabledRef.value||n?.disabled.value)}}),{mergedSizeRef:i,mergedDisabledRef:r}=t,c=F(null),b=F(null),f=F(o.defaultChecked),a=j(o,"checked"),p=N(a,f),m=O(()=>e?e.valueRef.value===o.value:p.value),w=O(()=>{const{name:n}=o;if(n!==void 0)return n;if(e)return e.nameRef.value}),g=F(!1);function C(){if(e){const{doUpdateValue:n}=e,{value:s}=o;I(n,s)}else{const{onUpdateChecked:n,"onUpdate:checked":s}=o,{nTriggerFormInput:u,nTriggerFormChange:l}=t;n&&I(n,!0),s&&I(s,!0),u(),l(),f.value=!0}}function x(){r.value||m.value||C()}function k(){x(),c.value&&(c.value.checked=m.value)}function z(){g.value=!1}function S(){g.value=!0}return{mergedClsPrefix:e?e.mergedClsPrefixRef:H(o).mergedClsPrefixRef,inputRef:c,labelRef:b,mergedName:w,mergedDisabled:r,renderSafeChecked:m,focus:g,mergedSize:i,handleRadioInputChange:k,handleRadioInputBlur:z,handleRadioInputFocus:S}}const fe=Object.assign(Object.assign({},D.props),ce),me=M({name:"Radio",props:fe,setup(o){const e=be(o),t=D("Radio","-radio",ue,K,o,e.mergedClsPrefix),i=V(()=>{const{mergedSize:{value:p}}=e,{common:{cubicBezierEaseInOut:m},self:{boxShadow:w,boxShadowActive:g,boxShadowDisabled:C,boxShadowFocus:x,boxShadowHover:k,color:z,colorDisabled:S,colorActive:n,textColor:s,textColorDisabled:u,dotColorActive:l,dotColorDisabled:h,labelPadding:B,labelLineHeight:T,labelFontWeight:U,[$("fontSize",p)]:A,[$("radioSize",p)]:E}}=t.value;return{"--n-bezier":m,"--n-label-line-height":T,"--n-label-font-weight":U,"--n-box-shadow":w,"--n-box-shadow-active":g,"--n-box-shadow-disabled":C,"--n-box-shadow-focus":x,"--n-box-shadow-hover":k,"--n-color":z,"--n-color-active":n,"--n-color-disabled":S,"--n-dot-color-active":l,"--n-dot-color-disabled":h,"--n-font-size":A,"--n-radio-size":E,"--n-text-color":s,"--n-text-color-disabled":u,"--n-label-padding":B}}),{inlineThemeDisabled:r,mergedClsPrefixRef:c,mergedRtlRef:b}=H(o),f=L("Radio",b,c),a=r?W("radio",V(()=>e.mergedSize.value[0]),i,o):void 0;return Object.assign(e,{rtlEnabled:f,cssVars:r?void 0:i,themeClass:a?.themeClass,onRender:a?.onRender})},render(){const{$slots:o,mergedClsPrefix:e,onRender:t,label:i}=this;return t?.(),y("label",{class:[`${e}-radio`,this.themeClass,this.rtlEnabled&&`${e}-radio--rtl`,this.mergedDisabled&&`${e}-radio--disabled`,this.renderSafeChecked&&`${e}-radio--checked`,this.focus&&`${e}-radio--focus`],style:this.cssVars},y("input",{ref:"inputRef",type:"radio",class:`${e}-radio-input`,value:this.value,name:this.mergedName,checked:this.renderSafeChecked,disabled:this.mergedDisabled,onChange:this.handleRadioInputChange,onFocus:this.handleRadioInputFocus,onBlur:this.handleRadioInputBlur}),y("div",{class:`${e}-radio__dot-wrapper`}," ",y("div",{class:[`${e}-radio__dot`,this.renderSafeChecked&&`${e}-radio__dot--checked`]})),ie(o.default,r=>!r&&!i?null:y("div",{ref:"labelRef",class:`${e}-radio__label`},r||i)))}}),he=_("radio-group",`
 display: inline-block;
 font-size: var(--n-font-size);
`,[d("splitor",`
 display: inline-block;
 vertical-align: bottom;
 width: 1px;
 transition:
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 background: var(--n-button-border-color);
 `,[v("checked",{backgroundColor:"var(--n-button-border-color-active)"}),v("disabled",{opacity:"var(--n-opacity-disabled)"})]),v("button-group",`
 white-space: nowrap;
 height: var(--n-height);
 line-height: var(--n-height);
 `,[_("radio-button",{height:"var(--n-height)",lineHeight:"var(--n-height)"}),d("splitor",{height:"var(--n-height)"})]),_("radio-button",`
 vertical-align: bottom;
 outline: none;
 position: relative;
 user-select: none;
 -webkit-user-select: none;
 display: inline-block;
 box-sizing: border-box;
 padding-left: 14px;
 padding-right: 14px;
 white-space: nowrap;
 transition:
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 background: var(--n-button-color);
 color: var(--n-button-text-color);
 border-top: 1px solid var(--n-button-border-color);
 border-bottom: 1px solid var(--n-button-border-color);
 `,[_("radio-input",`
 pointer-events: none;
 position: absolute;
 border: 0;
 border-radius: inherit;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 opacity: 0;
 z-index: 1;
 `),d("state-border",`
 z-index: 1;
 pointer-events: none;
 position: absolute;
 box-shadow: var(--n-button-box-shadow);
 transition: box-shadow .3s var(--n-bezier);
 left: -1px;
 bottom: -1px;
 right: -1px;
 top: -1px;
 `),R("&:first-child",`
 border-top-left-radius: var(--n-button-border-radius);
 border-bottom-left-radius: var(--n-button-border-radius);
 border-left: 1px solid var(--n-button-border-color);
 `,[d("state-border",`
 border-top-left-radius: var(--n-button-border-radius);
 border-bottom-left-radius: var(--n-button-border-radius);
 `)]),R("&:last-child",`
 border-top-right-radius: var(--n-button-border-radius);
 border-bottom-right-radius: var(--n-button-border-radius);
 border-right: 1px solid var(--n-button-border-color);
 `,[d("state-border",`
 border-top-right-radius: var(--n-button-border-radius);
 border-bottom-right-radius: var(--n-button-border-radius);
 `)]),P("disabled",`
 cursor: pointer;
 `,[R("&:hover",[d("state-border",`
 transition: box-shadow .3s var(--n-bezier);
 box-shadow: var(--n-button-box-shadow-hover);
 `),P("checked",{color:"var(--n-button-text-color-hover)"})]),v("focus",[R("&:not(:active)",[d("state-border",{boxShadow:"var(--n-button-box-shadow-focus)"})])])]),v("checked",`
 background: var(--n-button-color-active);
 color: var(--n-button-text-color-active);
 border-color: var(--n-button-border-color-active);
 `),v("disabled",`
 cursor: not-allowed;
 opacity: var(--n-opacity-disabled);
 `)])]);function ge(o,e,t){var i;const r=[];let c=!1;for(let b=0;b<o.length;++b){const f=o[b],a=(i=f.type)===null||i===void 0?void 0:i.name;a==="RadioButton"&&(c=!0);const p=f.props;if(a!=="RadioButton"){r.push(f);continue}if(b===0)r.push(f);else{const m=r[r.length-1].props,w=e===m.value,g=m.disabled,C=e===p.value,x=p.disabled,k=(w?2:0)+(g?0:1),z=(C?2:0)+(x?0:1),S={[`${t}-radio-group__splitor--disabled`]:g,[`${t}-radio-group__splitor--checked`]:w},n={[`${t}-radio-group__splitor--disabled`]:x,[`${t}-radio-group__splitor--checked`]:C},s=k<z?n:S;r.push(y("div",{class:[`${t}-radio-group__splitor`,s]}),f)}}return{children:r,isButtonGroup:c}}const ve=Object.assign(Object.assign({},D.props),{name:String,value:[String,Number,Boolean],defaultValue:{type:[String,Number,Boolean],default:null},size:String,disabled:{type:Boolean,default:void 0},"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array]}),xe=M({name:"RadioGroup",props:ve,setup(o){const e=F(null),{mergedSizeRef:t,mergedDisabledRef:i,nTriggerFormChange:r,nTriggerFormInput:c,nTriggerFormBlur:b,nTriggerFormFocus:f}=G(o),{mergedClsPrefixRef:a,inlineThemeDisabled:p,mergedRtlRef:m}=H(o),w=D("Radio","-radio-group",he,K,o,a),g=F(o.defaultValue),C=j(o,"value"),x=N(C,g);function k(l){const{onUpdateValue:h,"onUpdate:value":B}=o;h&&I(h,l),B&&I(B,l),g.value=l,r(),c()}function z(l){const{value:h}=e;h&&(h.contains(l.relatedTarget)||f())}function S(l){const{value:h}=e;h&&(h.contains(l.relatedTarget)||b())}le(q,{mergedClsPrefixRef:a,nameRef:j(o,"name"),valueRef:x,disabledRef:i,mergedSizeRef:t,doUpdateValue:k});const n=L("Radio",m,a),s=V(()=>{const{value:l}=t,{common:{cubicBezierEaseInOut:h},self:{buttonBorderColor:B,buttonBorderColorActive:T,buttonBorderRadius:U,buttonBoxShadow:A,buttonBoxShadowFocus:E,buttonBoxShadowHover:Y,buttonColor:J,buttonColorActive:Q,buttonTextColor:X,buttonTextColorActive:Z,buttonTextColorHover:ee,opacityDisabled:oe,[$("buttonHeight",l)]:te,[$("fontSize",l)]:ne}}=w.value;return{"--n-font-size":ne,"--n-bezier":h,"--n-button-border-color":B,"--n-button-border-color-active":T,"--n-button-border-radius":U,"--n-button-box-shadow":A,"--n-button-box-shadow-focus":E,"--n-button-box-shadow-hover":Y,"--n-button-color":J,"--n-button-color-active":Q,"--n-button-text-color":X,"--n-button-text-color-hover":ee,"--n-button-text-color-active":Z,"--n-height":te,"--n-opacity-disabled":oe}}),u=p?W("radio-group",V(()=>t.value[0]),s,o):void 0;return{selfElRef:e,rtlEnabled:n,mergedClsPrefix:a,mergedValue:x,handleFocusout:S,handleFocusin:z,cssVars:p?void 0:s,themeClass:u?.themeClass,onRender:u?.onRender}},render(){var o;const{mergedValue:e,mergedClsPrefix:t,handleFocusin:i,handleFocusout:r}=this,{children:c,isButtonGroup:b}=ge(de(se(this)),e,t);return(o=this.onRender)===null||o===void 0||o.call(this),y("div",{onFocusin:i,onFocusout:r,ref:"selfElRef",class:[`${t}-radio-group`,this.rtlEnabled&&`${t}-radio-group--rtl`,this.themeClass,b&&`${t}-radio-group--button-group`],style:this.cssVars},c)}});export{xe as _,me as a};
