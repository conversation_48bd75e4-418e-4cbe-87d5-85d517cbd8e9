<script setup lang="tsx">
// @ts-nocheck
import http from "@/service/axios";
import moment from "moment-timezone";
import { useAuthStore } from "@/store/auth";
const { t } = useI18n();
const message = useMessage();
const { userData } = useAuthStore();
const checkRole = () => {
  if (
    (userData?.position_type == 4 && userData?.upline_position_type == 3) ||
    (userData?.position_type == 4 && userData?.upline_position_type == 2) ||
    (userData?.position_type == 4 && userData?.upline_position_type == 1)
  ) {
    const permis = JSON.parse(userData?.permissions);
    if (permis.management !== 2) {
      return false;
    }
  }
  return true;
};
const whitelist_ip = ref([]);
const whitelistOne = ref([]);
const items = ref(null);
onMounted(() => {
  GetProfile();
});
const currencyAll = ref([]);
const GetProfile = async () => {
  const { data: res } = await http.get("v1/Profile/agent");
  items.value = res.data;
  if (items.value.whitelist_ip.length) {
    //   items.value.whitelist_ip = items.value.whitelist_ip.map(ele => {
    //     return {
    //       label: ele,
    //       value: ele,
    //     }
    //   })
    whitelistOne.value = res.data.whitelist_ip[0];
  }
  currencyAll.value = [];
  if (res.data.position_type == 3) {
    const obj = {
      currency: "THB",
      images: "/images/country/th.webp",
      hold_percent: res.data.hold_percent,
    };
    currencyAll.value.push(obj);
  } else {
    if (res.data.reseller_thb) {
      const obj = {
        currency: "THB",
        images: "/images/country/th.webp",
        hold_percent: res.data.hold_percent,
      };
      currencyAll.value.push(obj);
    }
    if (res.data.reseller_krw) {
      const obj = {
        currency: "KRW",
        images: "/images/country/kr.webp",
        hold_percent: res.data.hold_percent_krw,
      };
      currencyAll.value.push(obj);
    }
    if (res.data.reseller_usd) {
      const obj = {
        currency: "USD",
        images: "/images/country/us.webp",
        hold_percent: res.data.hold_percent_usd,
      };
      currencyAll.value.push(obj);
    }
  }
};

const showModalProfile = ref(false);
const showModalWhitelistIP = ref(false);
const showModalCallbackUrl = ref(false);
const showSecretKey = ref(false);
const SecretKey = ref(null);
const CallbackUrl = ref(null);
const WhitelistIP = ref(null);
const listIP = ref([]);
const profileEdit = ref({
  oldpassword: null,
  newpassword: null,
  confirmpassword: null,
});
const rules = computed(() => ({
  oldpassword: [
    {
      required: true,
      message: t("validation.oldPasswordRequired"),
      trigger: "blur",
    },
  ],
  newpassword: [
    {
      required: true,
      message: t("validation.newPasswordRequired"),
      trigger: "blur",
    },
  ],
  confirmpassword: [
    {
      required: true,
      message: t("validation.confirmPasswordRequired"),
      trigger: "blur",
    },
  ],
  whitlistip: [
    {
      required: true,
      message: t("validation.whitelistIPRequired"),
      trigger: "blur",
    },
  ],
  callbackUrl: [
    {
      required: true,
      message: t("validation.callbackURLRequired"),
      trigger: "blur",
    },
  ],
}));
const time = (value) => {
  return moment(value).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm");
};
const showProfile = () => {
  profileEdit.value = {
    oldpassword: null,
    newpassword: null,
    confirmpassword: null,
  };
  showModalProfile.value = true;
};
const savePassword = () => {
  if (
    !profileEdit.value.oldpassword ||
    !profileEdit.value.newpassword ||
    !profileEdit.value.confirmpassword
  ) {
    return message.error(t("validation.passwordRequired"));
  }
  if (profileEdit.value.newpassword != profileEdit.value.confirmpassword) {
    return message.error(t("validation.passwordMismatch"));
  }
  const obj = {
    old_pass: profileEdit.value.oldpassword,
    new_pass: profileEdit.value.confirmpassword,
    password: profileEdit.value.newpassword,
  };
  http.post("v1/changepass", obj).then((response) => {
    if (response.data.success) {
      message.success(response.data.mes);
      GetProfile();
      showModalProfile.value = false;
    } else {
      message.error(response.data.mes);
    }
  });
};
const StoreKey = () => {
  SecretKey.value = null;
  showSecretKey.value = false;
  http.post("v1/Store/SecretKey").then((response) => {
    if (response.data.success) {
      message.success(t("secretKeyGeneratedSuccess"));
      SecretKey.value = response.data.mes;
      showSecretKey.value = true;
      GetProfile();
    } else {
      message.error(t("secretKeyGenerationError"));
    }
  });
};
const EditCallback = () => {
  CallbackUrl.value = items.value.callback_url;
  showModalCallbackUrl.value = true;
};
const saveCallback = () => {
  const obj = {
    CallbackUrl: CallbackUrl.value,
  };
  http.post("v1/Update/callbackurl", obj).then((response) => {
    if (response.data.success) {
      message.success(t("saveDataSuccess"));
      GetProfile();
      showModalCallbackUrl.value = false;
    } else {
      message.error(t("errorOccurred"));
    }
  });
};
const showWhitelistIP = (check) => {
  if (check) {
    listIP.value = items.value.whitelist_ip;
    showModalWhitelistIP.value = true;
  } else {
    showModalWhitelistIP.value = false;
    listIP.value = [];
    WhitelistIP.value = null;
    GetProfile();
  }
};
const WhitelistIPAdd = () => {
  if (WhitelistIP.value && !listIP.value.includes(WhitelistIP.value)) {
    listIP.value.unshift(WhitelistIP.value);
  }
};
// const DeleteIP = (index) => {
//   items.value.whitelist_ip.splice(index, 1);
// };
const saveWhitelist = () => {
  const obj = {
    whitelist_ip: listIP.value,
  };
  http.post("v1/Update/change_whitelist_ip", obj).then((response) => {
    if (response.data.success) {
      message.success(t("saveDataSuccess"));
      GetProfile();
      showModalWhitelistIP.value = false;
      listIP.value = [];
      WhitelistIP.value = null;
    } else {
      message.error(t("errorOccurred"));
    }
  });
};
const copyToClipboard = (item) => {
  if (item) {
    navigator.clipboard.writeText(item).then(() => {
      message.success(t("copysuccess"));
    });
  }
};
</script>

<template>
  <div>
    <div class="max-w-7xl mx-auto" v-if="items">
      <!-- Header Section -->
      <div class="mb-4">
        <n-card>
          <div class="flex flex-col sm:flex-row items-center justify-between">
            <div class="flex items-center space-x-4 mb-4 sm:mb-0">
              <n-avatar size="large" :style="{ backgroundColor: '#3b82f6' }">
                <icon-park-outline-user class="text-2xl" />
              </n-avatar>
              <div>
                <h1 class="text-2xl font-bold">{{ items.username }}</h1>
                <p class="text-gray-600 dark:text-gray-400">
                  {{
                    items.position_type == 1
                      ? $t("company")
                      : items.position_type == 2
                      ? $t("reseller")
                      : $t("agent")
                  }}
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <n-tag
                :type="items.status == 1 ? 'success' : 'error'"
                size="large"
              >
                {{ $t(items.status == 1 ? "active" : "inactive") }}
              </n-tag>
              <n-button @click="showProfile()" type="primary" size="medium">
                <template #icon>
                  <icon-park-outline-edit-name />
                </template>
                {{ $t("common.edit") }}
              </n-button>
            </div>
          </div>
        </n-card>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-4">
        <!-- Left Column - Basic Information -->
        <div class="xl:col-span-2 space-y-4">
          <!-- Account Information Card -->
          <n-card>
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-id-card class="text-lg" />
                <span class="font-semibold">{{ $t("basicinfomation") }}</span>
              </div>
            </template>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Account Type -->
              <div
                class="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <n-avatar :style="{ backgroundColor: '#8b5cf6' }">
                  <icon-park-outline-id-card class="text-xl" />
                </n-avatar>
                <div class="flex-1">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $t("accounttype") }}
                  </p>
                  <n-tag
                    size="small"
                    :type="
                      items.position_type == 1
                        ? 'error'
                        : items.position_type == 2
                        ? 'warning'
                        : 'success'
                    "
                  >
                    {{
                      items.position_type == 1
                        ? $t("company")
                        : items.position_type == 2
                        ? $t("reseller")
                        : $t("agent")
                    }}
                  </n-tag>
                </div>
              </div>

              <!-- Created Time -->
              <div
                class="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <n-avatar :style="{ backgroundColor: '#78350f' }">
                  <icon-park-outline-time class="text-xl" />
                </n-avatar>
                <div class="flex-1">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $t("createtime") }}
                  </p>
                  <n-text code>{{ time(items.created_at) }}</n-text>
                </div>
              </div>

              <!-- Last Login -->
              <div
                class="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <n-avatar :style="{ backgroundColor: '#000000' }">
                  <icon-park-outline-login class="text-xl" />
                </n-avatar>
                <div class="flex-1">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $t("lastlogin") }}
                  </p>
                  <n-text code>{{ time(items.last_login) }}</n-text>
                </div>
              </div>

              <!-- Status -->
              <div
                class="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <n-avatar
                  :style="{
                    backgroundColor: items.status == 1 ? '#10b981' : '#ef4444',
                  }"
                >
                  <icon-park-outline-check-one
                    v-if="items.status == 1"
                    class="text-xl"
                  />
                  <icon-park-outline-close-one v-else class="text-xl" />
                </n-avatar>
                <div class="flex-1">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $t("status") }}
                  </p>
                  <n-tag
                    size="small"
                    :type="items.status == 1 ? 'success' : 'error'"
                  >
                    {{ $t(items.status == 1 ? "active" : "inactive") }}
                  </n-tag>
                </div>
              </div>
            </div>
          </n-card>

          <!-- Currency Support Card -->
          <n-card>
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-dollar class="text-lg" />
                <span class="font-semibold">{{ $t("currencysupported") }}</span>
              </div>
            </template>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="item in currencyAll"
                :key="item.currency"
                class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700/30 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <img
                    :src="item.images"
                    width="32"
                    height="32"
                    class="rounded-sm"
                  />
                  <div>
                    <p class="font-medium">{{ item.currency }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {{ $t("hold") }} : {{ item.hold_percent }}%
                    </p>
                  </div>
                </div>
                <n-tag type="warning"> {{ item.hold_percent }}% </n-tag>
              </div>
            </div>
          </n-card>

          <!-- API Configuration (Only for Agents) -->
          <n-card v-if="items.position_type == 3">
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-api class="text-lg" />
                <span class="font-semibold">{{ $t("API-Configuration") }}</span>
              </div>
            </template>

            <div class="space-y-4">
              <!-- Bet Type -->
              <div
                class="flex items-center justify-between p-4 dark:bg-gray-800/20 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <n-avatar :style="{ backgroundColor: '#10b981' }">
                    <icon-park-outline-game class="text-xl" />
                  </n-avatar>
                  <div>
                    <p class="font-medium">{{ $t("bettype") }}</p>
                  </div>
                </div>
                <n-tag type="info">
                  {{ items.bet_type == 1 ? "Seamless" : "Transfer" }}
                </n-tag>
              </div>

              <!-- API Endpoint -->
              <div class="p-4 dark:bg-gray-800/20 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <n-avatar :style="{ backgroundColor: '#3b82f6' }">
                      <icon-park-outline-link class="text-xl" />
                    </n-avatar>
                    <p class="font-medium">API Endpoint</p>
                  </div>
                  <n-button
                    type="default"
                    secondary
                    size="small"
                    @click="copyToClipboard('https://api.pgf-theks69.com')"
                  >
                    <template #icon>
                      <icon-park-outline-copy />
                    </template>
                    {{ $t("copy") }}
                  </n-button>
                </div>
                <n-text code class="break-all"
                  >https://api.pgf-theks69.com</n-text
                >
              </div>

              <!-- Secret Key -->
              <div class="p-4 dark:bg-gray-800/20 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <n-avatar :style="{ backgroundColor: '#f59e0b' }">
                      <icon-park-outline-key class="text-xl" />
                    </n-avatar>
                    <p class="font-medium">Secret Key</p>
                  </div>
                  <n-space>
                    <n-button
                      type="warning"
                      secondary
                      size="small"
                      @click="StoreKey()"
                      :disabled="!checkRole()"
                    >
                      <template #icon>
                        <icon-park-outline-loading />
                      </template>
                      {{ $t("generateapikey") }}
                    </n-button>
                  </n-space>
                </div>
                <n-text code class="break-all">{{ items.secret_key }}</n-text>
              </div>

              <!-- Callback URL -->
              <div class="p-4 dark:bg-gray-800/20 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <n-avatar :style="{ backgroundColor: '#8b5cf6' }">
                      <icon-park-outline-send class="text-xl" />
                    </n-avatar>
                    <p class="font-medium">Callback URL</p>
                  </div>
                  <n-space>
                    <n-button
                      type="primary"
                      secondary
                      size="small"
                      @click="EditCallback()"
                      :disabled="!checkRole()"
                    >
                      <template #icon>
                        <icon-park-outline-edit />
                      </template>
                      {{ $t("edit") }}
                    </n-button>
                  </n-space>
                </div>
                <n-text code class="break-all">{{
                  items.callback_url || "Not configured"
                }}</n-text>
              </div>
            </div>
          </n-card>
        </div>

        <!-- Right Column - Security & Additional Info -->
        <div class="space-y-4">
          <!-- Security Settings Card -->
          <n-card>
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-shield class="text-lg" />
                <span class="font-semibold">{{ $t("security-settings") }}</span>
              </div>
            </template>

            <div class="space-y-4">
              <!-- Password Change -->
              <div
                class="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <n-avatar :style="{ backgroundColor: '#ef4444' }">
                    <icon-park-outline-lock class="text-xl" />
                  </n-avatar>
                  <div>
                    <p class="font-medium">{{ $t("password") }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {{ $t("changepassword") }}
                    </p>
                  </div>
                </div>
                <n-button
                  type="error"
                  secondary
                  size="small"
                  @click="showProfile()"
                >
                  <template #icon>
                    <icon-park-outline-edit />
                  </template>
                  {{ $t("edit") }}
                </n-button>
              </div>

              <!-- Whitelist IP (Only for Agents) -->
              <div
                v-if="items.position_type == 3"
                class="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <n-avatar :style="{ backgroundColor: '#f97316' }">
                    <icon-park-outline-shield class="text-xl" />
                  </n-avatar>
                  <div>
                    <p class="font-medium">Whitelist IP</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {{ items.whitelist_ip?.length || 0 }} IP(s)
                      {{ $t("configured") }}
                    </p>
                  </div>
                </div>
                <n-button
                  type="warning"
                  secondary
                  size="small"
                  @click="showWhitelistIP(true)"
                  :disabled="!checkRole()"
                >
                  <template #icon>
                    <icon-park-outline-setting />
                  </template>
                  {{ $t("manage") }}
                </n-button>
              </div>
            </div>
          </n-card>

          <!-- Whitelist IP List (Only for Agents) -->
          <n-card
            v-if="items.position_type == 3 && items.whitelist_ip?.length > 0"
          >
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-list class="text-lg" />
                <span class="font-semibold">{{
                  $t("Current-Whitelist-IPs")
                }}</span>
              </div>
            </template>

            <div class="space-y-2">
              <div
                v-for="(ip, index) in items.whitelist_ip"
                :key="index"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <n-avatar
                    size="small"
                    :style="{ backgroundColor: '#10b981' }"
                  >
                    <icon-park-outline-computer class="text-sm" />
                  </n-avatar>
                  <n-text code>{{ ip }}</n-text>
                </div>
                <!-- <n-button
                  type="error"
                  quaternary
                  size="small"
                  @click="DeleteIP(index)"
                  :disabled="!checkRole()"
                >
                  <template #icon>
                    <icon-park-outline-delete />
                  </template>
                </n-button> -->
              </div>
            </div>
          </n-card>

          <!-- Quick Actions Card -->
          <n-card>
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-lightning class="text-lg" />
                <span class="font-semibold">{{ $t("quick-actions") }}</span>
              </div>
            </template>

            <div class="space-y-3">
              <n-button block type="default" @click="showProfile()">
                <template #icon>
                  <icon-park-outline-lock />
                </template>
                {{ $t("editpassword") }}
              </n-button>

              <n-button
                v-if="items.position_type == 3"
                block
                type="default"
                @click="StoreKey()"
                :disabled="!checkRole()"
              >
                <template #icon>
                  <icon-park-outline-key />
                </template>
                {{ $t("generateapikey") }}
              </n-button>

              <n-button
                v-if="items.position_type == 3"
                block
                type="default"
                @click="EditCallback()"
                :disabled="!checkRole()"
              >
                <template #icon>
                  <icon-park-outline-send />
                </template>
                {{ $t("editcallbackurl") }}
              </n-button>

              <n-button
                v-if="items.position_type == 3"
                block
                type="default"
                @click="showWhitelistIP(true)"
                :disabled="!checkRole()"
              >
                <template #icon>
                  <icon-park-outline-shield />
                </template>
                {{ $t("editwhitelistip") }}
              </n-button>
            </div>
          </n-card>

          <!-- Account Statistics Card -->
          <n-card>
            <template #header>
              <div class="flex items-center space-x-2">
                <icon-park-outline-chart-line class="text-lg" />
                <span class="font-semibold">{{
                  $t("account-statistics")
                }}</span>
              </div>
            </template>

            <div class="space-y-4">
              <div
                class="text-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg"
              >
                <div
                  class="text-2xl font-bold text-blue-600 dark:text-blue-400"
                >
                  {{ currencyAll.length }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ $t("currencysupported") }}
                </div>
              </div>

              <div v-if="items.position_type == 3"
                class="text-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg"
              >
                <div
                  class="text-2xl font-bold text-green-600 dark:text-green-400"
                >
                  {{ items.whitelist_ip?.length || 0 }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ $t("Current-Whitelist-IPs") }}
                </div>
              </div>

              <div  v-if="items.position_type == 3"
                class="text-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg"
              >
                <div
                  class="text-2xl font-bold text-purple-600 dark:text-purple-400"
                >
                  {{ items.secret_key ? "✓" : "✗" }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ $t("API-Configuration") }}
                </div>
              </div>
            </div>
          </n-card>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- Change Password Modal -->
    <n-modal v-model:show="showModalProfile" preset="dialog" :show-icon="false">
      <template #header>
        <div class="flex items-center gap-2">
          <icon-park-outline-lock class="text-[#f59e0b]" />
          <span>{{ $t("changepassword") }}</span>
        </div>
      </template>

      <n-form
        ref="formRef"
        :model="profileEdit"
        :rules="rules"
        label-placement="top"
      >
        <n-form-item :label="$t('oldpassword')" path="oldpassword">
          <n-input
            v-model:value="profileEdit.oldpassword"
            type="password"
            show-password-on="mousedown"
            :placeholder="$t('oldpassword')"
          />
        </n-form-item>

        <n-form-item :label="$t('newpassword')" path="newpassword">
          <n-input
            v-model:value="profileEdit.newpassword"
            type="password"
            show-password-on="mousedown"
            :placeholder="$t('newpassword')"
          />
        </n-form-item>

        <n-form-item :label="$t('confirmpassword')" path="confirmpassword">
          <n-input
            v-model:value="profileEdit.confirmpassword"
            type="password"
            show-password-on="mousedown"
            :placeholder="$t('confirmpassword')"
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showModalProfile = false">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="savePassword()">
            {{ $t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Callback URL Modal -->
    <n-modal
      v-model:show="showModalCallbackUrl"
      preset="dialog"
      :show-icon="false"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <icon-park-outline-send class="text-[#8b5cf6]" />
          <span>{{ $t("editcallbackurl") }}</span>
        </div>
      </template>

      <n-form>
        <n-form-item path="callbackUrl">
          <n-input
            v-model:value="CallbackUrl"
            :placeholder="$t('callbackurl')"
            clearable
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showModalCallbackUrl = false">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="saveCallback()">
            {{ $t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Whitelist IP Modal -->
    <n-modal
      v-model:show="showModalWhitelistIP"
      preset="dialog"
      :show-icon="false"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <icon-park-outline-shield class="text-[#ef4444]" />
          <span>{{ $t("editwhitelistip") }}</span>
        </div>
      </template>

      <div>
        <!-- Add IP Section -->
        <div>
          <n-form label-placement="top">
            <n-form-item path="whitlistip">
              <n-input-group>
                <n-input
                  v-model:value="WhitelistIP"
                  :placeholder="$t('ipAddressPlaceholder')"
                  @keyup.enter="WhitelistIPAdd()"
                  size="large"
                  clearable
                />
                <n-button
                  type="primary"
                  @click="WhitelistIPAdd()"
                  size="large"
                  :disabled="!WhitelistIP || !WhitelistIP.trim()"
                >
                  <template #icon>
                    <icon-park-outline-plus />
                  </template>
                  {{ $t("add") }}
                </n-button>
              </n-input-group>
            </n-form-item>
          </n-form>

          <!-- IP Format Helper -->
          <div
            class="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400"
          ></div>
        </div>

        <!-- Current IPs Section -->
        <div v-if="listIP.length > 0" class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <icon-park-outline-list class="text-[#10b981]" />

              <span class="font-medium text-gray-700 dark:text-gray-300">
                {{ $t("currentips") }} ({{ listIP.length }})
              </span>
            </div>
            <n-button
              type="error"
              quaternary
              size="small"
              @click="listIP = []"
              v-if="listIP.length > 1"
            >
              <template #icon>
                <icon-park-outline-delete />
              </template>
              {{ $t("clearall") }}
            </n-button>
          </div>

          <div class="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
            <div
              v-for="(ip, index) in listIP"
              :key="index"
              class="flex items-center justify-between p-3 bg-white dark:bg-gray-800/20 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <div class="flex items-center gap-3">
                <n-avatar size="small" :style="{ backgroundColor: '#10b981' }">
                  <icon-park-outline-computer class="text-sm" />
                </n-avatar>
                <div>
                  <n-text code class="text-sm">{{ ip }}</n-text>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ $t("addedip") || "Added IP Address" }}
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-2">
                <n-button
                  type="default"
                  quaternary
                  size="small"
                  @click="copyToClipboard(ip)"
                >
                  <template #icon>
                    <icon-park-outline-copy />
                  </template>
                </n-button>
                <n-button
                  type="error"
                  quaternary
                  size="small"
                  @click="listIP.splice(index, 1)"
                >
                  <template #icon>
                    <icon-park-outline-delete />
                  </template>
                </n-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-8">
          <div class="flex flex-col items-center gap-3">
            <n-avatar size="large" :style="{ backgroundColor: '#f3f4f6' }">
              <icon-park-outline-shield class="text-2xl text-gray-400" />
            </n-avatar>
            <div>
              <p class="text-gray-500 dark:text-gray-400 font-medium">
                {{ $t("noipsadded") || "No IP addresses added yet" }}
              </p>
              <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">
                {{
                  $t("addiphelp") ||
                  "Add IP addresses to restrict access to your account"
                }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <template #action>
        <n-space justify="end">
          <n-button @click="showWhitelistIP(false)">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="saveWhitelist()">
            {{ $t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Secret Key Generated Modal -->
    <n-modal
      v-model:show="showSecretKey"
      preset="dialog"
      :show-icon="false"
    >
      <template #header
        ><icon-park-outline-key /><span class="ml-1">Secret Key</span>
      </template>
      <div class="mt-5">
        <p class="text-center">
          {{ $t("secretkey1") }}
        </p>
        <p class="text-center mb-5">
          {{ $t("secretkey2") }}
        </p>
        <n-input
          v-model:value="SecretKey"
          placeholder=""
          type="text"
          disabled
        />
        <n-flex justify="center" class="mt-5">
          <n-button
            type="primary"
            secondary
            @click="copyToClipboard(SecretKey)"
          >
            <template #icon />
            <icon-park-outline-copy />
            <span class="ml-1"> {{ $t("copy") }}</span>
          </n-button>
        </n-flex>
      </div>
    </n-modal>
  </div>
</template>

<style scoped></style>
