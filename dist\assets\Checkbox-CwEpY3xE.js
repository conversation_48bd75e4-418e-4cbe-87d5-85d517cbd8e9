import{y as se,d as N,q as s,V as j,gU as E,r as F,U,gs as H,a6 as ue,K as P,ga as l,A as b,C as a,fr as be,fs as fe,ft as S,fu as C,g5 as he,fz as ke,g7 as ve,P as ge,fL as me,S as xe,z as V,fv as pe,fA as K,W as Ce,hg as ye,hm as we}from"./index-pY9FjpQW.js";const G=se("n-checkbox-group"),ze={min:Number,max:Number,size:String,value:Array,defaultValue:{type:Array,default:null},disabled:{type:Boolean,default:void 0},"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onChange:[Function,Array]},Ae=N({name:"CheckboxGroup",props:ze,setup(n){const{mergedClsPrefixRef:d}=j(n),m=E(n),{mergedSizeRef:y,mergedDisabledRef:w}=m,x=F(n.defaultValue),T=U(()=>n.value),f=H(T,x),D=U(()=>{var u;return((u=f.value)===null||u===void 0?void 0:u.length)||0}),o=U(()=>Array.isArray(f.value)?new Set(f.value):new Set);function z(u,r){const{nTriggerFormInput:p,nTriggerFormChange:h}=m,{onChange:c,"onUpdate:value":k,onUpdateValue:v}=n;if(Array.isArray(f.value)){const i=Array.from(f.value),M=i.findIndex(B=>B===r);u?~M||(i.push(r),v&&l(v,i,{actionType:"check",value:r}),k&&l(k,i,{actionType:"check",value:r}),p(),h(),x.value=i,c&&l(c,i)):~M&&(i.splice(M,1),v&&l(v,i,{actionType:"uncheck",value:r}),k&&l(k,i,{actionType:"uncheck",value:r}),c&&l(c,i),x.value=i,p(),h())}else u?(v&&l(v,[r],{actionType:"check",value:r}),k&&l(k,[r],{actionType:"check",value:r}),c&&l(c,[r]),x.value=[r],p(),h()):(v&&l(v,[],{actionType:"uncheck",value:r}),k&&l(k,[],{actionType:"uncheck",value:r}),c&&l(c,[]),x.value=[],p(),h())}return ue(G,{checkedCountRef:D,maxRef:P(n,"max"),minRef:P(n,"min"),valueSetRef:o,disabledRef:w,mergedSizeRef:y,toggleCheckbox:z}),{mergedClsPrefix:d}},render(){return s("div",{class:`${this.mergedClsPrefix}-checkbox-group`,role:"group"},this.$slots)}}),Re=()=>s("svg",{viewBox:"0 0 64 64",class:"check-icon"},s("path",{d:"M50.42,16.76L22.34,39.45l-8.1-11.46c-1.12-1.58-3.3-1.96-4.88-0.84c-1.58,1.12-1.95,3.3-0.84,4.88l10.26,14.51  c0.56,0.79,1.42,1.31,2.38,1.45c0.16,0.02,0.32,0.03,0.48,0.03c0.8,0,1.57-0.27,2.2-0.78l30.99-25.03c1.5-1.21,1.74-3.42,0.52-4.92  C54.13,15.78,51.93,15.55,50.42,16.76z"})),Se=()=>s("svg",{viewBox:"0 0 100 100",class:"line-icon"},s("path",{d:"M80.2,55.5H21.4c-2.8,0-5.1-2.5-5.1-5.5l0,0c0-3,2.3-5.5,5.1-5.5h58.7c2.8,0,5.1,2.5,5.1,5.5l0,0C85.2,53.1,82.9,55.5,80.2,55.5z"})),Te=b([a("checkbox",`
 font-size: var(--n-font-size);
 outline: none;
 cursor: pointer;
 display: inline-flex;
 flex-wrap: nowrap;
 align-items: flex-start;
 word-break: break-word;
 line-height: var(--n-size);
 --n-merged-color-table: var(--n-color-table);
 `,[S("show-label","line-height: var(--n-label-line-height);"),b("&:hover",[a("checkbox-box",[C("border","border: var(--n-border-checked);")])]),b("&:focus:not(:active)",[a("checkbox-box",[C("border",`
 border: var(--n-border-focus);
 box-shadow: var(--n-box-shadow-focus);
 `)])]),S("inside-table",[a("checkbox-box",`
 background-color: var(--n-merged-color-table);
 `)]),S("checked",[a("checkbox-box",`
 background-color: var(--n-color-checked);
 `,[a("checkbox-icon",[b(".check-icon",`
 opacity: 1;
 transform: scale(1);
 `)])])]),S("indeterminate",[a("checkbox-box",[a("checkbox-icon",[b(".check-icon",`
 opacity: 0;
 transform: scale(.5);
 `),b(".line-icon",`
 opacity: 1;
 transform: scale(1);
 `)])])]),S("checked, indeterminate",[b("&:focus:not(:active)",[a("checkbox-box",[C("border",`
 border: var(--n-border-checked);
 box-shadow: var(--n-box-shadow-focus);
 `)])]),a("checkbox-box",`
 background-color: var(--n-color-checked);
 border-left: 0;
 border-top: 0;
 `,[C("border",{border:"var(--n-border-checked)"})])]),S("disabled",{cursor:"not-allowed"},[S("checked",[a("checkbox-box",`
 background-color: var(--n-color-disabled-checked);
 `,[C("border",{border:"var(--n-border-disabled-checked)"}),a("checkbox-icon",[b(".check-icon, .line-icon",{fill:"var(--n-check-mark-color-disabled-checked)"})])])]),a("checkbox-box",`
 background-color: var(--n-color-disabled);
 `,[C("border",`
 border: var(--n-border-disabled);
 `),a("checkbox-icon",[b(".check-icon, .line-icon",`
 fill: var(--n-check-mark-color-disabled);
 `)])]),C("label",`
 color: var(--n-text-color-disabled);
 `)]),a("checkbox-box-wrapper",`
 position: relative;
 width: var(--n-size);
 flex-shrink: 0;
 flex-grow: 0;
 user-select: none;
 -webkit-user-select: none;
 `),a("checkbox-box",`
 position: absolute;
 left: 0;
 top: 50%;
 transform: translateY(-50%);
 height: var(--n-size);
 width: var(--n-size);
 display: inline-block;
 box-sizing: border-box;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 transition: background-color 0.3s var(--n-bezier);
 `,[C("border",`
 transition:
 border-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
 border-radius: inherit;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border: var(--n-border);
 `),a("checkbox-icon",`
 display: flex;
 align-items: center;
 justify-content: center;
 position: absolute;
 left: 1px;
 right: 1px;
 top: 1px;
 bottom: 1px;
 `,[b(".check-icon, .line-icon",`
 width: 100%;
 fill: var(--n-check-mark-color);
 opacity: 0;
 transform: scale(0.5);
 transform-origin: center;
 transition:
 fill 0.3s var(--n-bezier),
 transform 0.3s var(--n-bezier),
 opacity 0.3s var(--n-bezier),
 border-color 0.3s var(--n-bezier);
 `),he({left:"1px",top:"1px"})])]),C("label",`
 color: var(--n-text-color);
 transition: color .3s var(--n-bezier);
 user-select: none;
 -webkit-user-select: none;
 padding: var(--n-label-padding);
 font-weight: var(--n-label-font-weight);
 `,[b("&:empty",{display:"none"})])]),be(a("checkbox",`
 --n-merged-color-table: var(--n-color-table-modal);
 `)),fe(a("checkbox",`
 --n-merged-color-table: var(--n-color-table-popover);
 `))]),De=Object.assign(Object.assign({},V.props),{size:String,checked:{type:[Boolean,String,Number],default:void 0},defaultChecked:{type:[Boolean,String,Number],default:!1},value:[String,Number],disabled:{type:Boolean,default:void 0},indeterminate:Boolean,label:String,focusable:{type:Boolean,default:!0},checkedValue:{type:[Boolean,String,Number],default:!0},uncheckedValue:{type:[Boolean,String,Number],default:!1},"onUpdate:checked":[Function,Array],onUpdateChecked:[Function,Array],privateInsideTable:Boolean,onChange:[Function,Array]}),_e=N({name:"Checkbox",props:De,setup(n){const d=xe(G,null),m=F(null),{mergedClsPrefixRef:y,inlineThemeDisabled:w,mergedRtlRef:x}=j(n),T=F(n.defaultChecked),f=P(n,"checked"),D=H(f,T),o=me(()=>{if(d){const e=d.valueSetRef.value;return e&&n.value!==void 0?e.has(n.value):!1}else return D.value===n.checkedValue}),z=E(n,{mergedSize(e){const{size:g}=n;if(g!==void 0)return g;if(d){const{value:t}=d.mergedSizeRef;if(t!==void 0)return t}if(e){const{mergedSize:t}=e;if(t!==void 0)return t.value}return"medium"},mergedDisabled(e){const{disabled:g}=n;if(g!==void 0)return g;if(d){if(d.disabledRef.value)return!0;const{maxRef:{value:t},checkedCountRef:R}=d;if(t!==void 0&&R.value>=t&&!o.value)return!0;const{minRef:{value:A}}=d;if(A!==void 0&&R.value<=A&&o.value)return!0}return e?e.disabled.value:!1}}),{mergedDisabledRef:u,mergedSizeRef:r}=z,p=V("Checkbox","-checkbox",Te,we,n,y);function h(e){if(d&&n.value!==void 0)d.toggleCheckbox(!o.value,n.value);else{const{onChange:g,"onUpdate:checked":t,onUpdateChecked:R}=n,{nTriggerFormInput:A,nTriggerFormChange:I}=z,_=o.value?n.uncheckedValue:n.checkedValue;t&&l(t,_,e),R&&l(R,_,e),g&&l(g,_,e),A(),I(),T.value=_}}function c(e){u.value||h(e)}function k(e){if(!u.value)switch(e.key){case" ":case"Enter":h(e)}}function v(e){switch(e.key){case" ":e.preventDefault()}}const i={focus:()=>{var e;(e=m.value)===null||e===void 0||e.focus()},blur:()=>{var e;(e=m.value)===null||e===void 0||e.blur()}},M=pe("Checkbox",x,y),B=U(()=>{const{value:e}=r,{common:{cubicBezierEaseInOut:g},self:{borderRadius:t,color:R,colorChecked:A,colorDisabled:I,colorTableHeader:_,colorTableHeaderModal:L,colorTableHeaderPopover:O,checkMarkColor:W,checkMarkColorDisabled:q,border:Y,borderFocus:J,borderDisabled:Q,borderChecked:X,boxShadowFocus:Z,textColor:ee,textColorDisabled:ne,checkMarkColorDisabledChecked:oe,colorDisabledChecked:re,borderDisabledChecked:ae,labelPadding:ce,labelLineHeight:le,labelFontWeight:de,[K("fontSize",e)]:ie,[K("size",e)]:te}}=p.value;return{"--n-label-line-height":le,"--n-label-font-weight":de,"--n-size":te,"--n-bezier":g,"--n-border-radius":t,"--n-border":Y,"--n-border-checked":X,"--n-border-focus":J,"--n-border-disabled":Q,"--n-border-disabled-checked":ae,"--n-box-shadow-focus":Z,"--n-color":R,"--n-color-checked":A,"--n-color-table":_,"--n-color-table-modal":L,"--n-color-table-popover":O,"--n-color-disabled":I,"--n-color-disabled-checked":re,"--n-text-color":ee,"--n-text-color-disabled":ne,"--n-check-mark-color":W,"--n-check-mark-color-disabled":q,"--n-check-mark-color-disabled-checked":oe,"--n-font-size":ie,"--n-label-padding":ce}}),$=w?Ce("checkbox",U(()=>r.value[0]),B,n):void 0;return Object.assign(z,i,{rtlEnabled:M,selfRef:m,mergedClsPrefix:y,mergedDisabled:u,renderedChecked:o,mergedTheme:p,labelId:ye(),handleClick:c,handleKeyUp:k,handleKeyDown:v,cssVars:w?void 0:B,themeClass:$?.themeClass,onRender:$?.onRender})},render(){var n;const{$slots:d,renderedChecked:m,mergedDisabled:y,indeterminate:w,privateInsideTable:x,cssVars:T,labelId:f,label:D,mergedClsPrefix:o,focusable:z,handleKeyUp:u,handleKeyDown:r,handleClick:p}=this;(n=this.onRender)===null||n===void 0||n.call(this);const h=ke(d.default,c=>D||c?s("span",{class:`${o}-checkbox__label`,id:f},D||c):null);return s("div",{ref:"selfRef",class:[`${o}-checkbox`,this.themeClass,this.rtlEnabled&&`${o}-checkbox--rtl`,m&&`${o}-checkbox--checked`,y&&`${o}-checkbox--disabled`,w&&`${o}-checkbox--indeterminate`,x&&`${o}-checkbox--inside-table`,h&&`${o}-checkbox--show-label`],tabindex:y||!z?void 0:0,role:"checkbox","aria-checked":w?"mixed":m,"aria-labelledby":f,style:T,onKeyup:u,onKeydown:r,onClick:p,onMousedown:()=>{ge("selectstart",window,c=>{c.preventDefault()},{once:!0})}},s("div",{class:`${o}-checkbox-box-wrapper`}," ",s("div",{class:`${o}-checkbox-box`},s(ve,null,{default:()=>this.indeterminate?s("div",{key:"indeterminate",class:`${o}-checkbox-icon`},Se()):s("div",{key:"check",class:`${o}-checkbox-icon`},Re())}),s("div",{class:`${o}-checkbox-box__border`}))),h)}});export{Ae as N,_e as _};
