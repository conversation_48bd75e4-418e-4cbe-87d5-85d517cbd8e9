<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useI18n } from 'vue-i18n'
import http from '@/service/axios'

const { t } = useI18n()
const message = useMessage()

// Data refs
const orderIntents = ref([])
const loading = ref(false)
const searchQuery = ref('')
const selectedSource = ref('')
const selectedDirection = ref('')
const dateRange = ref<[number, number] | null>(null)
const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// Modal states
const showDetailModal = ref(false)
const selectedOrder = ref(null)
const showCreateModal = ref(false)

// Form for creating new order intent
const createForm = ref({
  userId: '',
  username: '',
  firstName: '',
  lastName: '',
  rawText: '',
  direction: 'buy',
  fiatTHB: 0,
  rateTHBPerUSDT: 0,
  usdtAmount: 0,
  source: 'text',
  sourceMeta: {
    bankName: '',
    refNo: '',
    meslipCode: null,
    meslipDesc: '',
    meslipDateText: ''
  }
})

// Filter options
const sourceOptions = [
  { label: 'All Sources', value: '' },
  { label: 'Text Message', value: 'text' },
  { label: 'Bank Slip', value: 'slip' }
]

const directionOptions = [
  { label: 'All Directions', value: '' },
  { label: 'Buy', value: 'buy' }
]

// Computed
const filteredData = computed(() => {
  let filtered = orderIntents.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.username?.toLowerCase().includes(query) ||
      item.userId?.toLowerCase().includes(query) ||
      item.rawText?.toLowerCase().includes(query) ||
      item.sourceMeta?.refNo?.toLowerCase().includes(query)
    )
  }

  if (selectedSource.value) {
    filtered = filtered.filter(item => item.source === selectedSource.value)
  }

  if (selectedDirection.value) {
    filtered = filtered.filter(item => item.direction === selectedDirection.value)
  }

  return filtered
})

// Table columns
const columns = [
  {
    title: 'Order ID',
    key: '_id',
    width: 120,
    ellipsis: true,
    render: (row: any) => row._id?.slice(-8) || 'N/A'
  },
  {
    title: 'User Info',
    key: 'userInfo',
    width: 200,
    render: (row: any) => ({
      tag: 'div',
      children: [
        {
          tag: 'div',
          props: { class: 'font-medium text-sm' },
          children: row.username || 'Unknown'
        },
        {
          tag: 'div',
          props: { class: 'text-xs text-gray-500' },
          children: `${row.firstName || ''} ${row.lastName || ''}`.trim() || row.userId
        }
      ]
    })
  },
  {
    title: 'Raw Text',
    key: 'rawText',
    width: 120,
    render: (row: any) => ({
      tag: 'n-tag',
      props: { 
        type: 'info',
        size: 'small',
        round: true
      },
      children: row.rawText || 'N/A'
    })
  },
  {
    title: 'Amount (THB)',
    key: 'fiatTHB',
    width: 120,
    render: (row: any) => ({
      tag: 'div',
      props: { class: 'font-mono text-green-600' },
      children: `฿${(row.fiatTHB || 0).toLocaleString()}`
    })
  },
  {
    title: 'USDT Amount',
    key: 'usdtAmount',
    width: 120,
    render: (row: any) => ({
      tag: 'div',
      props: { class: 'font-mono text-blue-600' },
      children: `${(row.usdtAmount || 0).toFixed(6)} USDT`
    })
  },
  {
    title: 'Rate',
    key: 'rateTHBPerUSDT',
    width: 100,
    render: (row: any) => ({
      tag: 'div',
      props: { class: 'font-mono text-sm' },
      children: (row.rateTHBPerUSDT || 0).toFixed(2)
    })
  },
  {
    title: 'Source',
    key: 'source',
    width: 100,
    render: (row: any) => ({
      tag: 'n-tag',
      props: { 
        type: row.source === 'slip' ? 'success' : 'warning',
        size: 'small'
      },
      children: row.source === 'slip' ? 'Bank Slip' : 'Text'
    })
  },
  {
    title: 'Created',
    key: 'ts',
    width: 140,
    render: (row: any) => new Date(row.ts).toLocaleString('th-TH')
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 120,
    render: (row: any) => ({
      tag: 'n-space',
      children: [
        {
          tag: 'n-button',
          props: {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => viewDetail(row)
          },
          children: 'View'
        }
      ]
    })
  }
]

// Methods
const fetchData = async () => {
  loading.value = true
  try {
    const response = await http.get('/api/order-intents', {
      params: {
        page: pagination.value.page,
        limit: pagination.value.pageSize
      }
    })
    orderIntents.value = response.data.data || []
    pagination.value.itemCount = response.data.total || 0
  } catch (error) {
    message.error('Failed to fetch order intents')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const viewDetail = (order: any) => {
  selectedOrder.value = order
  showDetailModal.value = true
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  fetchData()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchData()
}

const createOrderIntent = async () => {
  try {
    await http.post('/api/order-intents', createForm.value)
    message.success('Order intent created successfully')
    showCreateModal.value = false
    fetchData()
    resetCreateForm()
  } catch (error) {
    message.error('Failed to create order intent')
    console.error(error)
  }
}

const resetCreateForm = () => {
  createForm.value = {
    userId: '',
    username: '',
    firstName: '',
    lastName: '',
    rawText: '',
    direction: 'buy',
    fiatTHB: 0,
    rateTHBPerUSDT: 0,
    usdtAmount: 0,
    source: 'text',
    sourceMeta: {
      bankName: '',
      refNo: '',
      meslipCode: null,
      meslipDesc: '',
      meslipDateText: ''
    }
  }
}

const exportData = () => {
  // Export functionality
  message.info('Export feature coming soon')
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <n-space vertical size="large">
    <!-- Header Section -->
    <n-card>
      <n-space justify="space-between" align="center">
        <n-space align="center">
          <n-avatar size="large" color="#f0a020">
            <icon-park-outline-transaction />
          </n-avatar>
          <div>
            <n-h2 style="margin: 0">Order Intent Management</n-h2>
            <n-text depth="3">
              Manage and monitor order intentions from users
            </n-text>
          </div>
        </n-space>
        <n-space>
          <n-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <icon-park-outline-plus />
            </template>
            Create Order
          </n-button>
          <n-button @click="exportData">
            <template #icon>
              <icon-park-outline-download />
            </template>
            Export
          </n-button>
        </n-space>
      </n-space>
    </n-card>

    <!-- Filters Section -->
    <n-card >
      <n-grid cols="1 m:4" :x-gap="16" :y-gap="16" responsive="screen">
        <n-gi>
          <n-input
            v-model:value="searchQuery"
            placeholder="Search by username, user ID, or ref no..."
            clearable
          >
            <template #prefix>
              <icon-park-outline-search />
            </template>
          </n-input>
        </n-gi>
        <n-gi>
          <n-select
            v-model:value="selectedSource"
            :options="sourceOptions"
            placeholder="Filter by source"
            clearable
          />
        </n-gi>
        <n-gi>
          <n-select
            v-model:value="selectedDirection"
            :options="directionOptions"
            placeholder="Filter by direction"
            clearable
          />
        </n-gi>
        <n-gi>
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            placeholder="Select date range"
            clearable
          />
        </n-gi>
      </n-grid>
    </n-card>

    <!-- Statistics Cards -->
    <n-grid cols="1 m:4" :x-gap="16" :y-gap="16" responsive="screen" >
      <n-gi>
        <n-card>
          <n-statistic label="Total Orders" :value="orderIntents.length">
            <template #prefix>
              <icon-park-outline-transaction class="text-blue-500" />
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
      <n-gi>
        <n-card>
          <n-statistic 
            label="Text Orders" 
            :value="orderIntents.filter(o => o.source === 'text').length"
          >
            <template #prefix>
              <icon-park-outline-message class="text-green-500" />
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
      <n-gi>
        <n-card>
          <n-statistic 
            label="Slip Orders" 
            :value="orderIntents.filter(o => o.source === 'slip').length"
          >
            <template #prefix>
              <icon-park-outline-file-text class="text-orange-500" />
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
      <n-gi>
        <n-card>
          <n-statistic 
            label="Total Value (THB)" 
            :value="orderIntents.reduce((sum, o) => sum + (o.fiatTHB || 0), 0)"
            :precision="0"
          >
            <template #prefix>
              <icon-park-outline-dollar class="text-purple-500" />
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
    </n-grid>

    <!-- Data Table -->
    <n-card>
      <template #header>
        <n-space justify="space-between" align="center">
          <span class="font-medium">Order Intents</span>
          <n-button size="small" @click="fetchData" :loading="loading">
            <template #icon>
              <icon-park-outline-refresh />
            </template>
            Refresh
          </n-button>
        </n-space>
      </template>

      <n-data-table
        :columns="columns"
        :data="filteredData"
        :loading="loading"
        :pagination="pagination"
        :on-update:page="handlePageChange"
        :on-update:page-size="handlePageSizeChange"
        striped
        size="small"
      />
    </n-card>

    <!-- Detail Modal -->
    <n-modal v-model:show="showDetailModal" preset="card" style="width: 800px">
      <template #header>
        <n-space align="center">
          <icon-park-outline-info />
          <span>Order Intent Details</span>
        </n-space>
      </template>

      <div v-if="selectedOrder">
        <n-grid cols="1 m:2" :x-gap="16" :y-gap="16" responsive="screen">
          <!-- Basic Info -->
          <n-gi>
            <n-card title="Basic Information" size="small">
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="Order ID">
                  {{ selectedOrder._id }}
                </n-descriptions-item>
                <n-descriptions-item label="User ID">
                  {{ selectedOrder.userId }}
                </n-descriptions-item>
                <n-descriptions-item label="Username">
                  {{ selectedOrder.username }}
                </n-descriptions-item>
                <n-descriptions-item label="Full Name">
                  {{ `${selectedOrder.firstName || ''} ${selectedOrder.lastName || ''}`.trim() }}
                </n-descriptions-item>
                <n-descriptions-item label="Raw Text">
                  <n-tag type="info">{{ selectedOrder.rawText }}</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="Direction">
                  <n-tag type="success">{{ selectedOrder.direction }}</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>
          </n-gi>

          <!-- Financial Info -->
          <n-gi>
            <n-card title="Financial Information" size="small">
              <n-descriptions :column="1" size="small">
                                <n-descriptions-item label="THB Amount">
                  <n-text class="font-mono text-green-600 text-lg">
                    ฿{{ (selectedOrder.fiatTHB || 0).toLocaleString() }}
                  </n-text>
                </n-descriptions-item>
                <n-descriptions-item label="USDT Amount">
                  <n-text class="font-mono text-blue-600 text-lg">
                    {{ (selectedOrder.usdtAmount || 0).toFixed(6) }} USDT
                  </n-text>
                </n-descriptions-item>
                <n-descriptions-item label="Exchange Rate">
                  <n-text class="font-mono">
                    {{ (selectedOrder.rateTHBPerUSDT || 0).toFixed(2) }} THB/USDT
                  </n-text>
                </n-descriptions-item>
                <n-descriptions-item label="Rate Source">
                  <n-tag size="small">{{ selectedOrder.rateSource || 'N/A' }}</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="Rate Fetched At">
                  {{ selectedOrder.rateFetchedAt ? new Date(selectedOrder.rateFetchedAt).toLocaleString('th-TH') : 'N/A' }}
                </n-descriptions-item>
              </n-descriptions>
            </n-card>
          </n-gi>

          <!-- Source Info -->
          <n-gi>
            <n-card title="Source Information" size="small">
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="Source Type">
                  <n-tag :type="selectedOrder.source === 'slip' ? 'success' : 'warning'">
                    {{ selectedOrder.source === 'slip' ? 'Bank Slip' : 'Text Message' }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item v-if="selectedOrder.sourceMeta?.bankName" label="Bank Name">
                  {{ selectedOrder.sourceMeta.bankName }}
                </n-descriptions-item>
                <n-descriptions-item v-if="selectedOrder.sourceMeta?.refNo" label="Reference No">
                  <n-text class="font-mono">{{ selectedOrder.sourceMeta.refNo }}</n-text>
                </n-descriptions-item>
                <n-descriptions-item v-if="selectedOrder.sourceMeta?.meslipCode" label="Meslip Code">
                  <n-tag type="info">{{ selectedOrder.sourceMeta.meslipCode }}</n-tag>
                </n-descriptions-item>
                <n-descriptions-item v-if="selectedOrder.sourceMeta?.meslipDesc" label="Meslip Description">
                  {{ selectedOrder.sourceMeta.meslipDesc }}
                </n-descriptions-item>
                <n-descriptions-item v-if="selectedOrder.sourceMeta?.meslipDateText" label="Slip Date">
                  {{ selectedOrder.sourceMeta.meslipDateText }}
                </n-descriptions-item>
              </n-descriptions>
            </n-card>
          </n-gi>

          <!-- Timestamps -->
          <n-gi>
            <n-card title="Timestamps" size="small">
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="Created At">
                  {{ new Date(selectedOrder.ts).toLocaleString('th-TH') }}
                </n-descriptions-item>
                <n-descriptions-item label="Chat ID">
                  <n-text class="font-mono">{{ selectedOrder.chatId || 'N/A' }}</n-text>
                </n-descriptions-item>
                <n-descriptions-item label="Message ID">
                  {{ selectedOrder.messageId || 'N/A' }}
                </n-descriptions-item>
              </n-descriptions>
            </n-card>
          </n-gi>
        </n-grid>
      </div>

      <template #action>
        <n-space justify="end">
          <n-button @click="showDetailModal = false">Close</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Create Order Modal -->
    <n-modal v-model:show="showCreateModal" preset="card" style="width: 600px">
      <template #header>
        <n-space align="center">
          <icon-park-outline-plus />
          <span>Create New Order Intent</span>
        </n-space>
      </template>

      <n-form :model="createForm" label-placement="top">
        <n-grid cols="1 m:2" :x-gap="16" :y-gap="16" responsive="screen">
          <n-gi>
            <n-form-item label="User ID" required>
              <n-input v-model:value="createForm.userId" placeholder="Enter user ID" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="Username">
              <n-input v-model:value="createForm.username" placeholder="Enter username" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="First Name">
              <n-input v-model:value="createForm.firstName" placeholder="Enter first name" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="Last Name">
              <n-input v-model:value="createForm.lastName" placeholder="Enter last name" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="Raw Text" required>
              <n-input v-model:value="createForm.rawText" placeholder="e.g., +1000" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="Direction">
              <n-select v-model:value="createForm.direction" :options="[{ label: 'Buy', value: 'buy' }]" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="THB Amount" required>
              <n-input-number v-model:value="createForm.fiatTHB" :min="0" placeholder="Enter THB amount" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="Exchange Rate" required>
              <n-input-number v-model:value="createForm.rateTHBPerUSDT" :min="0" :precision="2" placeholder="THB per USDT" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="USDT Amount" required>
              <n-input-number v-model:value="createForm.usdtAmount" :min="0" :precision="6" placeholder="USDT amount" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="Source Type">
              <n-select 
                v-model:value="createForm.source" 
                :options="[
                  { label: 'Text Message', value: 'text' },
                  { label: 'Bank Slip', value: 'slip' }
                ]" 
              />
            </n-form-item>
          </n-gi>
        </n-grid>

        <!-- Source Meta Fields (only show if source is 'slip') -->
        <n-card v-if="createForm.source === 'slip'" title="Bank Slip Information" size="small" class="mt-4">
          <n-grid cols="1 m:2" :x-gap="16" :y-gap="16" responsive="screen">
            <n-gi>
              <n-form-item label="Bank Name">
                <n-input v-model:value="createForm.sourceMeta.bankName" placeholder="Enter bank name" />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="Reference Number">
                <n-input v-model:value="createForm.sourceMeta.refNo" placeholder="Enter reference number" />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="Meslip Code">
                <n-input-number v-model:value="createForm.sourceMeta.meslipCode" placeholder="Enter meslip code" />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="Meslip Description">
                <n-input v-model:value="createForm.sourceMeta.meslipDesc" placeholder="Enter description" />
              </n-form-item>
            </n-gi>
            <n-gi span="2">
              <n-form-item label="Slip Date">
                <n-input v-model:value="createForm.sourceMeta.meslipDateText" placeholder="Enter slip date" />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showCreateModal = false">Cancel</n-button>
          <n-button type="primary" @click="createOrderIntent">Create Order</n-button>
        </n-space>
      </template>
    </n-modal>
  </n-space>
</template>