import{d as p,l as c,m as e,o as t,n as o,b as i,w as m,g as _,t as l,f as u,B as d}from"./index-pY9FjpQW.js";const y="/assets/error-403-VPEqmg7m.svg",f="/assets/error-404-DLMSXL2R.svg",g="/assets/error-500-C1R4JvdT.svg",k={class:"flex-col-center h-full"},h={key:0,src:y,alt:"",class:"w-1/3"},v={key:1,src:f,alt:"",class:"w-1/3"},w={key:2,src:g,alt:"",class:"w-1/3"},b=p({__name:"ErrorTip",props:{type:{}},setup(C){const a=c();return(s,r)=>{const n=d;return t(),e("div",k,[s.type==="403"?(t(),e("img",h)):o("",!0),s.type==="404"?(t(),e("img",v)):o("",!0),s.type==="500"?(t(),e("img",w)):o("",!0),i(n,{type:"primary",onClick:r[0]||(r[0]=B=>u(a).push("/my-profile"))},{default:m(()=>[_(l(s.$t("app.backHome")),1)]),_:1})])}}});export{b as _};
