<template>
  <div>
    <n-tabs :type="isMobile ? 'line' : 'segment'" animated>
      <n-tab-pane :name="1" :tab="$t('baccarat.settings.title')">
        <SettingBet />
      </n-tab-pane>
      <n-tab-pane name="2" :tab="$t('ตั้งค่าเปอร์เซ็นต์รางวัลเดิมพัน')">
        <SettingGame />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="tsx">
import SettingBet from "./components/setting-bet.vue";
import SettingGame from "./components/setting-game.vue";

const isMobile = useMediaQuery("(max-width: 767px)");
</script>
