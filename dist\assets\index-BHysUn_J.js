import{_ as R}from"./add-one-BP9Rv5dm.js";import{d as q,fB as K,fS as L,ad as W,ac as X,r as m,a3 as Y,fQ as j,U as Z,c as I,o as z,w as n,b as l,af as M,ab as ee,a as d,n as G,f as s,fT as te,i as _,hH as le,g as f,t as p,ah as ne,fD as ae,m as se,B as oe}from"./index-pY9FjpQW.js";import{_ as ue}from"./FormItem-B4P0FvHJ.js";import{_ as ie}from"./Slider-DWNZsvrI.js";import{a as de}from"./headers-CSI__REg.js";const re={class:"flex gap-3"},me={key:0},_e={class:"sm:flex -mb-5 items-center justify-center gap-3"},pe={class:"flex justify-between items-center mb-2"},ve={class:"text-sm font-medium"},ce={class:"flex justify-between items-center mb-2"},fe={class:"text-sm font-medium"},be={class:"flex justify-between items-center mb-2"},ge={class:"text-sm font-medium"},Ue=q({__name:"index",setup(ye){const{userData:v}=K();L({condition_1:"",condition_2:"",condition_3:"",condition_4:""});const w=W(),{t:T}=X(),b=m(!1),H=()=>{if(Number(o.value)+Number(u.value)+Number(x.value)+Number(h.value)+Number(k.value)!=100)return w.error(T("validlv2tolv6"));const e={lv1:i.value,lv2:o.value,lv3:u.value,lv4:x.value,lv5:h.value,lv6:k.value,status:b.value};j.post("PG/Management/UpdateSetting",e).then(g=>{g.data.status?(w.success(g.data.mes),P()):w.error(g.data.mes)})},r=()=>!((v?.position_type==4&&v?.upline_position_type==3||v?.position_type==4&&v?.upline_position_type==2||v?.position_type==4&&v?.upline_position_type==1)&&JSON.parse(v?.permissions).setting!==2),i=m(0),o=m(0),u=m(0),x=m(0),h=m(0),k=m(0),A=t=>{b.value=t},c=m(null);Y(()=>{P(),E()});const P=async()=>{try{const{data:t}=await j.get("PG/Management/Setting");t&&(i.value=t.level1,o.value=t.level2,u.value=t.level3,x.value=t.level4,h.value=t.level5,k.value=t.level6,b.value=t.status)}catch(t){console.log(t)}},U=(t,e)=>{if(Number(t)>100&&e==1)return i.value=100;if(Number(t)>100&&e==2)return o.value=100;if(Number(t)>100&&e==3)return u.value=100;if(Number(t)>100&&e==4)return x.value=100;if(Number(t)>100&&e==5)return h.value=100;if(Number(t)>100&&e==6)return k.value=100},E=async()=>{try{const t=await j.get("PG/Profile");t.data&&(c.value=t.data)}catch(t){console.log(t)}},C=Z(()=>c.value&&c.value.credit_thb!==void 0?Number(c.value.credit_thb).toFixed(2):"0.00");return(t,e)=>{const g=de,F=R,J=oe,V=ae,y=te,D=ue,O=le,N=ee,$=ne,S=ie,B=M,Q=M;return z(),I(V,null,{default:n(()=>[l(Q,{vertical:"",class:"flex-1"},{default:n(()=>[l(N,{class:"flex-1"},{header:n(()=>[l(V,{justify:"space-between"},{default:n(()=>[d("div",re,[l(g,null,{default:n(()=>[f(p(t.$t("setting")),1)]),_:1})]),r()?(z(),se("div",me,[l(J,{type:"success",onClick:H},{icon:n(()=>[l(F)]),default:n(()=>[f(" "+p(t.$t("save")),1)]),_:1})])):G("",!0)]),_:1})]),default:n(()=>[d("div",null,[s(c)?(z(),I(N,{key:0,class:"mb-3 -mt-5"},{default:n(()=>[d("div",_e,[l(D,{label:t.$t("name"),path:"condition_2","label-placement":"left"},{default:n(()=>[l(y,{value:s(c).username,"onUpdate:value":e[0]||(e[0]=a=>s(c).username=a),disabled:"",placeholder:""},null,8,["value"])]),_:1},8,["label"]),l(D,{label:t.$t("credit"),path:"condition_3","label-placement":"left"},{default:n(()=>[l(y,{value:s(C),"onUpdate:value":e[1]||(e[1]=a=>_(C)?C.value=a:null),disabled:"",placeholder:""},null,8,["value"])]),_:1},8,["label"]),l(O,{class:"mb-6 text-sm flex-center",value:s(b),"onUpdate:value":[e[2]||(e[2]=a=>_(b)?b.value=a:null),A],disabled:!r()},{checked:n(()=>e[12]||(e[12]=[f("เปิดใช้งาน")])),unchecked:n(()=>e[13]||(e[13]=[f("ปิดใช้งาน")])),_:1},8,["value","disabled"])])]),_:1})):G("",!0),l(N,{class:"mb-3"},{default:n(()=>[d("div",pe,[d("label",ve,p(t.$t("settinglv1")),1),l($,{size:"small",type:"primary"},{default:n(()=>[f(p(s(i))+"%",1)]),_:1})]),l(B,{vertical:"",size:"small",class:"w-full"},{default:n(()=>[l(S,{value:s(i),"onUpdate:value":e[3]||(e[3]=a=>_(i)?i.value=a:null),min:0,max:100,step:1,disabled:!r(),tooltip:!0},null,8,["value","disabled"]),l(y,{value:s(i),"onUpdate:value":e[4]||(e[4]=a=>_(i)?i.value=a:null),type:"number",placeholder:"",disabled:!r(),onInput:e[5]||(e[5]=a=>U(s(i),1))},null,8,["value","disabled"])]),_:1})]),_:1}),l(N,{class:"mb-3"},{default:n(()=>[d("div",ce,[d("label",fe,p(t.$t("settinglv2")),1),l($,{size:"small",type:"primary"},{default:n(()=>[f(p(s(o))+"%",1)]),_:1})]),l(B,{vertical:"",size:"small",class:"w-full"},{default:n(()=>[l(S,{value:s(o),"onUpdate:value":e[6]||(e[6]=a=>_(o)?o.value=a:null),min:0,max:100,step:1,disabled:!r(),tooltip:!0},null,8,["value","disabled"]),l(y,{value:s(o),"onUpdate:value":e[7]||(e[7]=a=>_(o)?o.value=a:null),type:"number",placeholder:"",disabled:!r(),onInput:e[8]||(e[8]=a=>U(s(o),2))},null,8,["value","disabled"])]),_:1})]),_:1}),l(N,{class:"mb-3"},{default:n(()=>[d("div",be,[d("label",ge,p(t.$t("settinglv3")),1),l($,{size:"small",type:"primary"},{default:n(()=>[f(p(s(u))+"%",1)]),_:1})]),l(B,{vertical:"",size:"small",class:"w-full"},{default:n(()=>[l(S,{value:s(u),"onUpdate:value":e[9]||(e[9]=a=>_(u)?u.value=a:null),min:0,max:100,step:1,disabled:!r(),tooltip:!0},null,8,["value","disabled"]),l(y,{value:s(u),"onUpdate:value":e[10]||(e[10]=a=>_(u)?u.value=a:null),type:"number",placeholder:"",disabled:!r(),onInput:e[11]||(e[11]=a=>U(s(u),3))},null,8,["value","disabled"])]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1})}}});export{Ue as default};
