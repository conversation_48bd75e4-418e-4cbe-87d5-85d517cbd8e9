import{_ as ze,a as Ue}from"./Pagination.vue_vue_type_script_setup_true_lang-tK6i-N0U.js";import{u as se,_ as Re}from"./download-Bcu0jZCr.js";import{a7 as _e,m as W,o as I,a as f,d as Se,ac as He,r as u,gg as de,gh as re,O as Pe,U as ie,b as a,B as h,g as p,ah as Fe,ag as me,gd as qe,ad as Ge,fQ as P,a3 as Ee,c as F,n as ue,w as r,ab as Ve,f as l,fT as Je,gi as Qe,t as L,gc as Ke,gj as Xe,fD as Ze,i as U,af as ge,G as ea,a8 as aa,fR as ta,q as v}from"./index-pY9FjpQW.js";import{_ as oa}from"./preview-open-D9wsvucx.js";import{m as g}from"./index-DZ56Tu_n.js";import{t as ce,d as pe,_ as na}from"./DatePicker-HFFCQBdF.js";import{_ as la}from"./Alert-8Dqy05ab.js";import{_ as sa}from"./Form-_sVFK3VR.js";import{_ as da}from"./FormItem-B4P0FvHJ.js";import{_ as ra,a as ia}from"./Tabs-BOE8tUJg.js";import"./Checkbox-CwEpY3xE.js";import"./RadioGroup-DnXU-fFU.js";import"./download-C2161hUv.js";import"./toNumber-C1SyHx2r.js";const ma={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function ua(q,o){return I(),W("svg",ma,o[0]||(o[0]=[f("path",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4",d:"m20 12l12 12l-12 12z"},null,-1)]))}const ga=_e({name:"icon-park-outline-right-one",render:ua}),ca={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function pa(q,o){return I(),W("svg",ca,o[0]||(o[0]=[f("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[f("path",{d:"M43 5L29.7 43l-7.6-17.1L5 18.3z"}),f("path",{"stroke-linecap":"round",d:"M43 5L22.1 25.9"})],-1)]))}const _a=_e({name:"icon-park-outline-send",render:pa}),va=[{gameCode:"mahjong-ways",gameId:65},{gameCode:"treasures-aztec",gameId:87},{gameCode:"lucky-neko",gameId:89},{gameCode:"diaochan",gameId:1},{gameCode:"mahjong-ways2",gameId:74},{gameCode:"wild-bounty-sd",gameId:135},{gameCode:"ways-of-qilin",gameId:106},{gameCode:"wild-bandito",gameId:104},{gameCode:"fortune-ox",gameId:98},{gameCode:"fortune-rabbit",gameId:1543462},{gameCode:"cai-shen-wins",gameId:71},{gameCode:"asgardian-rs",gameId:1340277},{gameCode:"queen-bounty",gameId:84},{gameCode:"egypts-book-mystery",gameId:73},{gameCode:"gladi-glory",gameId:1572362},{gameCode:"jurassic-kdm",gameId:110},{gameCode:"ganesha-fortune",gameId:75},{gameCode:"dragon-hatch",gameId:57},{gameCode:"cocktail-nite",gameId:117},{gameCode:"ninja-raccoon",gameId:1529867},{gameCode:"fortune-tiger",gameId:126},{gameCode:"midas-fortune",gameId:1402846},{gameCode:"wild-heist-co",gameId:1568554},{gameCode:"shaolin-soccer",gameId:67},{gameCode:"rooster-rbl",gameId:123},{gameCode:"thai-river",gameId:92},{gameCode:"safari-wilds",gameId:1594259},{gameCode:"forge-wealth",gameId:1555350},{gameCode:"spr-golf-drive",gameId:1513328},{gameCode:"ult-striker",gameId:1489936},{gameCode:"queen-banquet",gameId:120},{gameCode:"fortune-mouse",gameId:68},{gameCode:"double-fortune",gameId:48},{gameCode:"hawaiian-tiki",gameId:1381200},{gameCode:"crypto-gold",gameId:103},{gameCode:"oriental-pros",gameId:112},{gameCode:"dreams-of-macau",gameId:79},{gameCode:"myst-spirits",gameId:1432733},{gameCode:"genies-wishes",gameId:85},{gameCode:"spirit-wonder",gameId:119},{gameCode:"fruity-candy",gameId:1397455},{gameCode:"phoenix-rises",gameId:82},{gameCode:"btrfly-blossom",gameId:125},{gameCode:"sct-cleopatra",gameId:90},{gameCode:"wild-fireworks",gameId:83},{gameCode:"legend-perseus",gameId:128},{gameCode:"prosper-ftree",gameId:1312883},{gameCode:"diner-delights",gameId:1372643},{gameCode:"lgd-monkey-kg",gameId:107},{gameCode:"battleground",gameId:124},{gameCode:"bakery-bonanza",gameId:1418544},{gameCode:"gdn-ice-fire",gameId:91},{gameCode:"heist-stakes",gameId:105},{gameCode:"lucky-piggy",gameId:130},{gameCode:"candy-burst",gameId:70},{gameCode:"dest-sun-moon",gameId:121},{gameCode:"speed-winner",gameId:127},{gameCode:"the-great-icescape",gameId:53},{gameCode:"mafia-mayhem",gameId:1580541},{gameCode:"tsar-treasures",gameId:1655268},{gameCode:"cruise-royale",gameId:1473388},{gameCode:"werewolf-hunt",gameId:1615454},{gameCode:"captains-bounty",gameId:54},{gameCode:"songkran-spl",gameId:1448762},{gameCode:"opera-dynasty",gameId:93},{gameCode:"circus-delight",gameId:80},{gameCode:"bikini-paradise",gameId:69},{gameCode:"majestic-ts",gameId:95},{gameCode:"dragon-hatch2",gameId:1451122},{gameCode:"buffalo-win",gameId:108},{gameCode:"hotpot",gameId:28},{gameCode:"baccarat-deluxe",gameId:31},{gameCode:"symbols-of-egypt",gameId:41},{gameCode:"fortune-gods",gameId:3},{gameCode:"gem-saviour-sword",gameId:38},{gameCode:"rise-of-apollo",gameId:101},{gameCode:"win-win-fpc",gameId:129},{gameCode:"sprmkt-spree",gameId:115},{gameCode:"crypt-fortune",gameId:113},{gameCode:"lucky-clover",gameId:1601012},{gameCode:"garuda-gems",gameId:122},{gameCode:"reel-love",gameId:20},{gameCode:"galactic-gems",gameId:86},{gameCode:"jack-frosts",gameId:97},{gameCode:"jungle-delight",gameId:40},{gameCode:"wild-coaster",gameId:132},{gameCode:"rave-party-fvr",gameId:1420892},{gameCode:"mask-carnival",gameId:118},{gameCode:"jewels-prosper",gameId:88},{gameCode:"totem-wonders",gameId:1338274},{gameCode:"ninja-vs-samurai",gameId:59},{gameCode:"muay-thai-champion",gameId:64},{gameCode:"alchemy-gold",gameId:1368367},{gameCode:"vampires-charm",gameId:58},{gameCode:"flirting-scholar",gameId:61},{gameCode:"emoji-riches",gameId:114},{gameCode:"bali-vacation",gameId:94},{gameCode:"mermaid-riches",gameId:102},{gameCode:"piggy-gold",gameId:39},{gameCode:"candy-bonanza",gameId:100},{gameCode:"win-win-won",gameId:24},{gameCode:"gem-saviour-conquest",gameId:62},{gameCode:"fortune-dragon",gameId:1695365},{gameCode:"leprechaun-riches",gameId:60},{gameCode:"medusa2",gameId:6},{gameCode:"emperors-favour",gameId:44},{gameCode:"ganesha-gold",gameId:42},{gameCode:"legend-of-hou-yi",gameId:34},{gameCode:"mr-hallow-win",gameId:35},{gameCode:"santas-gift-rush",gameId:37},{gameCode:"journey-to-the-wealth",gameId:50},{gameCode:"hood-wolf",gameId:18},{gameCode:"plushie-frenzy",gameId:25},{gameCode:"fortune-tree",gameId:26},{gameCode:"medusa",gameId:7},{gameCode:"prosperity-lion",gameId:36},{gameCode:"dragon-tiger-luck",gameId:63},{gameCode:"gemstones-gold",gameId:1671262},{gameCode:"dragon-legend",gameId:29},{gameCode:"hip-hop-panda",gameId:33},{gameCode:"cash-mania",gameId:1682240},{gameCode:"wild-ape-3258",gameId:1508783},{gameCode:"pinata-wins",gameId:1492288},{gameCode:"mystic-potions",gameId:1717688},{gameCode:"anubis-wrath",gameId:1623475},{gameCode:"zombie-outbrk",gameId:1635221},{gameCode:"shark-hunter",gameId:1648578},{gameCode:"yakuza-honor",gameId:1760238},{gameCode:"wings-iguazu",gameId:1747549},{gameCode:"three-cz-pigs",gameId:1727711},{gameCode:"oishi-delights",gameId:1815268},{gameCode:"museum-mystery",gameId:1755623},{gameCode:"rio-fantasia",gameId:1786529},{gameCode:"choc-deluxe",gameId:1666445},{gameCode:"geisha-revenge",gameId:1702123},{gameCode:"fortune-snake",gameId:1879752},{gameCode:"incan-wonders",gameId:1850016},{gameCode:"mr-treas-fort",gameId:1799745},{gameCode:"graffiti-rush",gameId:1804577},{gameCode:"doomsday-rampg",gameId:1827457},{gameCode:"knockout-rich",gameId:1881268},{gameCode:"diner-frenzy",gameId:1935269},{gameCode:"jack-giant-hunt",gameId:1834850}],ya={class:"sm:flex flex-wrap gap-3"},fa={class:"my-1 sm:w-auto"},Ca={class:"my-1 sm:w-auto"},ba={class:"ml-auto sm:flex items-center gap-3"},ha={class:"my-1 sm: w-auto"},Ia={class:"my-1 sm: w-auto"},ka={class:"mb-5"},wa={class:"flex flex-wrap items-center gap-2"},Da=["onClick"],xa={key:0,class:"ml-2"},Ta={class:"flex-center gap-4 my-3"},La={class:"font-bold text-[1rem]"},$a=["src"],qa=Se({__name:"index",setup(q){const{t:o,locale:R}=He(),A=u(R.value==="thTH"?ce:de),B=u(R.value==="thTH"?pe:re);Pe(()=>R.value,e=>{e==="thTH"?(B.value=pe,A.value=ce):e==="enUS"&&(B.value=re,A.value=de)});const N=u(!1),G=u(""),{bool:ve,setTrue:ye,setFalse:fe}=se(!1);se(!1);const C=JSON.parse(localStorage.getItem("userData")),M=u(null),E={condition_1:"",condition_2:[g().startOf("day").toDate(),g().endOf("day").toDate()],condition_3:""},i=u({...E}),V=u(),Ce=ie(()=>[{title:o("no."),align:"center",key:"index",render:(e,t)=>O.value*(b.value-1)+t+1},{title:o("agent"),align:"center",key:"loginName",render:e=>a(h,{tertiary:!0,round:!0,type:e.position_type===1?"warning":e.position_type===2||e.position_type===4?"success":"info",onClick:()=>Te(e),style:{cursor:"pointer"}},{default:()=>[e.loginName]})},{title:o("position"),align:"center",key:"position_type",render:e=>a(h,{size:"small",class:"cursor-default",ghost:!0,type:e.position_type===1?"warning":e.position_type===2||e.position_type===4?"success":"info"},{default:()=>[e.position_type===1?o("reseller"):e.position_type===2||e.position_type===4?o("agent"):o("member")]})},{title:o("roundamount"),align:"center",key:"noTicket",render:e=>s(e.noTicket)},{title:o("betamount"),align:"center",key:"betAmount",render:e=>s(e.betAmount)},{title:o("won"),align:"center",key:"payoutAmount",render:e=>s(e.payoutAmount)},{title:o("winloss100"),align:"center",key:"memberWL",render:e=>{const t={color:e.memberWL100<0?"#d03050":e.memberWL100>0?"#18a058":""};return a("div",{style:t},[s(e.memberWL100)])}},{title:o("member"),title(e){return a("div",{class:"bg-[#2080f0]/50 rounded-lg text-white"},[o("member")])},align:"center",key:"member",children:[{title:o("winloss"),align:"center",key:"member-win-loss",render:(e,t)=>{const n={color:e.memberWL<0?"#d03050":e.memberWL>0?"#18a058":""};return a("div",{style:n},[s(e.memberWL)])}},{title:o("comm"),align:"center",key:"member-comm",render:(e,t)=>a("div",null,[p("0.00")])},{title:o("total"),align:"center",key:"member-total",render:(e,t)=>{const n={color:e.memberTotal<0?"#d03050":e.memberTotal>0?"#18a058":""};return a("div",{style:n},[s(e.memberTotal)])}}]},{title(e){return C.value.position_type==1||C.value.position_type==4&&C.value.upline_position_type==1?a("div",{class:["rounded-lg text-white",c.value==0?"bg-[#d03050]/50":c.value==1?"bg-[#f0a020]/50":(c.value==2,"bg-[#18a058]/50")]},[c.value==0?_.value[0].name:$.value]):C.value.position_type==2||C.value.position_type==4&&C.value.upline_position_type==3?a("div",{class:["rounded-lg text-white",c.value==0||c.value==1?"bg-[#f0a020]/50":(c.value==2,"bg-[#18a058]/50")]},[c.value==0?_.value[0].name:$.value]):a("div",{class:["rounded-lg text-white",c.value==1?"bg-[#f0a020]/50":(c.value==2,"bg-[#18a058]/50")]},[c.value==0?_.value[0].name:$.value])},align:"center",key:"Company",children:[{title:o("winloss"),align:"center",key:"company-win-loss",render:(e,t)=>{const n={color:e.companyWL<0?"#d03050":e.companyWL>0?"#18a058":""};return a("div",{style:n},[s(e.companyWL)])}},{title:o("comm"),align:"center",key:"company-comm",render:(e,t)=>a("div",null,[p("0.00")])},{title:o("total"),align:"center",key:"company-total",render:(e,t)=>{const n={color:e.companyTotal<0?"#d03050":e.companyTotal>0?"#18a058":""};return a("div",{style:n},[s(e.companyTotal)])}}]},{title:o("provider"),title(e){return a("div",{class:"bg-[#000000]/50 rounded-lg text-white"},[o("provider")])},align:"center",key:"provider",children:[{title:o("winloss"),align:"center",key:"provider-win-loss",render:(e,t)=>{const n={color:e.providerWL<0?"#d03050":e.providerWL>0?"#18a058":""};return a("div",{style:n},[s(e.providerWL)])}},{title:o("comm"),align:"center",key:"provider-comm",render:(e,t)=>a("div",null,[p("0.00")])},{title:o("total"),align:"center",key:"provider-total",render:(e,t)=>{const n={color:e.providerTotal<0?"#d03050":e.providerTotal>0?"#18a058":""};return a("div",{style:n},[s(e.providerTotal)])}}]}]);o("no."),o("player"),o("roundamount"),o("betamount"),o("won"),o("winloss100");const be=ie(()=>[{title:o("no."),align:"center",key:"index",render:(e,t)=>O.value*(b.value-1)+t+1},{title:o("information"),align:"center",key:"information",render:e=>a("div",{class:"text-start"},[a("div",null,[a("span",{class:"font-bold"},[o("gameCode")]),p(": "),e.gameCode]),a("div",null,[a("span",{class:"font-bold"},[o("roundid")]),p(": "),e.roundId]),a("div",null,[a("div",{class:"flex"},[a("span",{class:"font-bold"},[o("currency")]),p(":"),a("img",{class:"mx-2 rounded-sm",src:e.currency==="THB"?"/images/country/th.webp":e.currency==="USD"?"/images/country/us.webp":"/images/country/kr.webp",alt:"Language Icon",width:"30",height:"10"},null),e.currency])]),a("div",null,[a("span",{class:"font-bold"},[o("betdate")]),p(":")," ",g(e.created_at).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss")])])},{title:o("creditbefore"),align:"center",key:"before_balance",render:e=>s(e.before_balance)},{title:o("creditafter"),align:"center",key:"after_balance",render:e=>s(e.after_balance)},{title:o("amount"),align:"center",key:"betAmount",render:e=>s(e.betAmount)},{title:o("member"),title(e){return a("div",{class:"bg-[#2080f0]/50 rounded-lg text-white"},[o("member")])},align:"center",key:"gameCode",children:[{title:o("winloss"),align:"center",key:"member_wl",render:(e,t)=>{const n={color:e.member_wl<0?"#d03050":e.member_wl>0?"#18a058":""};return a("div",{class:"mx-10"},[a("p",{class:"text-end",style:n},[s(e.member_wl)]),a("div",{class:"flex justify-between"},[p("pt"),a("p",{class:""},[p("100%")])])])}}]},{title:o("agent"),title(e){return a("div",{class:"bg-[#18a058]/50 rounded-lg text-white"},[_.value[_.value.length-2]?.name])},align:"center",key:"",children:[{title:o("winloss"),align:"center",key:"agent_wl",render:(e,t)=>{const n=s(-(e.member_wl*(e.ag_holdpercent+e.reseller_ourpercent)/100)),y={color:n<0?"#d03050":n>0?"#18a058":""};return a("div",{class:"mx-10"},[a("p",{class:"text-end",style:y},[n]),a("div",{class:"flex justify-between"},[p("pt"),a("p",{class:""},[e.ag_holdpercent+e.reseller_ourpercent,p("%")])])])}}]},{title:o("provider"),title(e){return a("div",{class:"bg-[#000000]/50 rounded-lg text-white"},[o("provider")])},align:"center",key:"gameCode",children:[{title:o("winloss"),align:"center",key:"provider_wl",render:(e,t)=>{const n=s(-(e.member_wl*e.ag_ourpercent/100)),y={color:n<0?"#d03050":n>0?"#18a058":""};return a("div",{class:"mx-10"},[a("p",{class:"text-end",style:y},[n]),a("div",{class:"flex justify-between"},[p("pt"),a("p",{class:""},[e.ag_ourpercent,p("%")])])])}}]},{title:o("result"),align:"center",key:"status_result",render:e=>a("div",null,[a(h,{type:e.member_wl==0?"warning":e.member_wl>0?"success":"error",size:"small",class:"cursor-default text-white"},{default:()=>[e.member_wl>0?o("win"):e.member_wl<0?o("lose"):o("draw")]})])},{title:o("detail"),align:"center",key:"action",render:e=>a("div",null,[a(me,{size:18,onClick:()=>we(e)},{default:()=>[a(oa,{class:"icon-action cursor-pointer"},null)]}),C.value.support===1&&a(me,{class:"ml-2",color:"#ffd07e",size:18,onClick:()=>ke(e)},{default:()=>[a(_a,{class:"icon-action cursor-pointer"},null)]})])}]),he=()=>{const e=t=>t<0?"#d03050":t>0?"#18a058":"";return{loginName:{value:v(h,{style:{cursor:"default"},secondary:!0,type:"default",size:"small",onClick:()=>{console.log("Button clicked")}},()=>o("sum"))},information:{value:v(h,{style:{cursor:"default"},secondary:!0,type:"default",size:"small",onClick:()=>{console.log("Button clicked")}},()=>o("sum"))},noTicket:{value:v("span",s(d.value.total_noTicket))},betAmount:{value:v("span",s(d.value.total_validTurn))},payoutAmount:{value:v("span",s(d.value.total_payoutAmount))},memberWL:{value:v("span",{style:{color:e(d.value.total_memberWL100)}},s(d.value.total_memberWL100))},"member-win-loss":{value:v("span",{style:{color:e(d.value.total_memberWL)}},s(d.value.total_memberWL))},"member-comm":{value:v("span",{style:{color:e(d.value.total_memberComm)}},s(d.value.total_memberComm))},"member-total":{value:v("span",{style:{color:e(d.value.total_memberTotal)}},s(d.value.total_memberTotal))},"company-win-loss":{value:v("span",{style:{color:e(d.value.total_companyWL)}},s(d.value.total_companyWL))},"company-comm":{value:v("span",{style:{color:e(d.value.total_companyComm)}},s(d.value.total_companyComm))},"company-total":{value:v("span",{style:{color:e(d.value.total_companyTotal)}},s(d.value.total_companyTotal))},"provider-win-loss":{value:v("span",{style:{color:e(d.value.total_providerWL)}},s(d.value.total_providerWL))},"provider-comm":{value:v("span",{style:{color:e(d.value.total_providerComm)}},s(d.value.total_providerComm))},"provider-total":{value:v("span",{style:{color:e(d.value.total_providerTotal)}},s(d.value.total_providerTotal))},member_wl:{value:v("span",{style:{color:e(d.value.total_member_wl)}},s(d.value.total_member_wl))},agent_wl:{value:v("span",{style:{color:e(d.value.total_agent_wl)}},s(d.value.total_agent_wl))},provider_wl:{value:v("span",{style:{color:e(d.value.total_provider_wl)}},s(d.value.total_provider_wl))}}},Ie=qe(),S=Ge();async function ke(e){Ie.warning({title:"ยืนยันการส่งรายงาน",content:"คุณแน่ใจหรือไม่ว่าต้องการส่งรายงานนี้?",positiveText:"ยืนยัน",negativeText:"ยกเลิก",onPositiveClick:async()=>{try{const t=await P.post("PG/save/report",e);t.data.status&&S.success(t.data.mes||"ส่งรายงานสำเร็จ!")}catch(t){console.error(t),S.error("เกิดข้อผิดพลาดในการส่งรายงาน")}},onNegativeClick:()=>{S.info("ยกเลิกการส่งรายงาน")}})}function we(e){const t=va.find(z=>z.gameCode===e.gameCode);let n="https%3A%2F%2Fapi.pgf-theks69.com%2Fweb-api%2Foperator-proxy%2Fv1%2FHistory%2FGetBetHistory";[1648578,1727711,1815268,1747549,1786529,1372643,53,1850016,1804577,83,1312883,1879752,1827457,1760238,1881268,1666445,1799745,1702123,1635221,1755623,1834850,1935269].includes(t?.gameId)&&(n="api.pgf-theks69.com%2Fweb-api%2Foperator-proxy%2Fv1%2FHistory%2FGetBetHistory");const y=`https://public.pgf-theks69.com/history/${t.gameId}/${t.gameId}.html?sid=${e.txnId}&psid=${e.roundId}&t=${e.agent_id}&api=${n}&lang=th`;G.value=y,N.value=!0}const s=e=>!e||e=="-0"?0:e%1!==0?(Math.round(e*100)/100).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),J=u([]);Ee(()=>{D(),_.value.push({name:C.value.username,position:C.value.position_type==1||C.value.position_type==4&&C.value.upline_position_type==1?"Company":C.value.position_type==2||C.value.position_type==4&&C.value.upline_position_type==2?"Reseller":"Agent"})});const De=e=>{c.value=2,Y.value=e._id,i.value.condition_1="",Z(e.loginName,e._id,c.value)},$=u([]),xe=u([]);u([]);const Te=e=>{c.value=e.position_type,T.value=e._id,$.value=e.loginName,xe.value=e.position_type,e.position_type==3&&(T.value=e.user_id),e.position_type==4&&(T.value=e.agent_id),i.value.condition_1="",b.value=1,Z(e.loginName,e.agent_id,c.value)},c=u(0),T=u(null),Y=u(null);u(null);const Q=()=>{b.value=1,c.value=0,_.value=[{name:_.value[0].name,position:_.value[0].position}],D()};function Le(e,t){window.$message.success(`${o("gotopage")}: ${e}/${t}`),O.value=t,b.value=e,D()}const _=u([]),j=u("today"),K=e=>{i.value={...E},k.value=null,w.value=null,j.value="today",b.value=1,D()},O=u(10),b=u(1),X=u(1),k=u(null),w=u(null),d=u({_id:null,total_noTicket:0,total_validTurn:0,total_payoutAmount:0,total_memberWL100:0,total_memberWL:0,total_memberComm:0,total_memberTotal:0,total_companyWL:0,total_companyComm:0,total_companyTotal:0,total_providerWL:0,total_providerComm:0,total_providerTotal:0,total_agent_wl:0,total_amount:0,total_member_wl:0,total_provider_wl:0}),D=async()=>{ye();try{const e={perPage:O.value,page:b.value,Stap:c.value,agent_id:T.value,username:Y.value,startDate:null,endDate:null,loginName:i.value.condition_1,gameCode:null,Round:null};M.value==2&&(k.value&&(e.gameCode=k.value),w.value&&(e.Round=w.value)),i.value.condition_2&&(e.startDate=g(i.value.condition_2[0]).format("YYYY-MM-DD"),e.endDate=g(i.value.condition_2[1]).format("YYYY-MM-DD"));let t=await P.get("PG/Report/PayerWinLose",{params:e});M.value=t.data.position_type,X.value=t.data.totalPages,J.value=t.data.data,d.value=t.data.total}catch(e){console.log(e)}finally{fe()}},$e=e=>{if(e=="previous"){const t=g().startOf("day").subtract(1,"days").toDate(),n=g().startOf("day").subtract(1,"days").toDate();i.value.condition_2=[t,n]}else if(e=="today"){const t=g().startOf("day").toDate(),n=g().startOf("day").toDate();i.value.condition_2=[t,n]}else if(e=="yesterday"){const t=g().startOf("day").subtract(1,"days").toDate(),n=g().startOf("day").subtract(1,"days").toDate();i.value.condition_2=[t,n]}else if(e=="this-week"){const t=g().startOf("week").toDate(),n=g().endOf("week").toDate();i.value.condition_2=[t,n]}else if(e=="last-week"){const t=g().subtract(1,"weeks").startOf("week").toDate(),n=g().subtract(1,"weeks").endOf("week").toDate();i.value.condition_2=[t,n]}else if(e=="this-month"){const t=g().startOf("month").toDate(),n=g().endOf("month").toDate();i.value.condition_2=[t,n]}else if(e=="last-month"){const t=g().subtract(1,"months").startOf("month").toDate(),n=g().subtract(1,"months").endOf("month").toDate();i.value.condition_2=[t,n]}else if(e=="next"){const t=g().startOf("day").add(1,"days").toDate(),n=g().startOf("day").add(1,"days").toDate();i.value.condition_2=[t,n]}D()},We=()=>{const e={Stap:c.value,agent_id:T.value,username:Y.value,startDate:null,endDate:null,loginName:i.value.condition_1,gameCode:null,Round:null};M.value==2&&(k.value&&(e.gameCode=k.value),w.value&&(e.Round=w.value)),i.value.condition_2&&(e.startDate=g(i.value.condition_2[0]).format("YYYY-MM-DD"),e.endDate=g(i.value.condition_2[1]).format("YYYY-MM-DD")),P.post("PG/download/report/wl",e,{responseType:"blob"}).then(t=>{const n=new Blob([t.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),y=document.createElement("a");y.href=window.URL.createObjectURL(n),y.download="report",y.click()}).catch(t=>{console.log(t)})},Me=async(e,t)=>{if(e!=_.value.length-1&&_.value.length!=1)if(e==0)c.value=0,T.value=null,Y.value=null,$.value=t.name,_.value=_.value.slice(0,e+1),b.value=1,D();else{const n=_.value[e];_.value=_.value.slice(0,e+1),c.value=n.position,T.value=n._id,$.value=t.name,b.value=1,D()}},Z=async(e,t,n)=>{_.value.push({name:e,_id:t,position:n}),D()};return(e,t)=>{const n=Je,y=da,z=na,ee=Qe,ae=Ke,te=Xe,oe=sa,ne=Ze,le=Ve,x=ia,Oe=ra,Ae=ga,Be=Re,Ne=ze,Ye=Ue,je=la;return I(),W("div",null,[a(l(ge),{vertical:"",size:"large"},{default:r(()=>[a(le,null,{default:r(()=>[l(M)!==2?(I(),F(oe,{key:0,ref_key:"formRef",ref:V,model:l(i),"label-placement":"left",inline:"","show-feedback":!1},{default:r(()=>[f("div",ya,[f("div",fa,[a(y,{label:e.$t("player"),path:"condition_1"},{default:r(()=>[a(n,{value:l(i).condition_1,"onUpdate:value":t[0]||(t[0]=m=>l(i).condition_1=m),placeholder:e.$t("player")},null,8,["value","placeholder"])]),_:1},8,["label"])]),f("div",Ca,[a(y,{label:e.$t("date"),path:"condition_2"},{default:r(()=>[a(ee,{locale:l(A),"date-locale":l(B)},{default:r(()=>[a(z,{value:l(i).condition_2,"onUpdate:value":t[1]||(t[1]=m=>l(i).condition_2=m),type:"daterange",format:"dd-MM-yyyy",clearable:""},null,8,["value"])]),_:1},8,["locale","date-locale"])]),_:1},8,["label"])]),f("div",ba,[f("div",ha,[a(l(h),{type:"primary",onClick:Q,class:"w-full"},{icon:r(()=>[a(ae)]),default:r(()=>[p(" "+L(e.$t("search")),1)]),_:1})]),f("div",Ia,[a(l(h),{secondary:"",onClick:K,class:"w-full"},{icon:r(()=>[a(te)]),default:r(()=>[p(" "+L(e.$t("reset")),1)]),_:1})])])])]),_:1},8,["model"])):(I(),F(oe,{key:1,ref_key:"formRef",ref:V,model:l(i),"label-placement":"left",inline:"","show-feedback":!1},{default:r(()=>[a(ne,null,{default:r(()=>[a(y,{label:e.$t("gameCode")},{default:r(()=>[a(n,{value:l(k),"onUpdate:value":t[2]||(t[2]=m=>U(k)?k.value=m:null),placeholder:e.$t("gameCode")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(y,{label:e.$t("roundid")},{default:r(()=>[a(n,{value:l(w),"onUpdate:value":t[3]||(t[3]=m=>U(w)?w.value=m:null),placeholder:e.$t("roundid")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(y,{label:e.$t("date"),path:"condition_2"},{default:r(()=>[a(ee,{locale:l(A),"date-locale":l(B)},{default:r(()=>[a(z,{value:l(i).condition_2,"onUpdate:value":t[4]||(t[4]=m=>l(i).condition_2=m),type:"daterange",format:"dd-MM-yyyy",clearable:""},null,8,["value"])]),_:1},8,["locale","date-locale"])]),_:1},8,["label"]),a(ne,{class:"ml-auto"},{default:r(()=>[a(l(h),{type:"primary",onClick:Q},{icon:r(()=>[a(ae)]),default:r(()=>[p(" "+L(e.$t("search")),1)]),_:1}),a(l(h),{secondary:"",onClick:K},{icon:r(()=>[a(te)]),default:r(()=>[p(" "+L(e.$t("reset")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]))]),_:1}),a(le,null,{default:r(()=>[f("div",ka,[a(Oe,{type:"card",value:l(j),"onUpdate:value":[t[5]||(t[5]=m=>U(j)?j.value=m:null),$e],"default-value":"today",animated:""},{default:r(()=>[a(x,{name:"previous",tab:l(o)("previous")},null,8,["tab"]),a(x,{name:"today",tab:l(o)("today")},null,8,["tab"]),a(x,{name:"yesterday",tab:l(o)("yesterday")},null,8,["tab"]),a(x,{name:"this-week",tab:l(o)("thisweek")},null,8,["tab"]),a(x,{name:"last-week",tab:l(o)("lastweek")},null,8,["tab"]),a(x,{name:"this-month",tab:l(o)("thismonth")},null,8,["tab"]),a(x,{name:"last-month",tab:l(o)("lastmonth")},null,8,["tab"]),a(x,{name:"next",tab:l(o)("next")},null,8,["tab"])]),_:1},8,["value"])]),a(l(ge),{size:"large"},{default:r(()=>[f("div",wa,[(I(!0),W(ea,null,aa(l(_),(m,H)=>(I(),W("div",{key:H,onClick:Wa=>Me(H,m),class:"flex items-center"},[a(l(h),{tertiary:"",round:"",type:m.position==="Company"?"error":m.position===1||m.position==="Reseller"?"warning":m.position===2||m.position===4||m.position==="Agent"?"success":"info",class:"inline-flex cursor-pointer"},{default:r(()=>[p(L(m.name),1)]),_:2},1032,["type"]),H+1!==l(_).length?(I(),W("span",xa,[a(Ae)])):ue("",!0)],8,Da))),128))])]),_:1}),f("div",Ta,[f("h4",La,L(e.$t("reportall")),1),a(l(h),{type:"primary",class:"ml-a",onClick:t[6]||(t[6]=m=>We())},{icon:r(()=>[a(Be)]),default:r(()=>[p(" "+L(e.$t("download")),1)]),_:1})]),a(Ne,{striped:"",class:"text-nowrap","scroll-x":1200,columns:l(M)==1?l(Ce):l(be),data:l(J),loading:l(ve),summary:he},null,8,["columns","data","loading"]),a(Ye,{class:"mt-5 overflow-auto",count:l(X),page:l(b),onChange:Le},null,8,["count","page"])]),_:1})]),_:1}),a(l(ta),{show:l(N),"onUpdate:show":t[7]||(t[7]=m=>U(N)?N.value=m:null),title:"Bet History","mask-closable":"false"},{default:r(()=>[f("iframe",{src:l(G),style:{width:"414px",height:"736px",border:"none"}},null,8,$a)]),_:1},8,["show"]),e.showAlert?(I(),F(je,{key:0,title:"Info",type:"info",closable:""},{default:r(()=>t[8]||(t[8]=[p("test")])),_:1})):ue("",!0)])}}});export{qa as default};
