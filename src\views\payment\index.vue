<script setup lang="tsx">
// @ts-nocheck
import { dateThTH, thTH, dateEnUS, enUS } from "naive-ui";
import { NButton, NSpace } from "naive-ui";
import http from "@/service/axios";
import moment from "moment-timezone";
import { useAuthStore } from "@/store/auth";
const { t, locale } = useI18n();
const localeDate = ref(locale.value === "thTH" ? thTH : enUS);
const dateLocale = ref(locale.value === "thTH" ? dateThTH : dateEnUS);
const { userData } = useAuthStore();
const router = useRouter();
const message = useMessage();
const confirmdialog = useDialog();
const search = ref("");
const perPage = ref(10);
const Page = ref(1);
const perPage1 = ref(10);
const Page1 = ref(1);
const total = ref(1);
const totalPages = ref(1);
const loading = ref(false);
const items = ref([]);
const datadetail = ref([]);
watch(
  () => locale.value,
  (newValue) => {
    if (newValue === "thTH") {
      dateLocale.value = dateThTH;
      localeDate.value = thTH;
    } else if (newValue === "enUS") {
      dateLocale.value = dateEnUS;
      localeDate.value = enUS;
    }
  }
);
const time = (value) => {
  return value
    ? moment(value).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm")
    : "-";
};

// const checkRole = () => {
//   if (userData?.position_type == 4 && userData?.upline_position_type == 1) {
//     const permis = JSON.parse(userData?.permissions);
//     if (permis.setting !== 2) {
//       return false;
//     }
//   }
//   return true;
// };
const columns = computed(() => {
  const roleColumns = [
    {
      title: t("no."),
      align: "center",
      key: "index",
      render: (row, index) => {
        return perPage.value * (Page.value - 1) + index + 1;
      },
    },

    {
      title: t("loginname"),
      align: "center",
      key: "username",
      render: (row) => {
        if (
          row.position_type &&
          (row.position_type == 2 || row.position_type == 3)
        ) {
          return (
            <n-button
              class="cursor-default"
              tertiary
              round
              type={
                row.position_type === 2
                  ? "warning"
                  : row.position_type === 3
                  ? "success"
                  : "info"
              }
            >
              {row.username}
            </n-button>
          );
        }
        return (
          <n-button
            class="cursor-default"
            tertiary
            round
            type={
              row.position_type === 2
                ? "warning"
                : row.position_type === 3
                ? "success"
                : "info"
            }
          >
            {row.username}
          </n-button>
        );
      },
    },
    {
      title: t("position"),
      align: "center",
      key: "position",
      render: (row) => {
        return (
          <n-button
            size="small"
            class="cursor-default"
            ghost
            type={
              row.position_type === 2
                ? "warning"
                : row.position_type === 3
                ? "success"
                : "info"
            }
          >
            {row.position_type === 2
              ? t("reseller")
              : row.position_type === 3
              ? t("agent")
              : t("member")}
          </n-button>
        );
      },
    },
    {
      title: t("currency"),
      align: "center",
      key: "currency",
      render: (item) => {
        let text = "";
        if (item.position_type == 3) {
          text = item.ag_currency;
        } else {
          if (item.reseller_thb == 1) {
            text = "THB";
          }
          if (item.reseller_krw == 1) {
            if (text) {
              text += ",KRW";
            } else {
              text = "KRW";
            }
          }
          if (item.reseller_usd == 1) {
            if (text) {
              text += ",USD";
            } else {
              text = "USD";
            }
          }
        }
        if (!text && item.currency) {
          text = item.currency;
        }
        return text;
      },
    },

    {
      title: t("credit"),
      align: "center",
      key: "credit",
      render: (row, index) => {
        return (
          <div class="text-start ">
            {row.reseller_thb === 1 && (
              <div class="flex justify-start items-center gap-2">
                <img
                  src="/images/country/th.webp"
                  alt="Language Icon"
                  width="25"
                  height="20"
                />{" "}
                {Commas(row.credit_thb)} ฿
              </div>
            )}
            {row.reseller_krw === 1 && (
              <div class="flex justify-start items-center gap-2">
                <img
                  src="/images/country/kr.webp"
                  alt="Language Icon"
                  width="25"
                  height="20"
                />{" "}
                {Commas(row.credit_krw)} ₩
              </div>
            )}
            {row.reseller_usd === 1 && (
              <div class="flex justify-start items-center gap-2">
                <img
                  src="/images/country/us.webp"
                  alt="Language Icon"
                  width="25"
                  height="20"
                />{" "}
                {Commas(row.credit_usd)} $
              </div>
            )}
            {row.ag_currency === "THB" && (
              <div class="flex justify-start items-center gap-2">
                <img
                  src="/images/country/th.webp"
                  alt="Language Icon"
                  width="25"
                  height="20"
                />{" "}
                {Commas(row.credit_thb)} ฿
              </div>
            )}
            {row.ag_currency === "KRW" && (
              <div class="flex justify-start items-center gap-2">
                <img
                  src="/images/country/kr.webp"
                  alt="Language Icon"
                  width="25"
                  height="20"
                />{" "}
                {Commas(row.credit_krw)} ₩
              </div>
            )}
            {row.ag_currency === "USD" && (
              <div class="flex justify-start items-center gap-2">
                <img
                  src="/images/country/us.webp"
                  alt="Language Icon"
                  width="25"
                  height="20"
                />{" "}
                {Commas(row.credit_usd)} $
              </div>
            )}
            {(userData.position_type == 4 &&
              userData.upline_position_type == 3) ||
              (userData.position_type == 3 && row.currency === "THB" && (
                <div class="flex justify-center items-center gap-2">
                  <img
                    src="/images/country/th.webp"
                    alt="Language Icon"
                    width="25"
                    height="20"
                  />{" "}
                  {Commas(row.credit)} ฿
                </div>
              ))}
            {(userData.position_type == 4 &&
              userData.upline_position_type == 3) ||
              (userData.position_type == 3 && row.currency === "KRW" && (
                <div class="flex justify-center items-center gap-2">
                  <img
                    src="/images/country/kr.webp"
                    alt="Language Icon"
                    width="25"
                    height="20"
                  />{" "}
                  {Commas(row.credit)} ₩
                </div>
              ))}
            {(userData.position_type == 4 &&
              userData.upline_position_type == 3) ||
              (userData.position_type == 3 && row.currency === "USD" && (
                <div class="flex justify-center items-center gap-2">
                  <img
                    src="/images/country/us.webp"
                    alt="Language Icon"
                    width="25"
                    height="20"
                  />{" "}
                  {Commas(row.credit)} $
                </div>
              ))}
          </div>
        );
      },
    },
  ];
  if (userData.position_type == 2 || userData.position_type == 1) {
    roleColumns.push(
      {
        title: t("deposit"),
        align: "center",
        key: "actions",
        render: (row) => {
          return (
            <n-button
              onClick={() => adddeposit(row)}
              secondary
              type="success"
              size="small"
            >
              <icon-park-outline-add-one />
            </n-button>
          );
        },
      },

      {
        title: t("withdraw"),
        align: "center",
        key: "actions",
        render: (row) => {
          return (
            <n-button
              onClick={() => addwithdraw(row)}
              secondary
              type="error"
              size="small"
            >
              <icon-park-outline-reduce-one />
            </n-button>
          );
        },
      },
      {
        title: t("detail"),
        align: "center",
        key: "",
        render: (row) => {
          return (
            <n-button
              onClick={() => viewdetail(row)}
              secondary
              type="info"
              size="small"
            >
              <icon-park-outline-bill />
            </n-button>
          );
        },
      }
    );
  }
  roleColumns.push(
    {
      title: t("lastlogindate"),
      align: "center",
      key: "last_login",
      render: (row) => {
        return time(row.last_login);
      },
    },
    {
      title: t("lastloginip"),
      align: "center",
      key: "last_ip",
      render: (row) => {
        return row.last_ip || "-";
      },
    }
  );
  return roleColumns;
});
const detailcolumns = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage1.value * (Page1.value - 1) + (index + 1);
    },
  },
  {
    title: t("detail"),
    align: "center",
    key: "detail",
    render: (row, index) => {
      return (
        <div>
          {" "}
          <n-button
            class="cursor-default"
            secondary
            type={row.action === "deposit" ? "success" : "error"}
            size="small"
          >
            {row.action === "deposit" ? t("deposit") : t("withdraw")}
          </n-button>
        </div>
      );
    },
  },
  {
    title: t("currency"),
    align: "center",
    key: "currency",
    render: (row, index) => {
      return row.currency;
    },
  },
  {
    title: t("amount"),
    align: "center",
    key: "amount",
    render: (row, index) => {
      return Commas(row.amount);
    },
  },
  {
    title: t("date"),
    align: "center",
    key: "created_at",
    render: (row, index) => {
      return time(row.created_at);
    },
  },
]);

onMounted(() => {
  GetData();
});
const currency_type = ref("THB");
const currency = computed(() => [
  {
    label: "THB",
    value: "THB",
  },
  {
    label: "KRW",
    value: "KRW",
  },
  {
    label: "USD",
    value: "USD",
  },
]);
const GetData = async () => {
  loading.value = true;
  try {
    const params = {
      Page: Page.value,
      perPage: perPage.value,
      currency: currency_type.value,
      username: search.value,
    };
    const { data: res } = await http.get("v1/Payment/list", { params });
    items.value = res.data;
    totalPages.value = res.total;
    loading.value = false;
  } catch (error) {
    console.error("Error fetching data:", error);
  }
};

const dialogdeposit = ref(false);
const editdeposit = ref([]);
const adddeposit = (item) => {
  editdeposit.value = item;
  dialogdeposit.value = true;
};

const dialogwithdraw = ref(false);
const editwithdraw = ref(null);
const addwithdraw = (item) => {
  if (item) {
    editwithdraw.value = item;
    dialogwithdraw.value = true;
  }
};
const submitWithdraw = () => {
  const currency =
    editwithdraw.value.ag_currency || editwithdraw.value.currency;
  if (!currency) {
    return message.error(t("valid"));
  }
  if (!Number(editwithdraw.value.amountcurrency)) {
    return message.error(t("valid"));
  }
  confirmdialog.warning({
    title: t("confirmsave"),
    content: t("areyousure"),
    positiveText: t("confirm"),
    negativeText: t("cancel"),
    maskClosable: false,
    onPositiveClick: () => {
      const obj = {
        username: editwithdraw.value.username,
        agent_id: editwithdraw.value.id,
        amount: editwithdraw.value.amountcurrency,
        currency: currency,
      };
      http.post("v1/Payment/Withdrawal", obj).then((response) => {
        if (response.data.status) {
          message.success(t("commitsuccess"));
          dialogwithdraw.value = false;
          GetData();
        } else {
          message.error(response.data.mes);
        }
      });
    },
    onEsc: () => {
      message.success(t("cancel"));
    },
  });
};

const submitDeposit = () => {
  const currency = editdeposit.value.ag_currency || editdeposit.value.currency;
  if (!currency) {
    return message.error(t("valid"));
  }
  if (!Number(editdeposit.value.amountcurrency)) {
    return message.error(t("valid"));
  }
  confirmdialog.warning({
    title: t("confirmsave"),
    content: t("areyousure"),
    positiveText: t("confirm"),
    negativeText: t("cancel"),
    maskClosable: false,
    onPositiveClick: () => {
      const obj = {
        username: editdeposit.value.username,
        agent_id: editdeposit.value.id,
        amount: editdeposit.value.amountcurrency,
        currency: currency,
      };
      http.post("v1/Payment/Deposit", obj).then((response) => {
        if (response.data.status) {
          message.success(t("commitsuccess"));
          dialogdeposit.value = false;
          GetData();
        } else {
          message.error(response.data.mes);
        }
      });
    },
    onEsc: () => {
      message.success(t("cancel"));
    },
  });
};
const getFilteredOptions = (item) => {
  const options = [];
  if (item.reseller_thb) {
    options.push({
      label: "THB",
      value: "THB",
    });
  }
  if (item.reseller_krw) {
    options.push({
      label: "KRW",
      value: "KRW",
    });
  }
  if (item.reseller_usd) {
    options.push({
      label: "USD",
      value: "USD",
    });
  }

  return options;
};
const dialogdetail = ref(false);
const detailpayment = ref([]);
const viewdetail = (item) => {
  loading.value = true;
  const params = {
    perPage: perPage1.value,
    page: Page1.value,
    agent_id: item.id,
    startDate: findDate.value
      ? moment(findDate.value[0]).format("YYYY-MM-DD HH:mm:ss")
      : null,
    endDate: findDate.value
      ? moment(findDate.value[1]).format("YYYY-MM-DD HH:mm:ss")
      : null,
  };
  http.get("v1/Payment/GetDetail", { params }).then((response) => {
    detailpayment.value = item;
    datadetail.value = response.data.data;
    total.value = response.data.total || 1;
    dialogdetail.value = true;
    loading.value = false;
  });
};
const GetDetail = () => {
  loading.value = true;
  const params = {
    perPage: perPage1.value,
    page: Page1.value,
    agent_id: detailpayment.value.id,
    startDate: findDate.value
      ? moment(findDate.value[0]).format("YYYY-MM-DD HH:mm:ss")
      : null,
    endDate: findDate.value
      ? moment(findDate.value[1]).format("YYYY-MM-DD HH:mm:ss")
      : null,
  };
  http.get("v1/Payment/GetDetail", { params }).then((response) => {
    datadetail.value = response.data.data;
    total.value = response.data.total || 1;
    loading.value = false;
  });
};

function changePage(page: number, size: number) {
  message.success(`${t("gotopage")} : ${page}/${size}`);
  perPage.value = size;
  Page.value = page;
  GetData();
}
function changePage1(page: number, size: number) {
  message.success(`${t("gotopage")} : ${page}/${size}`);
  perPage1.value = size;
  Page1.value = page;
  GetDetail();
}
const Commas = (x: any) => {
  if (!x || x == "-0") {
    return 0;
  }
  if (x % 1 !== 0) {
    let roundedNumber = Math.round(x * 100) / 100;
    return roundedNumber.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
const findDate = ref([
  moment().startOf("day").toDate(),
  moment().endOf("day").toDate(),
]);
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <div
          class="flex gap-3 flex-col lg:flex-row justify-between items-start"
        >
          <div class="font-medium text-[1rem]">
            {{ $t("paymentmanagement") }}
          </div>
          <div class="flex items-center flex-col sm:flex-row gap-3">
            <n-form-item
              :label="$t('loginname')"
              label-placement="left"
              :show-feedback="false"
              class="w-full sm:w-72"
            >
              <n-input
                v-model:value="search"
                type="text"
                :placeholder="$t('loginname')"
              />
            </n-form-item>
            <n-form-item
              :label="$t('currency')"
              label-placement="left"
              :show-feedback="false"
              class="w-full sm:w-40"
            >
              <n-select v-model:value="currency_type" :options="currency" />
            </n-form-item>
            <n-form-item label-placement="left" :show-feedback="false">
              <n-button type="primary" @click="GetData()">
                <icon-park-outline-search />
                <span class="ml-2">{{ $t("search") }}</span>
              </n-button>
            </n-form-item>
          </div>
        </div>

        <n-data-table
          :scroll-x="
            userData.position_type == 1 || userData.position_type == 2
              ? 1200
              : 1000
          "
          :loading="loading"
          :columns="columns"
          :data="items"
          :bordered="false"
        />
        <Pagination
          class="overflow-auto float-right"
          :count="totalPages"
          @change="changePage"
        />
      </n-space>
    </n-card>

    <n-modal v-model:show="dialogdeposit" :show-icon="false" preset="dialog">
      <template #header>{{ $t("deposit") }} </template>

      <div>
        <div class="flex justify-start items-center text-nowrap gap-3 my-3">
          <p>{{ $t("username") }} :</p>
          <n-button
            tertiary
            :type="
              editdeposit.position_type === 2
                ? 'warning'
                : editdeposit.position_type === 3
                ? 'success'
                : 'info'
            "
            class="cursor-default"
          >
            {{ editdeposit.username }}
          </n-button>
        </div>
        <div class="flex justify-center items-center text-nowrap gap-3 my-3">
          <p>{{ $t("deposit") }} :</p>
          <n-select
            v-if="
              editdeposit.position_type == 1 || editdeposit.position_type == 2
            "
            v-model:value="editdeposit.currency"
            :options="currency"
            :placeholder="$t('currency')"
          />
          <n-input
            v-if="editdeposit.position_type == 3"
            v-model:value="editdeposit.ag_currency"
            disabled
            :placeholder="$t('currency')"
          />
          <n-input
            v-model:value="editdeposit.amountcurrency"
            :placeholder="$t('amount')"
            type="number"
          />
        </div>
      </div>
      <n-flex justify="end">
        <NButton type="default" @click="dialogdeposit = false">
          <template #icon />
          {{ $t("cancel") }}
        </NButton>
        <NButton type="primary" @click="submitDeposit()">
          <template #icon />
          {{ $t("save") }}
        </NButton>
      </n-flex>
    </n-modal>

    <n-modal
      v-model:show="dialogwithdraw"
  :show-icon="false" preset="dialog"
    >
      <template #header>{{ $t("withdraw") }} </template>
      <n-scrollbar>
        <div>
          <div class="flex justify-start items-center text-nowrap gap-3 my-3">
            <p>{{ $t("username") }} :</p>
            <n-button
              tertiary
              :type="
                editwithdraw.position_type === 2
                  ? 'warning'
                  : editwithdraw.position_type === 3
                  ? 'success'
                  : 'info'
              "
              class="cursor-default"
            >
              {{ editwithdraw.username }}
            </n-button>
          </div>
          <div class="flex justify-center items-center text-nowrap gap-3 my-3">
            <p>{{ $t("withdraw") }} :</p>
            <n-select
              v-if="
                editwithdraw.position_type == 1 ||
                editwithdraw.position_type == 2
              "
              v-model:value="editwithdraw.currency"
              :options="getFilteredOptions(editwithdraw)"
              :placeholder="$t('currency')"
            />
            <n-input
              v-if="editwithdraw.position_type == 3"
              v-model:value="editwithdraw.ag_currency"
              disabled
              :placeholder="$t('currency')"
            />
            <n-input
              v-model:value="editwithdraw.amountcurrency"
              :placeholder="$t('amount')"
              type="number"
            />
          </div>
        </div>
        <n-flex justify="end" >
          <NButton type="default"  @click="dialogwithdraw = false">
            <template #icon />
            {{ $t("cancel") }}
          </NButton>
          <NButton type="primary" @click="submitWithdraw()">
            <template #icon />
            {{ $t("save") }}
          </NButton>
        </n-flex>
      </n-scrollbar>
    </n-modal>

    <n-modal
      v-model:show="dialogdetail"
      class="w-350px sm:w-800px fixed inset-x-0 max-h-[80vh] overflow-y-auto"
      size="small"
      preset="card"
      :segmented="{
        content: true,
        footer: true,
      }"
    >
      <template #header
        >{{ $t("detail") }}
        <n-button
          round
          tertiary
          :type="
            detailpayment.position_type === 2
              ? 'warning'
              : detailpayment.position_type === 3
              ? 'success'
              : 'info'
          "
          class="cursor-default ml-2"
        >
          {{ detailpayment.username }}
        </n-button></template
      >
      <n-scrollbar>
        <div class="sm:flex justify-center gap-3 mb-3">
          <n-config-provider :locale="localeDate" :date-locale="dateLocale">
            <n-date-picker
              class="my-2"
              v-model:value="findDate"
              type="daterange"
              format="dd-MM-yyyy"
              clearable
            />
          </n-config-provider>
          <n-button
            type="primary"
            class="my-2 w-full sm:w-auto"
            @click="GetDetail()"
          >
            <template #icon>
              <icon-park-outline-search />
            </template>
            {{ $t("search") }}
          </n-button>
        </div>
        <n-card class="mb-5">
          <n-space vertical>
            <n-data-table
              :scroll-x="500"
              :loading="loading"
              :columns="detailcolumns"
              :data="datadetail"
              :bordered="false"
            />
            <Pagination
              class="overflow-auto float-right"
              :count="total"
              @change="changePage1"
            />
          </n-space>
        </n-card>
      
      </n-scrollbar>
    </n-modal>
  </div>
</template>
