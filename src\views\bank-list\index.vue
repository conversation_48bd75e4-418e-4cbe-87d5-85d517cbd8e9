<script setup lang="tsx">
// @ts-nocheck
import moment from "moment-timezone";
import type { DataTableColumns, FormInst } from "naive-ui";
import { useBoolean } from "@/hooks";
import { NButton, NPopconfirm, NSpace, NSwitch, NTag } from "naive-ui";
import http from "@/service/axios";
import { useMessage } from "naive-ui";
import { useAuthStore } from "@/store/auth";
const { userData } = useAuthStore();
const {
  bool: loading,
  setTrue: startLoading,
  setFalse: endLoading,
} = useBoolean(false);
const { bool: visible, setTrue: openModal } = useBoolean(false);

const initialModel = {
  condition_1: "",
  condition_2: "",
  condition_3: null,
};
const model = ref({ ...initialModel });
const formRef = ref<FormInst | null>();
const { t } = useI18n();
const columns: DataTableColumns<Entity.User> = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return index + 1;
    },
  },

  {
    title: t("player"),
    align: "center",
    key: "username",
  },

  {
    title: t("currency"),
    align: "center",
    key: "currency",
  },
  // {
  //   title: "ผู้แก้ไข",
  //   align: "center",
  //   key: "edit_by",
  // },
  {
    title: t("createdate"),
    align: "center",
    key: "created_at",
    render: (row) => {
      return moment(row.created_at)
        .tz("Asia/Bangkok")
        .format("DD/MM/YYYY HH:mm:ss");
    },
  },
  {
    title: t("status"),
    align: "center",
    key: "status",
    render: (row) => {
      return row.status === true ? (
        <n-button ghost type="success" class="cursor-default">
          {t("active")}
        </n-button>
      ) : (
        <n-button ghost type="error" class="cursor-default">
          {t("inactive")}
        </n-button>
      );
    },
  },
  {
    title: t("manage"),
    align: "center",
    key: "actions",
    render: (row) => {
      if (
        (userData.position_type == 4 && userData.upline_position_type == 1) ||
        (userData.position_type == 4 && userData.upline_position_type == 2) ||
        (userData.position_type == 4 && userData.upline_position_type == 3)
      ) {
        const permis = JSON.parse(userData.permissions);
        if (permis.player !== 2) {
          return (
            <n-button class="cursor-default" tertiary type="error" size="small">
              <icon-park-outline-close />
            </n-button>
          );
        }
      }
      return (
        <NSpace justify="center">
          <n-button
            type="primary"
            size="small"
            secondary
            onClick={() => handleEditTable(row)}
          >
            <icon-park-outline-write />
          </n-button>
        </NSpace>
      );
    },
  },
]);
const columns1: DataTableColumns<Entity.User> = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return index + 1;
    },
  },
  {
    title: t("position"),
    align: "center",
    key: "position_type",
    render: (row) => {
      return (
        <n-button
          class="cursor-default"
          ghost
          size="small"
          type={row.position_type == 2 ? "warning" : "success"}
        >
          {t(row.position_type == 2 ? "reseller" : "agent")}
        </n-button>
      );
    },
  },
  {
    title: t("player"),
    align: "center",
    key: "username",
  },
  {
    title: t("name"),
    align: "center",
    key: "name",
    render: (row) => {
      return <div>{row.name || "-"}</div>;
    },
  },
  // {
  //   title: "สกุลเงิน",
  //   align: "center",
  //   key: "currency",
  // },
  {
    title: t("wallettype"),
    align: "center",
    key: "bet_type",
    render: (row) => {
      return (
        <n-button
          ghost
          size="small"
          class="cursor-default"
          type={row.bet_type == 1 ? "error" : "info"}
        >
          {row.bet_type == 1 ? "Seamless" : "Tranfer"}
        </n-button>
      );
    },
  },
  {
    title: t("createdate"),
    align: "center",
    key: "created_at",
  },
  {
    title: t("lastlogin"),
    align: "center",
    key: "last_login",
    render: (row) => {
      return moment(row.last_login)
        .tz("Asia/Bangkok")
        .format("DD/MM/YYYY HH:mm:ss");
    },
  },
  {
    title: t("lastloginip"),
    align: "center",
    key: "last_ip",
    render: (row) => {
      return moment(row.last_ip)
        .tz("Asia/Bangkok")
        .format("DD/MM/YYYY HH:mm:ss");
    },
  },
  // {
  //   title: "จัดการ",
  //   align: "center",
  //   key: "actions",
  //   render: (row) => {
  //     return (
  //       <NSpace justify="center">
  //         <NButton size="small" onClick={() => handleEditTable(row)}>
  //           แก้ไข
  //         </NButton>
  //         <NPopconfirm onPositiveClick={() => sendMail(row.id)}>
  //           {{
  //             default: () => "确认删除",
  //             trigger: () => <NButton size="small">ลบ</NButton>,
  //           }}
  //         </NPopconfirm>
  //       </NSpace>
  //     );
  //   },
  // },
]);
const message = useMessage();
const showModal = ref(false);
const itemsedit = ref(null);
const handleEditTable = (item) => {
  if (item) {
    itemsedit.value = item;
  } else {
    itemsedit.value = null;
  }
  showModal.value = true;
};
const SaveData = () => {
  if (itemsedit.value) {
    const obj = {
      id: itemsedit.value._id,
      name: itemsedit.value.name,
      Status: itemsedit.value.status,
    };
    http.post("v1/Management/Update", obj).then((response) => {
      if (response.data.status) {
        showModal.value = false;
        message.success(response.data.mes);
      } else {
        message.error(response.data.mes);
      }
      getData();
    });
  }
};

const options = computed(() => [
  { role: t("provider"), id: 1 },
  { role: t("player"), id: 2 },
  { role: t("status"), id: 3 },
]);

const optionsStatus = ref([
  { role: "THB", id: "THB" },
  { role: "KRW", id: "KRW" },
  { role: "USD", id: "USD" },
]);
const optionsStatusEdit = computed(() => [
  { label: t("active"), value: true },
  { label: t("inactive"), value: false },
]);

const list: any = ref([]);
const total: any = ref(1);
const UserData = JSON.parse(localStorage.getItem("userData"));

onMounted(() => {
  getData();
});

function changePage(page: number, size: number) {
  //   window.$message.success(`${t("gotopage")}: ${page}/${size}`);
  perPage.value = size;
  Page.value = page;
  getData();
}

const handleResetSearch = (row: Entity.User) => {
  model.value = { ...initialModel };
};
const perPage = ref(10);
const Page = ref(1);
const getData = async () => {
  startLoading();
  try {
    const params = {
      perPage: perPage.value,
      page: Page.value,
      search: model.value.condition_2,
      currency: model.value.condition_3,
    };
    const res = await http.get("v1/Management/GetMembers", { params });
    list.value = res.data.data;
    total.value = res.data.total ? res.data.total : 1;
  } catch (error) {
    console.log(error);
  } finally {
    endLoading();
  }
};
const downloadplayer = () => {
  const params = {
    search: model.value.condition_2,
    currency: model.value.condition_3,
  };
  http
    .post("v1/download/player", params, { responseType: "blob" })
    .then((response) => {
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "player";
      link.click();
      // this.showOver = false
    })
    .catch((error) => {
      console.log(error);
      // this.SwalError(error.response.data.message)
      // this.showOver = false
    });
};
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <div class="sm:flex justify-between items-center">
          <h4 class="font-medium text-[1rem] text-center">
            {{ $t("listplayer") }}
          </h4>

          <n-form
            ref="formRef"
            :model="model"
            label-placement="left"
            :show-feedback="false"
          >
            <div class="flex flex-wrap justify-center gap-3 mt-3 sm:mt-0">
              <div class="w-full sm:w-auto">
                <n-form-item :label="$t('player')" path="condition_2">
                  <n-input
                    v-model:value="model.condition_2"
                    :placeholder="$t('player')"
                  />
                </n-form-item>
              </div>
              <div class="w-full sm:w-auto">
                <n-form-item :label="$t('currency')" path="condition_3">
                  <n-select
                    v-model:value="model.condition_3"
                    :placeholder="$t('currency')"
                    filterable
                    clearable
                    label-field="role"
                    value-field="id"
                    :options="optionsStatus"
                  />
                </n-form-item>
              </div>
              <div class="space-x-3">
                <n-button secondary @click="handleResetSearch">
                  <template #icon>
                    <icon-park-outline-redo />
                  </template>
                  {{ $t("reset") }}
                </n-button>

                <n-button type="primary" @click="getData">
                  <template #icon>
                    <icon-park-outline-search />
                  </template>
                  {{ $t("search") }}
                </n-button>

                <n-button type="primary" secondary @click="downloadplayer()">
                  <template #icon>
                    <icon-park-outline-download />
                  </template>
                  {{ $t("download") }}
                </n-button>
              </div>
            </div>
          </n-form>
        </div>
        <n-data-table
          :scroll-x="1200"
          :columns="UserData.value.position_type == 1 ? columns1 : columns"
          :data="list"
          :loading="loading" />
        <Pagination
          class="overflow-auto float-right"
          :count="total"
          @change="changePage"
      /></n-space>
    </n-card>
    <n-modal v-model:show="showModal" preset="dialog" :show-icon="false">
      <template #header>
        <!-- <template #prefix> -->
        {{ $t("updateplayer") }}
        <!-- </template> -->
      </template>
      <NSpace vertical>
        <div class="flex items-center gap-3 my-2">
          <span class="w-15">{{ $t("player") }}</span>

          <n-input
            v-model:value="itemsedit.username"
            type="text"
            disabled
            placeholder
          />
        </div>

        <div class="flex items-center gap-3 my-2">
          <span class="w-15">{{ $t("name") }}</span>

          <n-input
            v-model:value="itemsedit.name"
            type="text"
            :placeholder="$t('name')"
          />
        </div>

        <div class="flex items-center gap-3 my-2">
          <span class="w-15">{{ $t("status") }}</span>
          <n-select
            v-model:value="itemsedit.status"
            :placeholder="$t('status')"
            filterable
            label-field="label"
            value-field="value"
            :options="optionsStatusEdit"
          />
        </div>

        <div class="flex justify-end">
          <n-button
            type="default"
            @click="(showModal = false), getData()"
            class="mr-2"
          >
            <template #icon></template>
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="SaveData()">
            <template #icon></template>
            {{ $t("save") }}
          </n-button>
        </div>
      </NSpace>
    </n-modal>
  </div>
</template>
