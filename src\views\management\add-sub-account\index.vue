<script setup lang="tsx">
// eslint-disable-next-line ts/ban-ts-comment
// @ts-nocheck
import type { FormInst } from "naive-ui";
import { useMessage } from "naive-ui";
import http from "@/service/axios";
import { router } from "@/router";
const { t } = useI18n();
const loading = ref(false);
const message = useMessage();
const formRef = ref<FormInst | null>(null);
const model = ref({
  username: "",
  name: "",
  password: "",
  phone: "",
  management: 0,
  setting: 0,
  report: 0,
  player: 0,
});

const management = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
  {
    value: 2,
    label: t("edit"),
  },
]);

const setting = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
  {
    value: 2,
    label: t("edit"),
  },
]);

const report = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
]);

const player = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
  {
    value: 2,
    label: t("edit"),
  },
]);

const rules = computed(() => ({
  username: [{ required: true, message: t("validusername"), trigger: "blur" }],
  name: [{ required: true, message: t("validname"), trigger: "blur" }],
  password: [{ required: true, message: t("validpassword"), trigger: "blur" }],
}));

const UserName = ref(null);
onMounted(() => {
  GetAgent();
});
function GetAgent() {
  http.get("v1/Profile/agent").then((response) => {
    if (response.data.success) {
      UserName.value = response.data.data.username;
    }
  });
}
function Submit(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      loading.value = true;
      const obj = {
        username: model.value.username,
        password: model.value.password,
        name: model.value.name,
        tel: model.value.phone,
        permissions: {
          management: model.value.management,
          player: model.value.player,
          report: model.value.report,
          setting: model.value.setting,
        },
      };
      http.post("v1/Store/SubAccount", obj).then((response) => {
        if (response.data.success) {
          message.success(response.data.mes);
          router.push("/management/sub-account");
          loading.value = false;
        } else {
          message.error(response.data.mes);
          loading.value = false;
        }
      });
    }
  });
}
</script>

<template>
  <n-space vertical size="large">
    <n-card>
      <n-divider>
        {{ $t("basicinfomation") }}
      </n-divider>
      <n-form ref="formRef" :model="model" :show-feedback="true" :rules="rules">
        <div
          class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-5 max-w-7xl mx-auto"
        >
          <n-form-item :label="`${UserName}@`" path="username">
            <n-input
              v-model:value="model.username"
              :placeholder="$t('username')"
            />
          </n-form-item>
          <n-form-item :label="$t('name')" path="name">
            <n-input v-model:value="model.name" :placeholder="$t('name')" />
          </n-form-item>
          <n-form-item :label="$t('password')" path="password">
            <n-input
              v-model:value="model.password"
              :placeholder="$t('password')"
            />
          </n-form-item>
          <n-form-item :label="$t('phone')">
            <n-input v-model:value="model.phone" :placeholder="$t('phone')" />
          </n-form-item>
        </div>
      </n-form>
      <n-divider>
        {{ $t("permissions") }}
      </n-divider>
      <n-space vertical size="large">
        <div class="flex justify-center">
          <div
            class="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-5 items-start"
          >
            <n-form-item :label="$t('management')" class="sm:mx-10">
              <n-card>
                <n-radio-group v-model:value="model.management">
                  <n-space>
                    <n-radio
                      v-for="item in management"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    />
                  </n-space> </n-radio-group
              ></n-card>
            </n-form-item>

            <n-form-item :label="$t('settingmanagement')" class="sm:mx-10">
              <n-radio-group v-model:value="model.setting">
                <n-card>
                  <n-space>
                    <n-radio
                      v-for="item in setting"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    /> </n-space
                ></n-card>
              </n-radio-group>
            </n-form-item>

            <n-form-item :label="$t('report')" class="sm:mx-10">
              <n-radio-group v-model:value="model.report">
                <n-card>
                  <n-space>
                    <n-radio
                      v-for="item in report"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    /> </n-space
                ></n-card>
              </n-radio-group>
            </n-form-item>

            <n-form-item :label="$t('player')" class="sm:mx-10">
              <n-radio-group v-model:value="model.player">
                <n-card>
                  <n-space>
                    <n-radio
                      v-for="item in player"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    /> </n-space
                ></n-card>
              </n-radio-group>
            </n-form-item>
          </div>
        </div>
      </n-space>
      <n-flex justify="end" class="mt-10">
        <n-button :loading="loading" type="primary" @click="Submit">
          <template #icon>
            <icon-park-outline-add-one />
          </template>
          {{ $t("save") }}
        </n-button>
      </n-flex>
    </n-card>
  </n-space>
</template>
