<script setup lang="ts">
import { useMessage } from 'naive-ui';
const jsonContent = ref<HTMLElement | null>(null); 
const {t} = useI18n()
const message = useMessage();
const copyToClipboard = () => {
  const content = jsonContent.value?.innerText;
  if (content) {
    navigator.clipboard.writeText(content).then(() => {
      message.success(t('copysuccess'));
    })
  }
};</script>
<template>
  <div>
    <n-card>
      <div class="font-bold">Get Balance</div>
      <div>
        <n-button
          type="success"
          round
          size="small"
          class="mt-3 cursor-default"
          >{{ $t("update") }}</n-button
        >
        {{ $t("lastupdate") }}
      </div>
      <div class="border border-orange rounded-lg p-2 mt-4">
        <n-button class="cursor-default" type="warning" round size="tiny"
          >POST</n-button
        >
        <span v-pre> {{ CALLBACK_URL }}/api/pgpro/checkBalance</span>
      </div>
      <n-divider></n-divider>
      <!-- Security -->
      <div>
        <div class="font-bold">{{$t('security')}}</div>
        <div class="border rounded-lg p-2 mt-4">
          <div>
            <n-button class="cursor-default" ghost round size="tiny"
              >Authorization</n-button
            >
          </div>
          <span v-pre>
            apiKey : Base64({{ agent_username }}:{{ secret_key }})</span
          >
          <hr class="my-2" />
          <div>
            <n-button class="cursor-default" ghost round size="tiny"
              >Content Type</n-button
            >
          </div>
          <span v-pre> Type: application/json</span>
        </div>
      </div>
      <n-divider></n-divider>

     <!-- Parameter  -->
     <div>
        <div class="font-bold">Parameter Description</div>
        <div class="mt-4 overflow-x-auto ">
          <n-table>
            <thead>
              <tr>
                <th class="w-1/4">{{ $t("property") }}</th>
                <th class="w-1/4">{{ $t("type") }}</th>
                <th class="w-1/4">{{ $t("required") }}</th>
                <th class="w-1/4">{{ $t("description") }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>id</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb1") }}</td>
              </tr>
              <tr>
                <td>timestampMillis</td>
                <td>number</td>
                <td>Required</td>
                <td>{{ $t("doc.gb2") }}</td>
              </tr>
              <tr>
                <td>productId</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb3") }}</td>
              </tr>
              <tr>
                <td>currency</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb4") }}</td>
              </tr>
              <tr>
                <td>username</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb5") }}</td>
              </tr>
              <tr>
                <td>sessionToken</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb6") }}</td>
              </tr>
            </tbody>
          </n-table>
        </div>
      </div>

      <!-- Response -->
      <div class="mt-4">
        <div class="font-bold">Response Description</div>
        <div class="mt-4 overflow-x-auto">
          <n-table>
            <thead>
              <tr>
                <th class="w-1/4">{{ $t("property") }}</th>
                <th class="w-1/4">{{ $t("type") }}</th>
                <th class="w-1/4">{{ $t("required") }}</th>
                <th class="w-1/4">{{ $t("description") }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>id</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb7") }}</td>
              </tr>
              <tr>
                <td>statusCode</td>
                <td>number</td>
                <td>Required</td>
                <td>{{ $t("doc.gb8") }}</td>
              </tr>
              <tr>
                <td>timestampMillis</td>
                <td>number</td>
                <td>Required</td>
                <td>{{ $t("doc.gb9") }}</td>
              </tr>
           
              <tr>
                <td>productId</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb10") }}</td>
              </tr>
              <tr>
                <td>currency</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb11") }}</td>
              </tr>
              <tr>
                <td>balance</td>
                <td>number</td>
                <td>Required</td>
                <td>{{ $t("doc.gb12") }}</td>
              </tr>
              <tr>
                <td>username</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gb13") }}</td>
              </tr>
            </tbody>
          </n-table>
        </div>
      </div>

      <!-- Request Body  -->
      <div class="mt-4">
        <div class="font-bold mb-4">Request Body</div>
        <div class="bg-black/70 rounded-lg px-4 pt-4">
          <div class="absolute right-0 mr-10">
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button class="text-white" @click="copyToClipboard"
                  ><icon-park-outline-copy
                /></n-button>
              </template>
              {{ $t("copy") }}
            </n-tooltip>
          </div>
          <pre  ref="jsonContent" class="font-normal text-white overflow-x-auto">
{
  "id": "string",
  "timestampMillis": 0,
  "productId": "PGSOFT",
  "currency": "THB",
  "username": "string",
  "sessionToken": "string"
}
    </pre
          >
        </div>
      </div>

      <!-- JSON response example  -->
      <div class="mt-4">
        <div class="font-bold mb-4">JSON response example</div>
        <div class="bg-black/70 rounded-lg px-4 pt-4">
          <div class="absolute right-0 mr-10">
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button class="text-white" @click="copyToClipboard"
                  ><icon-park-outline-copy
                /></n-button>
              </template>
              {{ $t("copy") }}
            </n-tooltip>
          </div>
          <pre  ref="jsonContent" class="font-normal text-white overflow-x-auto">
{
    "id": "string",
    "statusCode": 0,
    "timestampMillis": "int",
    "productId": "PGSOFT",
    "currency": "THB",
    "balance": "Number",
    "username": "string"
}
    </pre
          >
        </div>
      </div>
    </n-card>
  </div>
</template>
