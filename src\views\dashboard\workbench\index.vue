<script setup lang="ts">
import { useAuthStore } from "@/store";
import Chart from "./components/chart.vue";

const { userInfo } = useAuthStore();
</script>

<template>
  <n-grid :x-gap="16" :y-gap="16">
    <n-gi :span="16">
      <n-space vertical :size="16">
        <n-card
          class="h-fit  w-full bg-center bg-cover border-2 border-black/10 rounded-2xl p-4"
        >
          <h3 class="text-[26px] font-bold">ประกาศ</h3>

          <div class="mt-3">
            <h4 class="text-md font-semibold text-blue-300">Features</h4>
            <ul class="text-sm  list-disc pl-5 leading-6">
              <li>
                Currency Convertor Box - choose which currency that you want to
                replace.
              </li>
              <li>
                Operator Report - remove sorting for player count and new player
                count.
              </li>
              <li>New Pages: Operator > Operator Hourly Hands Summary</li>
              <li>
                Free Game & Bonus Enhancement - Player name allowed "|"
                character.
              </li>
            </ul>
          </div>
        </n-card>
        <n-card style="--n-padding-left: 0">
          <Chart />
        </n-card>
        <n-card>
          <n-grid :x-gap="8" :y-gap="8">
            <n-gi :span="6">
              <n-card>
                <n-thing>
                  <template #avatar>
                    <n-el>
                      <n-icon-wrapper
                        :size="46"
                        color="var(--success-color)"
                        :border-radius="999"
                      >
                        <nova-icon :size="26" icon="icon-park-outline:user" />
                      </n-icon-wrapper>
                    </n-el>
                  </template>
                  <template #header>
                    <n-statistic label="ทดสอบ">
                      <n-number-animation
                        show-separator
                        :from="0"
                        :to="12039"
                      />
                    </n-statistic>
                  </template>
                </n-thing>
              </n-card>
            </n-gi>
            <n-gi :span="6">
              <n-card>
                <n-thing>
                  <template #avatar>
                    <n-el>
                      <n-icon-wrapper
                        :size="46"
                        color="var(--success-color)"
                        :border-radius="999"
                      >
                        <nova-icon
                          :size="26"
                          icon="icon-park-outline:every-user"
                        />
                      </n-icon-wrapper>
                    </n-el>
                  </template>
                  <template #header>
                    <n-statistic label="ทดสอบ">
                      <n-number-animation
                        show-separator
                        :from="0"
                        :to="44039"
                      />
                    </n-statistic>
                  </template>
                </n-thing>
              </n-card>
            </n-gi>
            <n-gi :span="6">
              <n-card>
                <n-thing>
                  <template #avatar>
                    <n-el>
                      <n-icon-wrapper
                        :size="46"
                        color="var(--success-color)"
                        :border-radius="999"
                      >
                        <nova-icon
                          :size="26"
                          icon="icon-park-outline:preview-open"
                        />
                      </n-icon-wrapper>
                    </n-el>
                  </template>
                  <template #header>
                    <n-statistic label="ทดสอบ">
                      <n-number-animation
                        show-separator
                        :from="0"
                        :to="551039"
                      />
                    </n-statistic>
                  </template>
                </n-thing>
              </n-card>
            </n-gi>
            <n-gi :span="6">
              <n-card>
                <n-thing>
                  <template #avatar>
                    <n-el>
                      <n-icon-wrapper
                        :size="46"
                        color="var(--success-color)"
                        :border-radius="999"
                      >
                        <nova-icon :size="26" icon="icon-park-outline:star" />
                      </n-icon-wrapper>
                    </n-el>
                  </template>
                  <template #header>
                    <n-statistic label="ทดสอบ">
                      <n-number-animation show-separator :from="0" :to="7739" />
                    </n-statistic>
                  </template>
                </n-thing>
              </n-card>
            </n-gi>
          </n-grid>
        </n-card>
      </n-space>
    </n-gi>
    <n-gi :span="8">
      <n-space vertical :size="16">
        <n-card title="ทดสอบ">
          <template #header-extra>
            <n-button type="primary" quaternary> ทดสอบ </n-button>
          </template>
          <n-list>
            <n-list-item>
              <template #prefix>
                <n-tag :bordered="false" type="info" size="small"> ทดสอบ </n-tag>
              </template>
              <n-button text> ทดสอบ </n-button>
            </n-list-item>
            <n-list-item>
              <template #prefix>
                <n-tag :bordered="false" type="success" size="small">
                  ทดสอบ
                </n-tag>
              </template>
              <n-button text> ทดสอบ </n-button>
            </n-list-item>
            <n-list-item>
              <template #prefix>
                <n-tag :bordered="false" type="warning" size="small">
                  ทดสอบ
                </n-tag>
              </template>
              <n-button text> ทดสอบ </n-button>
            </n-list-item>
          </n-list>
        </n-card>
        <n-grid :x-gap="8" :y-gap="8">
          <n-gi :span="12">
            <n-card>
              <n-flex vertical align="center">
                <n-text depth="3"> ทดสอบ </n-text>
                <n-icon-wrapper :size="46" :border-radius="999">
                  <nova-icon
                    :size="26"
                    icon="icon-park-outline:all-application"
                  />
                </n-icon-wrapper>
                <n-text strong class="text-2xl"> 1,234,123 </n-text>
              </n-flex>
            </n-card>
          </n-gi>
          <n-gi :span="12">
            <n-card>
              <n-flex vertical align="center">
                <n-text depth="3"> ทดสอบ </n-text>
                <n-el>
                  <n-icon-wrapper
                    :size="46"
                    color="var(--warning-color)"
                    :border-radius="999"
                  >
                    <nova-icon
                      :size="26"
                      icon="icon-park-outline:list-bottom"
                    />
                  </n-icon-wrapper>
                </n-el>
                <n-text strong class="text-2xl"> 78 </n-text>
              </n-flex>
            </n-card>
          </n-gi>
        </n-grid>
        <n-card title="ทดสอบ">
          <n-timeline>
            <n-timeline-item content="ทดสอบ" />
            <n-timeline-item
              type="success"
              title="ทดสอบ"
              content="ทดสอบ"
              time="2018-04-03 20:46"
            />
            <n-timeline-item
              type="error"
              content="ทดสอบ"
              time="2018-04-03 20:46"
            />
            <n-timeline-item
              type="warning"
              title="ทดสอบ"
              content="ทดสอบ"
              time="2018-04-03 20:46"
            />
            <n-timeline-item
              type="info"
              title="ทดสอบ"
              content="ทดสอบ"
              time="2018-04-03 20:46"
              line-type="dashed"
            />
            <n-timeline-item content="ทดสอบ" />
          </n-timeline>
        </n-card>
      </n-space>
    </n-gi>
  </n-grid>
</template>

<style scoped></style>
