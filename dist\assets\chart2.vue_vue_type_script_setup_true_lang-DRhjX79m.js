import{d as o,r as s,ai as i,aj as r,m,o as c}from"./index-pY9FjpQW.js";import"./index-Dx6UGq-y.js";const u={ref:"lineRef",class:"h-400px"},x=o({__name:"chart2",setup(p){const a=[{name:"1",value:300},{name:"2",value:400},{name:"3",value:452},{name:"4",value:770},{name:"5",value:650},{name:"6",value:256},{name:"7",value:350},{name:"8",value:422},{name:"9",value:235},{name:"10",value:658},{name:"11",value:600},{name:"12",value:400},{name:"13",value:523},{name:"14",value:482},{name:"15",value:423}],t=a.map(e=>e.name),l=a.map(e=>e.value),n=s({tooltip:{trigger:"axis"},grid:{left:"2%",right:"2%",bottom:"0%",top:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:t,axisTick:{show:!1},axisLine:{lineStyle:{color:"rgba(151,151,151,0.5)",type:"dashed"}},axisLabel:{margin:10,color:"#666",fontSize:14}},yAxis:{type:"value",splitLine:{lineStyle:{color:"rgba(151,151,151,0.5)",type:"dashed"}},axisLine:{lineStyle:{color:"rgba(151,151,151,0.5)",type:"dashed"}},axisTick:{show:!1},axisLabel:{show:!1}},series:[{type:"bar",barWidth:"20px",data:l,itemStyle:{color:new i(0,0,0,1,[{offset:0,color:"#00BD89"},{offset:1,color:"#C9F9E1"}],!1)}}]});return r("lineRef",n),(e,f)=>(c(),m("div",u,null,512))}});export{x as _};
