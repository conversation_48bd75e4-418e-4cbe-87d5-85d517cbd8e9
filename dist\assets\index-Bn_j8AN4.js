import{a7 as g,m as i,o as p,a as e,d as B,b as n,w as t,ab as C,af as H,ag as N,G as S,a8 as V,t as d,ah as D,g as k,B as E}from"./index-pY9FjpQW.js";import{_ as q}from"./chart.vue_vue_type_script_setup_true_lang-D1Zq9SXO.js";import{_ as A}from"./chart2.vue_vue_type_script_setup_true_lang-DRhjX79m.js";import{_ as F}from"./chart3.vue_vue_type_script_setup_true_lang-CIhaSmOi.js";import{_ as G,a as I,b as L,c as R}from"./Statistic-CVqPeFEG.js";import{_ as T,a as J}from"./Tabs-BOE8tUJg.js";import{_ as K}from"./Table-DoHSPnrC.js";import"./index-Dx6UGq-y.js";import"./toNumber-C1SyHx2r.js";const O={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function P(c,o){return p(),i("svg",O,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M44 24c0 11.046-8.954 20-20 20S4 35.046 4 24S12.954 4 24 4v20z"}),e("path",{d:"M43.084 18H30V4.916A20.05 20.05 0 0 1 43.084 18"})],-1)]))}const Q=g({name:"icon-park-outline-chart-pie",render:P}),U={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function W(c,o){return p(),i("svg",U,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M4 4v40h40"}),e("path",{d:"M10 38S15.313 4 27 4s17 34 17 34"}),e("path",{"stroke-dasharray":"2 6",d:"M10 24h34"})],-1)]))}const X=g({name:"icon-park-outline-average",render:W}),Y={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Z(c,o){return p(),i("svg",Y,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M17 6h14v9H17zM6 33h14v9H6zm22 0h14v9H28z"}),e("path",{"stroke-linecap":"round",d:"M24 16v8m-11 9v-9h22v9"})],-1)]))}const nn=g({name:"icon-park-outline-chart-graph",render:Z}),tn={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function en(c,o){return p(),i("svg",tn,o[0]||(o[0]=[e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"M6 6v36h36M14 30v4m8-12v12m8-28v28m8-20v20"},null,-1)]))}const on=g({name:"icon-park-outline-chart-histogram",render:en}),fn=B({__name:"index",setup(c){const o=[{id:0,name:"ยอดทั้งหมด",start:"2022-02-02",end:"2022-02-02",prograss:"100",status:"online"},{id:0,name:"ยอดทั้งหมด",start:"2022-02-02",end:"2022-02-02",prograss:"50",status:"online"},{id:0,name:"ยอดทั้งหมด",start:"2022-02-02",end:"2022-02-02",prograss:"100",status:"online"}];return(an,a)=>{const s=L,m=I,w=on,f=N,l=H,r=C,_=G,b=nn,y=X,v=Q,h=J,x=T,$=E,j=D,M=K,z=R;return p(),i("div",null,[n(z,{"x-gap":16,"y-gap":16},{default:t(()=>[n(_,{span:6},{default:t(()=>[n(r,null,{footer:t(()=>[n(l,{justify:"space-between"},{default:t(()=>[a[0]||(a[0]=e("span",null,"ยอดทั้งหมด",-1)),e("span",null,[n(s,{from:0,to:322039,"show-separator":""})])]),_:1})]),default:t(()=>[n(l,{justify:"space-between",align:"center"},{default:t(()=>[n(m,{label:"ยอดทั้งหมด"},{default:t(()=>[n(s,{from:0,to:12039,"show-separator":""})]),_:1}),n(f,{color:"#de4307",size:"42"},{default:t(()=>[n(w)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,{span:6},{default:t(()=>[n(r,null,{footer:t(()=>[n(l,{justify:"space-between"},{default:t(()=>[a[1]||(a[1]=e("span",null,"ยอดทั้งหมด",-1)),e("span",null,[n(s,{from:0,to:322039,"show-separator":""})])]),_:1})]),default:t(()=>[n(l,{justify:"space-between",align:"center"},{default:t(()=>[n(m,{label:"ยอดทั้งหมด"},{default:t(()=>[n(s,{from:0,to:12039,"show-separator":""})]),_:1}),n(f,{color:"#ffb549",size:"42"},{default:t(()=>[n(b)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,{span:6},{default:t(()=>[n(r,null,{footer:t(()=>[n(l,{justify:"space-between"},{default:t(()=>[a[2]||(a[2]=e("span",null,"ยอดทั้งหมด",-1)),e("span",null,[n(s,{from:0,to:322039,"show-separator":""})])]),_:1})]),default:t(()=>[n(l,{justify:"space-between",align:"center"},{default:t(()=>[n(m,{label:"ยอดทั้งหมด"},{default:t(()=>[n(s,{from:0,to:12039,"show-separator":""})]),_:1}),n(f,{color:"#1687a7",size:"42"},{default:t(()=>[n(y)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,{span:6},{default:t(()=>[n(r,null,{footer:t(()=>[n(l,{justify:"space-between"},{default:t(()=>[a[3]||(a[3]=e("span",null,"ยอดทั้งหมด",-1)),e("span",null,[n(s,{from:0,to:322039,"show-separator":""})])]),_:1})]),default:t(()=>[n(l,{justify:"space-between",align:"center"},{default:t(()=>[n(m,{label:"ยอดทั้งหมด"},{default:t(()=>[n(s,{from:0,to:12039,"show-separator":""})]),_:1}),n(f,{color:"#42218E",size:"42"},{default:t(()=>[n(v)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,{span:24},{default:t(()=>[n(r,{"content-style":"padding: 0;"},{default:t(()=>[n(x,{type:"line",size:"large","tabs-padding":20,"pane-style":"padding: 20px;"},{default:t(()=>[n(h,{name:"ยอดทั้งหมด"},{default:t(()=>[n(q)]),_:1}),n(h,{name:"ยอดทั้งหมด2"},{default:t(()=>[n(A)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,{span:8},{default:t(()=>[n(r,{title:"ยอดทั้งหมด",segmented:{content:!0}},{default:t(()=>[n(F)]),_:1})]),_:1}),n(_,{span:16},{default:t(()=>[n(r,{title:"ยอดทั้งหมด",segmented:{content:!0}},{"header-extra":t(()=>[n($,{type:"primary",quaternary:""},{default:t(()=>a[4]||(a[4]=[k(" ยอดทั้งหมด ")])),_:1})]),default:t(()=>[n(M,{bordered:!1,"single-line":!1},{default:t(()=>[a[5]||(a[5]=e("thead",null,[e("tr",null,[e("th",null,"ยอดทั้งหมด"),e("th",null,"ยอดทั้งหมด"),e("th",null,"ยอดทั้งหมด"),e("th",null,"ยอดทั้งหมด"),e("th",null,"ยอดทั้งหมด")])],-1)),e("tbody",null,[(p(),i(S,null,V(o,u=>e("tr",{key:u.id},[e("td",null,d(u.name),1),e("td",null,d(u.start),1),e("td",null,d(u.end),1),e("td",null,d(u.prograss)+"%",1),e("td",null,[n(j,{bordered:!1,type:"info"},{default:t(()=>[k(d(u.status),1)]),_:2},1024)])])),64))])]),_:1})]),_:1})]),_:1})]),_:1})])}}});export{fn as default};
