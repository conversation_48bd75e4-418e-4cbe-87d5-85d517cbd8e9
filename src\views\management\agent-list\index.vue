<script setup lang="tsx">
// @ts-nocheck
import http from "@/service/axios";
import moment from "moment-timezone";
import { NButton } from "naive-ui";
import { useAuthStore } from "@/store/auth";
import { log } from "console";
const { userData } = useAuthStore();
const router = useRouter();
const search = ref("");
const currency_type = ref("ALL");
const perPage = ref(10);
const Page = ref(1);
const total = ref(0);
const items = ref([]);
const loading = ref(false);
const { t } = useI18n();
const time = (value) => {
  return value
    ? moment(value).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm")
    : "-";
};
const currency = computed(() => [
  {
    label: t("all"),
    value: "ALL",
  },
  {
    label: "THB",
    value: "THB",
  },
  {
    label: "KRW",
    value: "KRW",
  },
  {
    label: "USD",
    value: "USD",
  },
]);
const checkPrime = () => {
  if (userData?.position_type == 4 && userData?.upline_position_type == 1) {
    const permis = JSON.parse(userData?.permissions);
    if (permis.setting !== 2) {
      return false;
    }
  }
  return true;
};
const columns = computed(() => {
  const baseColumns = [
    {
      title: t("no."),
      align: "center",
      key: "index",
      render: (row, index) => {
        return perPage.value * (Page.value - 1) + index + 1;
      },
    },

    {
      title: t("loginname"),
      align: "center",
      key: "username",
      render: (row) => {
        if (
          row.position_type &&
          (row.position_type == 2 || row.position_type == 3)
        ) {
          // <NTag type="primary" onCl >{row.username}</NTag>
          return (
            <n-button
              tertiary
              round
              type={
                row.position_type === 2
                  ? "warning"
                  : row.position_type === 3
                  ? "success"
                  : "info"
              }
              onClick={() => GetDataDetall(row.id)}
              style={{ cursor: "pointer" }}
            >
              {row.username}
            </n-button>
          );
        } else {
          return (
            <n-button
              tertiary
              round
              type={
                row.position_type === 2
                  ? "warning"
                  : row.position_type === 3
                  ? "success"
                  : "info"
              }
              style={{ cursor: "default" }}
            >
              {row.username}
            </n-button>
          );
        }
      },
    },
    {
      title: t("position"),
      align: "center",
      key: "position",
      render: (row) => {
        return (
          <n-button
            size="small"
            class="cursor-default"
            ghost
            type={
              row.position_type === 2
                ? "warning"
                : row.position_type === 3
                ? "success"
                : "info"
            }
          >
            {row.position_type === 2
              ? t("reseller")
              : row.position_type === 3
              ? t("agent")
              : t("member")}
          </n-button>
        );
      },
    },
    {
      title: t("currency"),
      align: "center",
      key: "currency",
      render: (item) => {
        let text = "";
        if (item.position_type == 3) {
          text = item.ag_currency;
        } else {
          if (item.reseller_thb == 1) {
            text = "THB";
          }
          if (item.reseller_krw == 1) {
            if (text) {
              text += ",KRW";
            } else {
              text = "KRW";
            }
          }
          if (item.reseller_usd == 1) {
            if (text) {
              text += ",USD";
            } else {
              text = "USD";
            }
          }
        }
        if (!text && item.currency) {
          text = item.currency;
        }
        return text;
      },
    },
    {
      title: t("wallettype"),
      align: "center",
      key: "wallet_type",
      render: (row) => {
        return row.bet_type && row.bet_type == 1
          ? "Seamless"
          : row.bet_type && row.bet_type == 1
          ? "Tranfer"
          : "-";
      },
    },
    {
      title: t("createdate"),
      align: "center",
      key: "created_at",
      render: (row) => {
        return time(row.created_at);
      },
    },
    {
      title: t("lastlogindate"),
      align: "center",
      key: "last_login",
      render: (row) => {
        return time(row.last_login);
      },
    },
    {
      title: t("lastloginip"),
      align: "center",
      key: "last_ip",
      render: (row) => {
        return row.last_ip || "-";
      },
    },
    {
      title: t("status"),
      align: "center",
      key: "status",
      render: (row) => {
        return (
          <n-button
            class="cursor-default"
            ghost
                       type={row.status === 1 || row.status == true ? "success" : row.status === 2 ? "warning" : "error"}
            size="small"
          >
            {row.status === 1 || row.status == true
              ? t("active")
              : row.status === 2
              ? t("suspend")
              : t("inactive")}
          </n-button>
        );
      },
    },
  ];

  if (userData.position_type == 2 || userData.position_type == 1) {
    baseColumns.push({
      title: t("edit"),
      align: "center",
      key: "actions",
      render: (row) => {
        if (row.position_type) {
          if (
            (userData.position_type == 4 &&
              userData.upline_position_type == 1) ||
            (userData.position_type == 4 &&
              userData.upline_position_type == 2) ||
            (userData.position_type == 4 && userData.upline_position_type == 3)
          ) {
            const permis = JSON.parse(userData?.permissions);
            if (permis.management !== 2) {
              return (
                <n-button
                  class="cursor-default"
                  tertiary
                  type="error"
                  size="small"
                >
                  <icon-park-outline-close />
                </n-button>
              );
            }
          }

          return h(
            NButton,
            {
              type: "primary",
              size: "small",
              secondary: true,
              onClick: () => Edit(row),
            },
            { default: () => <icon-park-outline-write /> }
          );
        }
        return (
          <n-button class="cursor-default" tertiary type="error" size="small">
            <icon-park-outline-close />
          </n-button>
        );
      },
    });
  }
  return baseColumns;
});
const Userlist = ref([]);
const AgentDetail = ref(null);
function Edit(row) {
  router.push({ name: "edit-agent", params: { id: row.id } });
}
onMounted(() => {
  GetAgent();
  GetData();
});
const GetAgent = () => {
  http.get("v1/Profile/agent").then((response) => {
    if (response.data.success) {
      AgentDetail.value = response.data.data;
    }
  });
};
const GetData = async () => {
  loading.value = true;
  const params = {
    currency: currency_type.value,
    perPage: perPage.value,
    page: Page.value,
    search: search.value,
  };
  const { data: res } = await http.get("v1/Account/list", { params });
  items.value = res.data;
  total.value = res.total;
  Userlist.value = [];
  loading.value = false;
};
const searchItems = () => {
  if (Userlist.value.length && Userlist.value.length > 1) {
    const Data = Userlist.value[Userlist.value.length - 1];
    GetDataDetall(Data.id);
  } else {
    GetData();
  }
};

const GetDataDetall = async (id) => {
  loading.value = true;
  const params = {
    currency: currency_type.value,
    perPage: perPage.value,
    page: Page.value,
    id,
    search: search.value,
  };
  const { data: res } = await http.get("v1/Account/list/detall", { params });
  items.value = res.data;
  total.value = res.total;
  if (Userlist.value.length === 0) {
    const obj = {
      id: AgentDetail.value.id,
      username: AgentDetail.value.username,
      position_type: AgentDetail.value.position_type,
    };
    Userlist.value.push(obj);
  }
  loading.value = false;
  if (res.user && !Userlist.value.some((user) => user.id === res.user.id)) {
    Userlist.value.push(res.user);
    console.log(res);
    loading.value = false;
  } else {
    const index = Userlist.value.findIndex((user) => user.id === res.user.id);
    if (index !== -1 && index + 1 < Userlist.value.length) {
      Userlist.value.splice(index + 1);
    }
    loading.value = false;
  }
};
const message = useMessage();
function changePage(page: number, size: number) {
  window.$message.success(`${t("gotopage")} : ${page}/${size}`);
  perPage.value = size;
  Page.value = page;
  GetData();
}
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <div
          class="flex gap-3 flex-col lg:flex-row justify-between items-start"
        >
          <div class="font-medium text-[1rem]">{{ $t("agentlist") }}</div>
          <div class="flex items-center flex-col sm:flex-row gap-3">
            <n-form-item
              :label="$t('loginname')"
              label-placement="left"
              :show-feedback="false"
              class="w-full sm:w-72"
            >
              <n-input
                v-model:value="search"
                type="text"
                :placeholder="$t('loginname')"
              />
            </n-form-item>
            <n-form-item
              :label="$t('currency')"
              label-placement="left"
              :show-feedback="false"
              class="w-full sm:w-40"
            >
              <n-select v-model:value="currency_type" :options="currency" />
            </n-form-item>
            <n-form-item label-placement="left" :show-feedback="false">
              <n-button type="primary" @click="searchItems()">
                <icon-park-outline-search />
                <span class="ml-2">{{ $t("search") }}</span>
              </n-button>
            </n-form-item>
          </div>
        </div>

        <n-card v-if="Userlist.length">
          <span v-for="(item, index) of Userlist" :key="index">
            <n-button
              tertiary
              round
              :type="
                item.position_type === 1 ||
                (userData?.position_type == 4 &&
                  userData?.upline_position_type == 1)
                  ? 'error'
                  : item.position_type === 2 ||
                    (userData?.position_type == 4 &&
                      userData?.upline_position_type == 2)
                  ? 'warning'
                  : item.position_type === 3 ||
                    (userData?.position_type == 4 &&
                      userData?.upline_position_type == 3)
                  ? 'success'
                  : 'default'
              "
              class="mx-2 my-1"
              v-if="index == 0"
              @click="GetData()"
              >{{ item.username }}
            </n-button>

            <n-button
              round
              tertiary
              :type="
                item.position_type === 1
                  ? 'error'
                  : item.position_type === 2
                  ? 'warning'
                  : item.position_type === 3
                  ? 'success'
                  : 'info'
              "
              class="mx-2 my-1"
              v-if="index > 0"
              @click="GetDataDetall(item.id)"
            >
              {{ item.username }}</n-button
            >
          </span>
        </n-card>

        <n-data-table
          :scroll-x="800"
          :loading="loading"
          :columns="columns"
          :data="items"
          :bordered="false"
        />
        <Pagination
          class="overflow-auto float-right"
          :count="total"
          @change="changePage"
        />
      </n-space>
    </n-card>
  </div>
</template>
