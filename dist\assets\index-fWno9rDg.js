import{fn as J,x as Q,fo as X,C as s,d as P,V as E,z as S,fp as Y,U as O,W as I,fq as V,q as l,A as h,fr as Z,fs as ee,ft as x,fu as f,fv as N,fw as te,a6 as L,K as ne,y as M,S as K,fx as q,fy as ie,G as re,fz as W,a1 as D,fA as T,fB as oe,c as le,o as ae,w as i,b as t,af as se,ab as ce,a as w,j as de,fC as ue,B as me,g as v,ah as fe,fD as pe}from"./index-pY9FjpQW.js";import{_ as ge}from"./chart.vue_vue_type_script_setup_true_lang-TCmpaPAr.js";import{_ as _e,c as he,a as ve,b as be}from"./Statistic-CVqPeFEG.js";import{_ as xe}from"./text-GY30jg6U.js";import"./toNumber-C1SyHx2r.js";let H=!1;function ze(){if(J&&window.CSS&&!H&&(H=!0,"registerProperty"in window?.CSS))try{CSS.registerProperty({name:"--n-color-start",syntax:"<color>",inherits:!1,initialValue:"#0000"}),CSS.registerProperty({name:"--n-color-end",syntax:"<color>",inherits:!1,initialValue:"#0000"})}catch{}}function ye(e){const{textColor3:n,infoColor:a,errorColor:r,successColor:o,warningColor:u,textColor1:m,textColor2:d,railColor:p,fontWeightStrong:g,fontSize:c}=e;return Object.assign(Object.assign({},X),{contentFontSize:c,titleFontWeight:g,circleBorder:`2px solid ${n}`,circleBorderInfo:`2px solid ${a}`,circleBorderError:`2px solid ${r}`,circleBorderSuccess:`2px solid ${o}`,circleBorderWarning:`2px solid ${u}`,iconColor:n,iconColorInfo:a,iconColorError:r,iconColorSuccess:o,iconColorWarning:u,titleTextColor:m,contentTextColor:d,metaTextColor:n,lineColor:p})}const Ce={common:Q,self:ye},$e=s("icon-wrapper",`
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 background-color: var(--n-color);
 display: inline-flex;
 align-items: center;
 justify-content: center;
 color: var(--n-icon-color);
`),we=Object.assign(Object.assign({},S.props),{size:{type:Number,default:24},borderRadius:{type:Number,default:6},color:String,iconColor:String}),Se=P({name:"IconWrapper",props:we,setup(e,{slots:n}){const{mergedClsPrefixRef:a,inlineThemeDisabled:r}=E(e),o=S("IconWrapper","-icon-wrapper",$e,Y,e,a),u=O(()=>{const{common:{cubicBezierEaseInOut:d},self:{color:p,iconColor:g}}=o.value;return{"--n-bezier":d,"--n-color":p,"--n-icon-color":g}}),m=r?I("icon-wrapper",void 0,u,e):void 0;return()=>{const d=V(e.size);return m?.onRender(),l("div",{class:[`${a.value}-icon-wrapper`,m?.themeClass.value],style:[u?.value,{height:d,width:d,borderRadius:V(e.borderRadius),backgroundColor:e.color,color:e.iconColor}]},n)}}}),Re=h([s("list",`
 --n-merged-border-color: var(--n-border-color);
 --n-merged-color: var(--n-color);
 --n-merged-color-hover: var(--n-color-hover);
 margin: 0;
 font-size: var(--n-font-size);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 padding: 0;
 list-style-type: none;
 color: var(--n-text-color);
 background-color: var(--n-merged-color);
 `,[x("show-divider",[s("list-item",[h("&:not(:last-child)",[f("divider",`
 background-color: var(--n-merged-border-color);
 `)])])]),x("clickable",[s("list-item",`
 cursor: pointer;
 `)]),x("bordered",`
 border: 1px solid var(--n-merged-border-color);
 border-radius: var(--n-border-radius);
 `),x("hoverable",[s("list-item",`
 border-radius: var(--n-border-radius);
 `,[h("&:hover",`
 background-color: var(--n-merged-color-hover);
 `,[f("divider",`
 background-color: transparent;
 `)])])]),x("bordered, hoverable",[s("list-item",`
 padding: 12px 20px;
 `),f("header, footer",`
 padding: 12px 20px;
 `)]),f("header, footer",`
 padding: 12px 0;
 box-sizing: border-box;
 transition: border-color .3s var(--n-bezier);
 `,[h("&:not(:last-child)",`
 border-bottom: 1px solid var(--n-merged-border-color);
 `)]),s("list-item",`
 position: relative;
 padding: 12px 0; 
 box-sizing: border-box;
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[f("prefix",`
 margin-right: 20px;
 flex: 0;
 `),f("suffix",`
 margin-left: 20px;
 flex: 0;
 `),f("main",`
 flex: 1;
 `),f("divider",`
 height: 1px;
 position: absolute;
 bottom: 0;
 left: 0;
 right: 0;
 background-color: transparent;
 transition: background-color .3s var(--n-bezier);
 pointer-events: none;
 `)])]),Z(s("list",`
 --n-merged-color-hover: var(--n-color-hover-modal);
 --n-merged-color: var(--n-color-modal);
 --n-merged-border-color: var(--n-border-color-modal);
 `)),ee(s("list",`
 --n-merged-color-hover: var(--n-color-hover-popover);
 --n-merged-color: var(--n-color-popover);
 --n-merged-border-color: var(--n-border-color-popover);
 `))]),ke=Object.assign(Object.assign({},S.props),{size:{type:String,default:"medium"},bordered:Boolean,clickable:Boolean,hoverable:Boolean,showDivider:{type:Boolean,default:!0}}),A=M("n-list"),Pe=P({name:"List",props:ke,slots:Object,setup(e){const{mergedClsPrefixRef:n,inlineThemeDisabled:a,mergedRtlRef:r}=E(e),o=N("List",r,n),u=S("List","-list",Re,te,e,n);L(A,{showDividerRef:ne(e,"showDivider"),mergedClsPrefixRef:n});const m=O(()=>{const{common:{cubicBezierEaseInOut:p},self:{fontSize:g,textColor:c,color:_,colorModal:z,colorPopover:y,borderColor:C,borderColorModal:R,borderColorPopover:k,borderRadius:j,colorHover:$,colorHoverModal:B,colorHoverPopover:b}}=u.value;return{"--n-font-size":g,"--n-bezier":p,"--n-text-color":c,"--n-color":_,"--n-border-radius":j,"--n-border-color":C,"--n-border-color-modal":R,"--n-border-color-popover":k,"--n-color-modal":z,"--n-color-popover":y,"--n-color-hover":$,"--n-color-hover-modal":B,"--n-color-hover-popover":b}}),d=a?I("list",void 0,m,e):void 0;return{mergedClsPrefix:n,rtlEnabled:o,cssVars:a?void 0:m,themeClass:d?.themeClass,onRender:d?.onRender}},render(){var e;const{$slots:n,mergedClsPrefix:a,onRender:r}=this;return r?.(),l("ul",{class:[`${a}-list`,this.rtlEnabled&&`${a}-list--rtl`,this.bordered&&`${a}-list--bordered`,this.showDivider&&`${a}-list--show-divider`,this.hoverable&&`${a}-list--hoverable`,this.clickable&&`${a}-list--clickable`,this.themeClass],style:this.cssVars},n.header?l("div",{class:`${a}-list__header`},n.header()):null,(e=n.default)===null||e===void 0?void 0:e.call(n),n.footer?l("div",{class:`${a}-list__footer`},n.footer()):null)}}),Be=P({name:"ListItem",slots:Object,setup(){const e=K(A,null);return e||q("list-item","`n-list-item` must be placed in `n-list`."),{showDivider:e.showDividerRef,mergedClsPrefix:e.mergedClsPrefixRef}},render(){const{$slots:e,mergedClsPrefix:n}=this;return l("li",{class:`${n}-list-item`},e.prefix?l("div",{class:`${n}-list-item__prefix`},e.prefix()):null,e.default?l("div",{class:`${n}-list-item__main`},e):null,e.suffix?l("div",{class:`${n}-list-item__suffix`},e.suffix()):null,this.showDivider&&l("div",{class:`${n}-list-item__divider`}))}}),je=s("thing",`
 display: flex;
 transition: color .3s var(--n-bezier);
 font-size: var(--n-font-size);
 color: var(--n-text-color);
`,[s("thing-avatar",`
 margin-right: 12px;
 margin-top: 2px;
 `),s("thing-avatar-header-wrapper",`
 display: flex;
 flex-wrap: nowrap;
 `,[s("thing-header-wrapper",`
 flex: 1;
 `)]),s("thing-main",`
 flex-grow: 1;
 `,[s("thing-header",`
 display: flex;
 margin-bottom: 4px;
 justify-content: space-between;
 align-items: center;
 `,[f("title",`
 font-size: 16px;
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 color: var(--n-title-text-color);
 `)]),f("description",[h("&:not(:last-child)",`
 margin-bottom: 4px;
 `)]),f("content",[h("&:not(:first-child)",`
 margin-top: 12px;
 `)]),f("footer",[h("&:not(:first-child)",`
 margin-top: 12px;
 `)]),f("action",[h("&:not(:first-child)",`
 margin-top: 12px;
 `)])])]),Te=Object.assign(Object.assign({},S.props),{title:String,titleExtra:String,description:String,descriptionClass:String,descriptionStyle:[String,Object],content:String,contentClass:String,contentStyle:[String,Object],contentIndented:Boolean}),Oe=P({name:"Thing",props:Te,slots:Object,setup(e,{slots:n}){const{mergedClsPrefixRef:a,inlineThemeDisabled:r,mergedRtlRef:o}=E(e),u=S("Thing","-thing",je,ie,e,a),m=N("Thing",o,a),d=O(()=>{const{self:{titleTextColor:g,textColor:c,titleFontWeight:_,fontSize:z},common:{cubicBezierEaseInOut:y}}=u.value;return{"--n-bezier":y,"--n-font-size":z,"--n-text-color":c,"--n-title-font-weight":_,"--n-title-text-color":g}}),p=r?I("thing",void 0,d,e):void 0;return()=>{var g;const{value:c}=a,_=m?m.value:!1;return(g=p?.onRender)===null||g===void 0||g.call(p),l("div",{class:[`${c}-thing`,p?.themeClass,_&&`${c}-thing--rtl`],style:r?void 0:d.value},n.avatar&&e.contentIndented?l("div",{class:`${c}-thing-avatar`},n.avatar()):null,l("div",{class:`${c}-thing-main`},!e.contentIndented&&(n.header||e.title||n["header-extra"]||e.titleExtra||n.avatar)?l("div",{class:`${c}-thing-avatar-header-wrapper`},n.avatar?l("div",{class:`${c}-thing-avatar`},n.avatar()):null,n.header||e.title||n["header-extra"]||e.titleExtra?l("div",{class:`${c}-thing-header-wrapper`},l("div",{class:`${c}-thing-header`},n.header||e.title?l("div",{class:`${c}-thing-header__title`},n.header?n.header():e.title):null,n["header-extra"]||e.titleExtra?l("div",{class:`${c}-thing-header__extra`},n["header-extra"]?n["header-extra"]():e.titleExtra):null),n.description||e.description?l("div",{class:[`${c}-thing-main__description`,e.descriptionClass],style:e.descriptionStyle},n.description?n.description():e.description):null):null):l(re,null,n.header||e.title||n["header-extra"]||e.titleExtra?l("div",{class:`${c}-thing-header`},n.header||e.title?l("div",{class:`${c}-thing-header__title`},n.header?n.header():e.title):null,n["header-extra"]||e.titleExtra?l("div",{class:`${c}-thing-header__extra`},n["header-extra"]?n["header-extra"]():e.titleExtra):null):null,n.description||e.description?l("div",{class:[`${c}-thing-main__description`,e.descriptionClass],style:e.descriptionStyle},n.description?n.description():e.description):null),n.default||e.content?l("div",{class:[`${c}-thing-main__content`,e.contentClass],style:e.contentStyle},n.default?n.default():e.content):null,n.footer?l("div",{class:`${c}-thing-main__footer`},n.footer()):null,n.action?l("div",{class:`${c}-thing-main__action`},n.action()):null))}}}),F=1.25,Ee=s("timeline",`
 position: relative;
 width: 100%;
 display: flex;
 flex-direction: column;
 line-height: ${F};
`,[x("horizontal",`
 flex-direction: row;
 `,[h(">",[s("timeline-item",`
 flex-shrink: 0;
 padding-right: 40px;
 `,[x("dashed-line-type",[h(">",[s("timeline-item-timeline",[f("line",`
 background-image: linear-gradient(90deg, var(--n-color-start), var(--n-color-start) 50%, transparent 50%, transparent 100%);
 background-size: 10px 1px;
 `)])])]),h(">",[s("timeline-item-content",`
 margin-top: calc(var(--n-icon-size) + 12px);
 `,[h(">",[f("meta",`
 margin-top: 6px;
 margin-bottom: unset;
 `)])]),s("timeline-item-timeline",`
 width: 100%;
 height: calc(var(--n-icon-size) + 12px);
 `,[f("line",`
 left: var(--n-icon-size);
 top: calc(var(--n-icon-size) / 2 - 1px);
 right: 0px;
 width: unset;
 height: 2px;
 `)])])])])]),x("right-placement",[s("timeline-item",[s("timeline-item-content",`
 text-align: right;
 margin-right: calc(var(--n-icon-size) + 12px);
 `),s("timeline-item-timeline",`
 width: var(--n-icon-size);
 right: 0;
 `)])]),x("left-placement",[s("timeline-item",[s("timeline-item-content",`
 margin-left: calc(var(--n-icon-size) + 12px);
 `),s("timeline-item-timeline",`
 left: 0;
 `)])]),s("timeline-item",`
 position: relative;
 `,[h("&:last-child",[s("timeline-item-timeline",[f("line",`
 display: none;
 `)]),s("timeline-item-content",[f("meta",`
 margin-bottom: 0;
 `)])]),s("timeline-item-content",[f("title",`
 margin: var(--n-title-margin);
 font-size: var(--n-title-font-size);
 transition: color .3s var(--n-bezier);
 font-weight: var(--n-title-font-weight);
 color: var(--n-title-text-color);
 `),f("content",`
 transition: color .3s var(--n-bezier);
 font-size: var(--n-content-font-size);
 color: var(--n-content-text-color);
 `),f("meta",`
 transition: color .3s var(--n-bezier);
 font-size: 12px;
 margin-top: 6px;
 margin-bottom: 20px;
 color: var(--n-meta-text-color);
 `)]),x("dashed-line-type",[s("timeline-item-timeline",[f("line",`
 --n-color-start: var(--n-line-color);
 transition: --n-color-start .3s var(--n-bezier);
 background-color: transparent;
 background-image: linear-gradient(180deg, var(--n-color-start), var(--n-color-start) 50%, transparent 50%, transparent 100%);
 background-size: 1px 10px;
 `)])]),s("timeline-item-timeline",`
 width: calc(var(--n-icon-size) + 12px);
 position: absolute;
 top: calc(var(--n-title-font-size) * ${F} / 2 - var(--n-icon-size) / 2);
 height: 100%;
 `,[f("circle",`
 border: var(--n-circle-border);
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 width: var(--n-icon-size);
 height: var(--n-icon-size);
 border-radius: var(--n-icon-size);
 box-sizing: border-box;
 `),f("icon",`
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 display: flex;
 align-items: center;
 justify-content: center;
 `),f("line",`
 transition: background-color .3s var(--n-bezier);
 position: absolute;
 top: var(--n-icon-size);
 left: calc(var(--n-icon-size) / 2 - 1px);
 bottom: 0px;
 width: 2px;
 background-color: var(--n-line-color);
 `)])])]),Ie=Object.assign(Object.assign({},S.props),{horizontal:Boolean,itemPlacement:{type:String,default:"left"},size:{type:String,default:"medium"},iconSize:Number}),G=M("n-timeline"),Ve=P({name:"Timeline",props:Ie,setup(e,{slots:n}){const{mergedClsPrefixRef:a}=E(e),r=S("Timeline","-timeline",Ee,Ce,e,a);return L(G,{props:e,mergedThemeRef:r,mergedClsPrefixRef:a}),()=>{const{value:o}=a;return l("div",{class:[`${o}-timeline`,e.horizontal&&`${o}-timeline--horizontal`,`${o}-timeline--${e.size}-size`,!e.horizontal&&`${o}-timeline--${e.itemPlacement}-placement`]},n)}}}),We={time:[String,Number],title:String,content:String,color:String,lineType:{type:String,default:"default"},type:{type:String,default:"default"}},De=P({name:"TimelineItem",props:We,slots:Object,setup(e){const n=K(G);n||q("timeline-item","`n-timeline-item` must be placed inside `n-timeline`."),ze();const{inlineThemeDisabled:a}=E(),r=O(()=>{const{props:{size:u,iconSize:m},mergedThemeRef:d}=n,{type:p}=e,{self:{titleTextColor:g,contentTextColor:c,metaTextColor:_,lineColor:z,titleFontWeight:y,contentFontSize:C,[T("iconSize",u)]:R,[T("titleMargin",u)]:k,[T("titleFontSize",u)]:j,[T("circleBorder",p)]:$,[T("iconColor",p)]:B},common:{cubicBezierEaseInOut:b}}=d.value;return{"--n-bezier":b,"--n-circle-border":$,"--n-icon-color":B,"--n-content-font-size":C,"--n-content-text-color":c,"--n-line-color":z,"--n-meta-text-color":_,"--n-title-font-size":j,"--n-title-font-weight":y,"--n-title-margin":k,"--n-title-text-color":g,"--n-icon-size":V(m)||R}}),o=a?I("timeline-item",O(()=>{const{props:{size:u,iconSize:m}}=n,{type:d}=e;return`${u[0]}${m||"a"}${d[0]}`}),r,n.props):void 0;return{mergedClsPrefix:n.mergedClsPrefixRef,cssVars:a?void 0:r,themeClass:o?.themeClass,onRender:o?.onRender}},render(){const{mergedClsPrefix:e,color:n,onRender:a,$slots:r}=this;return a?.(),l("div",{class:[`${e}-timeline-item`,this.themeClass,`${e}-timeline-item--${this.type}-type`,`${e}-timeline-item--${this.lineType}-line-type`],style:this.cssVars},l("div",{class:`${e}-timeline-item-timeline`},l("div",{class:`${e}-timeline-item-timeline__line`}),W(r.icon,o=>o?l("div",{class:`${e}-timeline-item-timeline__icon`,style:{color:n}},o):l("div",{class:`${e}-timeline-item-timeline__circle`,style:{borderColor:n}}))),l("div",{class:`${e}-timeline-item-content`},W(r.header,o=>o||this.title?l("div",{class:`${e}-timeline-item-content__title`},o||this.title):null),l("div",{class:`${e}-timeline-item-content__content`},D(r.default,()=>[this.content])),l("div",{class:`${e}-timeline-item-content__meta`},D(r.footer,()=>[this.time]))))}}),Ke=P({__name:"index",setup(e){const{userInfo:n}=oe();return(a,r)=>{const o=ce,u=ue,m=Se,d=de,p=be,g=ve,c=Oe,_=_e,z=he,y=se,C=me,R=fe,k=Be,j=Pe,$=xe,B=pe,b=De,U=Ve;return ae(),le(z,{"x-gap":16,"y-gap":16},{default:i(()=>[t(_,{span:16},{default:i(()=>[t(y,{vertical:"",size:16},{default:i(()=>[t(o,{class:"h-fit w-full bg-center bg-cover border-2 border-black/10 rounded-2xl p-4"},{default:i(()=>r[0]||(r[0]=[w("h3",{class:"text-[26px] font-bold"},"ประกาศ",-1),w("div",{class:"mt-3"},[w("h4",{class:"text-md font-semibold text-blue-300"},"Features"),w("ul",{class:"text-sm list-disc pl-5 leading-6"},[w("li",null," Currency Convertor Box - choose which currency that you want to replace. "),w("li",null," Operator Report - remove sorting for player count and new player count. "),w("li",null,"New Pages: Operator > Operator Hourly Hands Summary"),w("li",null,' Free Game & Bonus Enhancement - Player name allowed "|" character. ')])],-1)])),_:1}),t(o,{style:{"--n-padding-left":"0"}},{default:i(()=>[t(ge)]),_:1}),t(o,null,{default:i(()=>[t(z,{"x-gap":8,"y-gap":8},{default:i(()=>[t(_,{span:6},{default:i(()=>[t(o,null,{default:i(()=>[t(c,null,{avatar:i(()=>[t(d,null,{default:i(()=>[t(m,{size:46,color:"var(--success-color)","border-radius":999},{default:i(()=>[t(u,{size:26,icon:"icon-park-outline:user"})]),_:1})]),_:1})]),header:i(()=>[t(g,{label:"ทดสอบ"},{default:i(()=>[t(p,{"show-separator":"",from:0,to:12039})]),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:6},{default:i(()=>[t(o,null,{default:i(()=>[t(c,null,{avatar:i(()=>[t(d,null,{default:i(()=>[t(m,{size:46,color:"var(--success-color)","border-radius":999},{default:i(()=>[t(u,{size:26,icon:"icon-park-outline:every-user"})]),_:1})]),_:1})]),header:i(()=>[t(g,{label:"ทดสอบ"},{default:i(()=>[t(p,{"show-separator":"",from:0,to:44039})]),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:6},{default:i(()=>[t(o,null,{default:i(()=>[t(c,null,{avatar:i(()=>[t(d,null,{default:i(()=>[t(m,{size:46,color:"var(--success-color)","border-radius":999},{default:i(()=>[t(u,{size:26,icon:"icon-park-outline:preview-open"})]),_:1})]),_:1})]),header:i(()=>[t(g,{label:"ทดสอบ"},{default:i(()=>[t(p,{"show-separator":"",from:0,to:551039})]),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:6},{default:i(()=>[t(o,null,{default:i(()=>[t(c,null,{avatar:i(()=>[t(d,null,{default:i(()=>[t(m,{size:46,color:"var(--success-color)","border-radius":999},{default:i(()=>[t(u,{size:26,icon:"icon-park-outline:star"})]),_:1})]),_:1})]),header:i(()=>[t(g,{label:"ทดสอบ"},{default:i(()=>[t(p,{"show-separator":"",from:0,to:7739})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:8},{default:i(()=>[t(y,{vertical:"",size:16},{default:i(()=>[t(o,{title:"ทดสอบ"},{"header-extra":i(()=>[t(C,{type:"primary",quaternary:""},{default:i(()=>r[1]||(r[1]=[v(" ทดสอบ ")])),_:1})]),default:i(()=>[t(j,null,{default:i(()=>[t(k,null,{prefix:i(()=>[t(R,{bordered:!1,type:"info",size:"small"},{default:i(()=>r[2]||(r[2]=[v(" ทดสอบ ")])),_:1})]),default:i(()=>[t(C,{text:""},{default:i(()=>r[3]||(r[3]=[v(" ทดสอบ ")])),_:1})]),_:1}),t(k,null,{prefix:i(()=>[t(R,{bordered:!1,type:"success",size:"small"},{default:i(()=>r[4]||(r[4]=[v(" ทดสอบ ")])),_:1})]),default:i(()=>[t(C,{text:""},{default:i(()=>r[5]||(r[5]=[v(" ทดสอบ ")])),_:1})]),_:1}),t(k,null,{prefix:i(()=>[t(R,{bordered:!1,type:"warning",size:"small"},{default:i(()=>r[6]||(r[6]=[v(" ทดสอบ ")])),_:1})]),default:i(()=>[t(C,{text:""},{default:i(()=>r[7]||(r[7]=[v(" ทดสอบ ")])),_:1})]),_:1})]),_:1})]),_:1}),t(z,{"x-gap":8,"y-gap":8},{default:i(()=>[t(_,{span:12},{default:i(()=>[t(o,null,{default:i(()=>[t(B,{vertical:"",align:"center"},{default:i(()=>[t($,{depth:"3"},{default:i(()=>r[8]||(r[8]=[v(" ทดสอบ ")])),_:1}),t(m,{size:46,"border-radius":999},{default:i(()=>[t(u,{size:26,icon:"icon-park-outline:all-application"})]),_:1}),t($,{strong:"",class:"text-2xl"},{default:i(()=>r[9]||(r[9]=[v(" 1,234,123 ")])),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:12},{default:i(()=>[t(o,null,{default:i(()=>[t(B,{vertical:"",align:"center"},{default:i(()=>[t($,{depth:"3"},{default:i(()=>r[10]||(r[10]=[v(" ทดสอบ ")])),_:1}),t(d,null,{default:i(()=>[t(m,{size:46,color:"var(--warning-color)","border-radius":999},{default:i(()=>[t(u,{size:26,icon:"icon-park-outline:list-bottom"})]),_:1})]),_:1}),t($,{strong:"",class:"text-2xl"},{default:i(()=>r[11]||(r[11]=[v(" 78 ")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(o,{title:"ทดสอบ"},{default:i(()=>[t(U,null,{default:i(()=>[t(b,{content:"ทดสอบ"}),t(b,{type:"success",title:"ทดสอบ",content:"ทดสอบ",time:"2018-04-03 20:46"}),t(b,{type:"error",content:"ทดสอบ",time:"2018-04-03 20:46"}),t(b,{type:"warning",title:"ทดสอบ",content:"ทดสอบ",time:"2018-04-03 20:46"}),t(b,{type:"info",title:"ทดสอบ",content:"ทดสอบ",time:"2018-04-03 20:46","line-type":"dashed"}),t(b,{content:"ทดสอบ"})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}});export{Ke as default};
