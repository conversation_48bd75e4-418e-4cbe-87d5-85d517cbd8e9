import{d as g,u as x,r as h,c as r,o as i,w as t,a as n,b as o,_ as w,e as v,f as a,g as C,t as b,T as y,h as N,i as V,j as k}from"./index-pY9FjpQW.js";import{_ as S}from"./index.vue_vue_type_script_setup_true_lang-DwEsXleN.js";import{_ as B}from"./index.vue_vue_type_script_setup_true_lang-DnIVhGcE.js";import{_ as T}from"./index.vue_vue_type_script_setup_true_lang-DtbfWLYp.js";import{N as $}from"./headers-CSI__REg.js";import"./preview-open-D9wsvucx.js";import"./preview-close-one-CsVAe9aK.js";import"./Form-_sVFK3VR.js";import"./FormItem-B4P0FvHJ.js";import"./Checkbox-CwEpY3xE.js";import"./text-GY30jg6U.js";const D={class:"fixed top-40px right-5px sm:right-40px text-lg"},A={class:"w-full flex flex-col items-center mt-10"},F=["src"],G=g({__name:"index",setup(M){const p=x(),e=h("login"),m={login:S,register:B,resetPwd:T},c="BACK OFFICE";return(j,s)=>{const _=w,d=v,u=$,l=k;return i(),r(l,{class:"wh-full flex-center",style:{"background-color":"var(--body-color)"}},{default:t(()=>[n("div",D,[o(_),o(d)]),o(l,{class:"p-4xl h-full w-full rounded-12px sm:w-450px sm:h-700px",style:{background:"var(--card-color)","box-shadow":"var(--box-shadow-1)"}},{default:t(()=>[n("div",A,[n("img",{src:a(p).storeColorMode==="light"?"/images/logo/logo.png":"/images/logo/logo.png",class:"w-100px h-auto",alt:"logo"},null,8,F),o(u,null,{default:t(()=>[C(b(a(c)),1)]),_:1}),o(y,{name:"fade-slide",mode:"out-in"},{default:t(()=>[(i(),r(N(m[a(e)]),{modelValue:a(e),"onUpdate:modelValue":s[0]||(s[0]=f=>V(e)?e.value=f:null),class:"w-85%"},null,8,["modelValue"]))]),_:1})])]),_:1}),s[1]||(s[1]=n("div",null,null,-1))]),_:1})}}});export{G as default};
