import{ak as E,al as Qe,am as wt,an as $,ao as bt,ap as Ut,aq as Pt,ar as nt,as as ka,at as Na,au as _t,av as X,aw as lc,ax as B,ay as ft,az as ht,aA as Po,aB as Ro,aC as ze,aD as uc,aE as Q,aF as pt,aG as gt,aH as z,aI as Vu,aJ as hc,aK as pe,aL as Be,aM as _e,aN as vc,aO as Kt,aP as za,aQ as cc,ai as In,aR as D,aS as W,aT as fc,aU as Qi,aV as Oa,aW as ku,aX as Oe,aY as tr,aZ as Eo,a_ as pc,a$ as Nu,b0 as br,b1 as zu,b2 as Ou,b3 as rt,b4 as _r,b5 as Gt,b6 as dc,b7 as Dt,b8 as mt,b9 as Gu,ba as Ke,bb as qe,bc as F,bd as Mn,be as Pn,bf as jt,bg as Nt,bh as Bu,bi as St,bj as gc,bk as Vr,bl as Fu,bm as Ga,bn as it,bo as Hu,bp as zt,bq as Zt,br as Wr,bs as O,bt as yc,bu as It,bv as Tt,bw as ie,bx as er,by as Rn,bz as Vt,bA as Yt,bB as Ba,bC as Wu,bD as Jt,bE as st,bF as $t,bG as Zu,bH as te,bI as mc,bJ as Uu,bK as Yu,bL as pa,bM as Sc,bN as da,bO as $u,bP as Qt,bQ as H,bR as oe,bS as En,bT as kt,bU as Mt,bV as Ge,bW as tn,bX as ue,bY as Vn,bZ as xc,b_ as ut,b$ as ct,c0 as Fa,c1 as Xu,c2 as Vo,c3 as cr,c4 as ko,c5 as Ku,c6 as bc,c7 as at,c8 as Ot,c9 as _c,ca as ti,cb as ei,cc as ga,cd as Sr,ce as No,cf as he,cg as kr,ch as Se,ci as Nr,cj as wc,ck as Ac,cl as $e,cm as zo,cn as en,co as Tc,cp as ve,cq as rn,cr as kn,cs as Nn,ct as zn,cu as Ha,cv as zr,cw as Wa,cx as Dc,cy as qu,cz as Lc,cA as ju,cB as On,cC as Cc,cD as Ic,cE as Oo,cF as wr,cG as Go,cH as Mc,cI as Pc,cJ as Rc,cK as rr,cL as Ju,cM as Ec,cN as Vc,cO as Gn,cP as kc,cQ as Nc,cR as Za,cS as Qu,cT as de,cU as Bn,cV as ya,cW as xe,cX as Or,cY as qt,cZ as Fn,c_ as th,c$ as je,d0 as Hn,d1 as zc,d2 as Oc,d3 as Gc,d4 as ma,d5 as an,d6 as nn,d7 as Bc,d8 as Gr,d9 as ri,da as Fc,db as eh,dc as Wn,dd as Ua,de as ai,df as Hc,dg as Wc,dh as fr,di as Zc,dj as Bo,dk as Uc,dl as Fo,dm as Ya,dn as on,dp as Yc,dq as ar,dr as Ho,ds as $c,dt as Xc,du as Kc,dv as Wo,dw as Zr,dx as qc,dy as jc,dz as Jc,dA as rh,dB as Ar,dC as Qc,dD as tf,dE as Zo,dF as ef,dG as rf,dH as af,dI as ah,dJ as ih,dK as nf,dL as nh,dM as Uo,dN as oh,dO as of,dP as Yo,dQ as Br,dR as ne,dS as sf,dT as sh,dU as Zn,dV as lf,dW as uf,dX as hf,dY as vf,dZ as cf,d_ as ff,d$ as lh,e0 as pf,e1 as uh,e2 as df,e3 as $o,e4 as gf,e5 as yf,e6 as mf,e7 as Sf,e8 as hh,e9 as vh,ea as Ur,eb as xf,ec as bf,ed as _f,ee as wf,ef as Un,eg as sa,eh as sn,ei as Af,ej as Tf,ek as Df,el as Lf,em as Cf,en as Sa,eo as If,ep as Mf,eq as Pf,er as $a,es as Rf,et as xa,eu as ch,ev as fh,ew as ph,ex as Ef,ey as Vf,ez as la,eA as dh,eB as kf,eC as Nf,eD as Xo,eE as zf,eF as ye,eG as Tr,eH as Of,eI as Ko,eJ as Gf,eK as Bf,eL as Ff,eM as gh,eN as yh,eO as Hf,eP as Wf,eQ as Xa,eR as Zf,eS as Uf,eT as Yf,eU as $f,eV as Xf,eW as Kf,eX as qf,eY as jf,eZ as Jf,e_ as Qf,e$ as tp,f0 as ep,f1 as rp,f2 as mh,f3 as Yn,f4 as ap,f5 as ip,f6 as np,f7 as qo,f8 as op,f9 as sp,fa as lp,fb as up,fc as hp,fd as vp,fe as cp,ff as fp,fg as pp,fh as dp,fi as gp,fj as yp,fk as mp,fl as Sp,fm as xp}from"./index-pY9FjpQW.js";var bp=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Qe(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?5e3:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?1e4:this.get("progressiveThreshold"))},e.prototype.brushSelector=function(t,r,i){return i.point(r.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(wt),Sh=4,_p=function(){function a(){}return a}(),wp=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.getDefaultShape=function(){return new _p},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(t,r){var i=r.points,n=r.size,o=this.symbolProxy,s=o.shape,l=t.getContext?t.getContext():t,u=l&&n[0]<Sh,h=this.softClipShape,v;if(u){this._ctx=l;return}for(this._ctx=null,v=this._off;v<i.length;){var c=i[v++],f=i[v++];isNaN(c)||isNaN(f)||h&&!h.contain(c,f)||(s.x=c-n[0]/2,s.y=f-n[1]/2,s.width=n[0],s.height=n[1],o.buildPath(t,s,!0))}this.incremental&&(this._off=v,this.notClear=!0)},e.prototype.afterBrush=function(){var t=this.shape,r=t.points,i=t.size,n=this._ctx,o=this.softClipShape,s;if(n){for(s=this._off;s<r.length;){var l=r[s++],u=r[s++];isNaN(l)||isNaN(u)||o&&!o.contain(l,u)||n.fillRect(l-i[0]/2,u-i[1]/2,i[0],i[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(t,r){for(var i=this.shape,n=i.points,o=i.size,s=Math.max(o[0],4),l=Math.max(o[1],4),u=n.length/2-1;u>=0;u--){var h=u*2,v=n[h]-s/2,c=n[h+1]-l/2;if(t>=v&&r>=c&&t<=v+s&&r<=c+l)return u}return-1},e.prototype.contain=function(t,r){var i=this.transformCoordToLocal(t,r),n=this.getBoundingRect();if(t=i[0],r=i[1],n.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,i=r.points,n=r.size,o=n[0],s=n[1],l=1/0,u=1/0,h=-1/0,v=-1/0,c=0;c<i.length;){var f=i[c++],p=i[c++];l=Math.min(f,l),h=Math.max(f,h),u=Math.min(p,u),v=Math.max(p,v)}t=this._rect=new bt(l-o/2,u-s/2,h-l+o,v-u+s)}return t},e}(Ut),Ap=function(){function a(){this.group=new $}return a.prototype.updateData=function(e,t){this._clear();var r=this._create();r.setShape({points:e.getLayout("points")}),this._setCommon(r,e,t)},a.prototype.updateLayout=function(e){var t=e.getLayout("points");this.group.eachChild(function(r){if(r.startIndex!=null){var i=(r.endIndex-r.startIndex)*2,n=r.startIndex*4*2;t=new Float32Array(t.buffer,n,i)}r.setShape("points",t),r.reset()})},a.prototype.incrementalPrepareUpdate=function(e){this._clear()},a.prototype.incrementalUpdate=function(e,t,r){var i=this._newAdded[0],n=t.getLayout("points"),o=i&&i.shape.points;if(o&&o.length<2e4){var s=o.length,l=new Float32Array(s+n.length);l.set(o),l.set(n,s),i.endIndex=e.end,i.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:n}),this._setCommon(u,t,r)}},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new wp({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},a.prototype._setCommon=function(e,t,r){var i=t.hostModel;r=r||{};var n=t.getVisual("symbolSize");e.setShape("size",n instanceof Array?n:[n,n]),e.softClipShape=r.clipShape||null,e.symbolProxy=Pt(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var o=e.shape.size[0]<Sh;e.useStyle(i.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=nt(e);u.seriesIndex=i.seriesIndex,e.on("mousemove",function(h){u.dataIndex=null;var v=e.hoverDataIdx;v>=0&&(u.dataIndex=v+(e.startIndex||0))})},a.prototype.remove=function(){this._clear()},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),Tp=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._updateSymbolDraw(n,t);o.updateData(n,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,i){var n=t.getData(),o=this._updateSymbolDraw(n,t);o.incrementalPrepareUpdate(n),this._finished=!1},e.prototype.incrementalRender=function(t,r,i){this._symbolDraw.incrementalUpdate(t,r.getData(),{clipShape:this._getClipShape(r)}),this._finished=t.end===r.getData().count()},e.prototype.updateTransform=function(t,r,i){var n=t.getData();if(this.group.dirty(),!this._finished||n.count()>1e4)return{update:!0};var o=ka("").reset(t,r,i);o.progress&&o.progress({start:0,end:n.count(),count:n.count()},n),this._symbolDraw.updateLayout(n)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var r=t.coordinateSystem;return r&&r.getArea&&r.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,r){var i=this._symbolDraw,n=r.pipelineContext,o=n.large;return(!i||o!==this._isLargeDraw)&&(i&&i.remove(),i=this._symbolDraw=o?new Ap:new Na,this._isLargeDraw=o,this.group.removeAll()),this.group.add(i.group),i},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(_t);function Dp(a){X(lc),a.registerSeriesModel(bp),a.registerChartView(Tp),a.registerLayout(ka("scatter"))}var Fr=function(a){E(e,a);function e(t){var r=a.call(this)||this;r._zr=t;var i=B(r._mousedownHandler,r),n=B(r._mousemoveHandler,r),o=B(r._mouseupHandler,r),s=B(r._mousewheelHandler,r),l=B(r._pinchHandler,r);return r.enable=function(u,h){this.disable(),this._opt=ft(ht(h)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),u==null&&(u=!0),(u===!0||u==="move"||u==="pan")&&(t.on("mousedown",i),t.on("mousemove",n),t.on("mouseup",o)),(u===!0||u==="scale"||u==="zoom")&&(t.on("mousewheel",s),t.on("pinch",l))},r.disable=function(){t.off("mousedown",i),t.off("mousemove",n),t.off("mouseup",o),t.off("mousewheel",s),t.off("pinch",l)},r}return e.prototype.isDragging=function(){return this._dragging},e.prototype.isPinching=function(){return this._pinching},e.prototype.setPointerChecker=function(t){this.pointerChecker=t},e.prototype.dispose=function(){this.disable()},e.prototype._mousedownHandler=function(t){if(!Po(t)){for(var r=t.target;r;){if(r.draggable)return;r=r.__hostTarget||r.parent}var i=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,i,n)&&(this._x=i,this._y=n,this._dragging=!0)}},e.prototype._mousemoveHandler=function(t){if(!(!this._dragging||!ua("moveOnMouseMove",t,this._opt)||t.gestureEvent==="pinch"||Ro(this._zr,"globalPan"))){var r=t.offsetX,i=t.offsetY,n=this._x,o=this._y,s=r-n,l=i-o;this._x=r,this._y=i,this._opt.preventDefaultMouseMove&&ze(t.event),xh(this,"pan","moveOnMouseMove",t,{dx:s,dy:l,oldX:n,oldY:o,newX:r,newY:i,isAvailableBehavior:null})}},e.prototype._mouseupHandler=function(t){Po(t)||(this._dragging=!1)},e.prototype._mousewheelHandler=function(t){var r=ua("zoomOnMouseWheel",t,this._opt),i=ua("moveOnMouseWheel",t,this._opt),n=t.wheelDelta,o=Math.abs(n),s=t.offsetX,l=t.offsetY;if(!(n===0||!r&&!i)){if(r){var u=o>3?1.4:o>1?1.2:1.1,h=n>0?u:1/u;ii(this,"zoom","zoomOnMouseWheel",t,{scale:h,originX:s,originY:l,isAvailableBehavior:null})}if(i){var v=Math.abs(n),c=(n>0?1:-1)*(v>3?.4:v>1?.15:.05);ii(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:c,originX:s,originY:l,isAvailableBehavior:null})}}},e.prototype._pinchHandler=function(t){if(!Ro(this._zr,"globalPan")){var r=t.pinchScale>1?1.1:1/1.1;ii(this,"zoom",null,t,{scale:r,originX:t.pinchX,originY:t.pinchY,isAvailableBehavior:null})}},e}(uc);function ii(a,e,t,r,i){a.pointerChecker&&a.pointerChecker(r,i.originX,i.originY)&&(ze(r.event),xh(a,e,t,r,i))}function xh(a,e,t,r,i){i.isAvailableBehavior=B(ua,null,t,r),a.trigger(e,i)}function ua(a,e,t){var r=t[a];return!a||r&&(!Q(r)||e.event[r+"Key"])}function $n(a,e,t){var r=a.target;r.x+=e,r.y+=t,r.dirty()}function Xn(a,e,t,r){var i=a.target,n=a.zoomLimit,o=a.zoom=a.zoom||1;if(o*=e,n){var s=n.min||0,l=n.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/a.zoom;a.zoom=o,i.x-=(t-i.x)*(u-1),i.y-=(r-i.y)*(u-1),i.scaleX*=u,i.scaleY*=u,i.dirty()}function bh(a){if(Q(a)){var e=new DOMParser;a=e.parseFromString(a,"text/xml")}var t=a;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var ni,ba={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},jo=pt(ba),_a={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Jo=pt(_a),Lp=function(){function a(){this._defs={},this._root=null}return a.prototype.parse=function(e,t){t=t||{};var r=bh(e);this._defsUsePending=[];var i=new $;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",s=parseFloat(r.getAttribute("width")||t.width),l=parseFloat(r.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),Et(r,i,null,!0,!1);for(var u=r.firstChild;u;)this._parseNode(u,i,n,null,!1,!1),u=u.nextSibling;Mp(this._defs,this._defsUsePending),this._defsUsePending=[];var h,v;if(o){var c=Ka(o);c.length>=4&&(h={x:parseFloat(c[0]||0),y:parseFloat(c[1]||0),width:parseFloat(c[2]),height:parseFloat(c[3])})}if(h&&s!=null&&l!=null&&(v=wh(h,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var f=i;i=new $,i.add(f),f.scaleX=f.scaleY=v.scale,f.x=v.x,f.y=v.y}return!t.ignoreRootClip&&s!=null&&l!=null&&i.setClipPath(new gt({shape:{x:0,y:0,width:s,height:l}})),{root:i,width:s,height:l,viewBoxRect:h,viewBoxTransform:v,named:n}},a.prototype._parseNode=function(e,t,r,i,n,o){var s=e.nodeName.toLowerCase(),l,u=i;if(s==="defs"&&(n=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!n){var h=ni[s];if(h&&z(ni,s)){l=h.call(this,e,t);var v=e.getAttribute("name");if(v){var c={name:v,namedFrom:null,svgNodeTagLower:s,el:l};r.push(c),s==="g"&&(u=c)}else i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:s,el:l});t.add(l)}}var f=Qo[s];if(f&&z(Qo,s)){var p=f.call(this,e),d=e.getAttribute("id");d&&(this._defs[d]=p)}}if(l&&l.isGroup)for(var g=e.firstChild;g;)g.nodeType===1?this._parseNode(g,l,r,u,n,o):g.nodeType===3&&o&&this._parseText(g,l),g=g.nextSibling},a.prototype._parseText=function(e,t){var r=new Vu({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),Cp(r,t);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var s=r.getBoundingRect();return this._textX+=s.width,t.add(r),r},a.internalField=function(){ni={g:function(e,t){var r=new $;return Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),r},rect:function(e,t){var r=new gt;return Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(e,t){var r=new za;return Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),r.silent=!0,r},line:function(e,t){var r=new Kt;return Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(e,t){var r=new vc;return Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(e,t){var r=e.getAttribute("points"),i;r&&(i=rs(r));var n=new _e({shape:{points:i||[]},silent:!0});return Bt(t,n),Et(e,n,this._defsUsePending,!1,!1),n},polyline:function(e,t){var r=e.getAttribute("points"),i;r&&(i=rs(r));var n=new Be({shape:{points:i||[]},silent:!0});return Bt(t,n),Et(e,n,this._defsUsePending,!1,!1),n},image:function(e,t){var r=new pe;return Bt(t,r),Et(e,r,this._defsUsePending,!1,!1),r.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),r.silent=!0,r},text:function(e,t){var r=e.getAttribute("x")||"0",i=e.getAttribute("y")||"0",n=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var s=new $;return Bt(t,s),Et(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var r=e.getAttribute("x"),i=e.getAttribute("y");r!=null&&(this._textX=parseFloat(r)),i!=null&&(this._textY=parseFloat(i));var n=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new $;return Bt(t,s),Et(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),s},path:function(e,t){var r=e.getAttribute("d")||"",i=hc(r);return Bt(t,i),Et(e,i,this._defsUsePending,!1,!1),i.silent=!0,i}}}(),a}(),Qo={lineargradient:function(a){var e=parseInt(a.getAttribute("x1")||"0",10),t=parseInt(a.getAttribute("y1")||"0",10),r=parseInt(a.getAttribute("x2")||"10",10),i=parseInt(a.getAttribute("y2")||"0",10),n=new In(e,t,r,i);return ts(a,n),es(a,n),n},radialgradient:function(a){var e=parseInt(a.getAttribute("cx")||"0",10),t=parseInt(a.getAttribute("cy")||"0",10),r=parseInt(a.getAttribute("r")||"0",10),i=new cc(e,t,r);return ts(a,i),es(a,i),i}};function ts(a,e){var t=a.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function es(a,e){for(var t=a.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var r=t.getAttribute("offset"),i=void 0;r&&r.indexOf("%")>0?i=parseInt(r,10)/100:r?i=parseFloat(r):i=0;var n={};_h(t,n,n);var o=n.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:i,color:o})}t=t.nextSibling}}function Bt(a,e){a&&a.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),ft(e.__inheritedStyle,a.__inheritedStyle))}function rs(a){for(var e=Ka(a),t=[],r=0;r<e.length;r+=2){var i=parseFloat(e[r]),n=parseFloat(e[r+1]);t.push([i,n])}return t}function Et(a,e,t,r,i){var n=e,o=n.__inheritedStyle=n.__inheritedStyle||{},s={};a.nodeType===1&&(Ep(a,e),_h(a,o,s),r||Vp(a,o,s)),n.style=n.style||{},o.fill!=null&&(n.style.fill=as(n,"fill",o.fill,t)),o.stroke!=null&&(n.style.stroke=as(n,"stroke",o.stroke,t)),D(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(n.style[l]=parseFloat(o[l]))}),D(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(n.style[l]=o[l])}),i&&(n.__selfStyle=s),o.lineDash&&(n.style.lineDash=W(Ka(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(n.invisible=!0),o.display==="none"&&(n.ignore=!0)}function Cp(a,e){var t=e.__selfStyle;if(t){var r=t.textBaseline,i=r;!r||r==="auto"||r==="baseline"?i="alphabetic":r==="before-edge"||r==="text-before-edge"?i="top":r==="after-edge"||r==="text-after-edge"?i="bottom":(r==="central"||r==="mathematical")&&(i="middle"),a.style.textBaseline=i}var n=e.__inheritedStyle;if(n){var o=n.textAlign,s=o;o&&(o==="middle"&&(s="center"),a.style.textAlign=s)}}var Ip=/^url\(\s*#(.*?)\)/;function as(a,e,t,r){var i=t&&t.match(Ip);if(i){var n=fc(i[1]);r.push([a,e,n]);return}return t==="none"&&(t=null),t}function Mp(a,e){for(var t=0;t<e.length;t++){var r=e[t];r[0].style[r[1]]=a[r[2]]}}var Pp=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Ka(a){return a.match(Pp)||[]}var Rp=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,oi=Math.PI/180;function Ep(a,e){var t=a.getAttribute("transform");if(t){t=t.replace(/,/g," ");var r=[],i=null;t.replace(Rp,function(v,c,f){return r.push(c,f),""});for(var n=r.length-1;n>0;n-=2){var o=r[n],s=r[n-1],l=Ka(o);switch(i=i||tr(),s){case"translate":Oe(i,i,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":ku(i,i,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":Oa(i,i,-parseFloat(l[0])*oi,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*oi);Qi(i,[1,0,u,1,0,0],i);break;case"skewY":var h=Math.tan(parseFloat(l[0])*oi);Qi(i,[1,h,0,1,0,0],i);break;case"matrix":i[0]=parseFloat(l[0]),i[1]=parseFloat(l[1]),i[2]=parseFloat(l[2]),i[3]=parseFloat(l[3]),i[4]=parseFloat(l[4]),i[5]=parseFloat(l[5]);break}}e.setLocalTransform(i)}}var is=/([^\s:;]+)\s*:\s*([^:;]+)/g;function _h(a,e,t){var r=a.getAttribute("style");if(r){is.lastIndex=0;for(var i;(i=is.exec(r))!=null;){var n=i[1],o=z(ba,n)?ba[n]:null;o&&(e[o]=i[2]);var s=z(_a,n)?_a[n]:null;s&&(t[s]=i[2])}}}function Vp(a,e,t){for(var r=0;r<jo.length;r++){var i=jo[r],n=a.getAttribute(i);n!=null&&(e[ba[i]]=n)}for(var r=0;r<Jo.length;r++){var i=Jo[r],n=a.getAttribute(i);n!=null&&(t[_a[i]]=n)}}function wh(a,e){var t=e.width/a.width,r=e.height/a.height,i=Math.min(t,r);return{scale:i,x:-(a.x+a.width/2)*i+(e.x+e.width/2),y:-(a.y+a.height/2)*i+(e.y+e.height/2)}}function kp(a,e){var t=new Lp;return t.parse(a,e)}var Np=1e-8;function ns(a,e){return Math.abs(a-e)<Np}function Ee(a,e,t){var r=0,i=a[0];if(!i)return!1;for(var n=1;n<a.length;n++){var o=a[n];r+=Eo(i[0],i[1],o[0],o[1],e,t),i=o}var s=a[0];return(!ns(i[0],s[0])||!ns(i[1],s[1]))&&(r+=Eo(i[0],i[1],s[0],s[1],e,t)),r!==0}var zp=[];function si(a,e){for(var t=0;t<a.length;t++)br(a[t],a[t],e)}function os(a,e,t,r){for(var i=0;i<a.length;i++){var n=a[i];r&&(n=r.project(n)),n&&isFinite(n[0])&&isFinite(n[1])&&(zu(e,e,n),Ou(t,t,n))}}function Op(a){for(var e=0,t=0,r=0,i=a.length,n=a[i-1][0],o=a[i-1][1],s=0;s<i;s++){var l=a[s][0],u=a[s][1],h=n*u-l*o;e+=h,t+=(n+l)*h,r+=(o+u)*h,n=l,o=u}return e?[t/e/3,r/e/3,e]:[a[0][0]||0,a[0][1]||0]}var Ah=function(){function a(e){this.name=e}return a.prototype.setCenter=function(e){this._center=e},a.prototype.getCenter=function(){var e=this._center;return e||(e=this._center=this.calcCenter()),e},a}(),ss=function(){function a(e,t){this.type="polygon",this.exterior=e,this.interiors=t}return a}(),ls=function(){function a(e){this.type="linestring",this.points=e}return a}(),Th=function(a){E(e,a);function e(t,r,i){var n=a.call(this,t)||this;return n.type="geoJSON",n.geometries=r,n._center=i&&[i[0],i[1]],n}return e.prototype.calcCenter=function(){for(var t=this.geometries,r,i=0,n=0;n<t.length;n++){var o=t[n],s=o.exterior,l=s&&s.length;l>i&&(r=o,i=l)}if(r)return Op(r.exterior);var u=this.getBoundingRect();return[u.x+u.width/2,u.y+u.height/2]},e.prototype.getBoundingRect=function(t){var r=this._rect;if(r&&!t)return r;var i=[1/0,1/0],n=[-1/0,-1/0],o=this.geometries;return D(o,function(s){s.type==="polygon"?os(s.exterior,i,n,t):D(s.points,function(l){os(l,i,n,t)})}),isFinite(i[0])&&isFinite(i[1])&&isFinite(n[0])&&isFinite(n[1])||(i[0]=i[1]=n[0]=n[1]=0),r=new bt(i[0],i[1],n[0]-i[0],n[1]-i[1]),t||(this._rect=r),r},e.prototype.contain=function(t){var r=this.getBoundingRect(),i=this.geometries;if(!r.contain(t[0],t[1]))return!1;t:for(var n=0,o=i.length;n<o;n++){var s=i[n];if(s.type==="polygon"){var l=s.exterior,u=s.interiors;if(Ee(l,t[0],t[1])){for(var h=0;h<(u?u.length:0);h++)if(Ee(u[h],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,r,i,n){var o=this.getBoundingRect(),s=o.width/o.height;i?n||(n=i/s):i=s*n;for(var l=new bt(t,r,i,n),u=o.calculateTransform(l),h=this.geometries,v=0;v<h.length;v++){var c=h[v];c.type==="polygon"?(si(c.exterior,u),D(c.interiors,function(f){si(f,u)})):D(c.points,function(f){si(f,u)})}o=this._rect,o.copy(l),this._center=[o.x+o.width/2,o.y+o.height/2]},e.prototype.cloneShallow=function(t){t==null&&(t=this.name);var r=new e(t,this.geometries,this._center);return r._rect=this._rect,r.transformTo=null,r},e}(Ah),Gp=function(a){E(e,a);function e(t,r){var i=a.call(this,t)||this;return i.type="geoSVG",i._elOnlyForCalculate=r,i}return e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,r=t.getBoundingRect(),i=[r.x+r.width/2,r.y+r.height/2],n=pc(zp),o=t;o&&!o.isGeoSVGGraphicRoot;)Qi(n,o.getLocalTransform(),n),o=o.parent;return Nu(n,n),br(i,i,n),i},e}(Ah),Bp=rt(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),Fp=function(){function a(e,t){this.type="geoSVG",this._usedGraphicMap=rt(),this._freedGraphics=[],this._mapName=e,this._parsedXML=bh(t)}return a.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=Wp(e.named),r=t.regions,i=t.regionsMap;this._regions=r,this._regionsMap=i}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},a.prototype._buildGraphic=function(e){var t,r;try{t=e&&kp(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},r=t.root,_r(r!=null)}catch(g){throw new Error(`Invalid svg format
`+g.message)}var i=new $;i.add(r),i.isGeoSVGGraphicRoot=!0;var n=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,h=void 0,v=void 0,c=void 0;if(n!=null?(u=0,v=n):s&&(u=s.x,v=s.width),o!=null?(h=0,c=o):s&&(h=s.y,c=s.height),u==null||h==null){var f=r.getBoundingRect();u==null&&(u=f.x,v=f.width),h==null&&(h=f.y,c=f.height)}l=this._boundingRect=new bt(u,h,v,c)}if(s){var p=wh(s,l);r.scaleX=r.scaleY=p.scale,r.x=p.x,r.y=p.y}i.setClipPath(new gt({shape:l.plain()}));var d=[];return D(t.named,function(g){Bp.get(g.svgNodeTagLower)!=null&&(d.push(g),Hp(g.el))}),{root:i,boundingRect:l,named:d}},a.prototype.useGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);return r||(r=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,r),r)},a.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);r&&(t.removeKey(e),this._freedGraphics.push(r))},a}();function Hp(a){a.silent=!1,a.isGroup&&a.traverse(function(e){e.silent=!1})}function Wp(a){var e=[],t=rt();return D(a,function(r){if(r.namedFrom==null){var i=new Gp(r.name,r.el);e.push(i),t.set(r.name,i)}}),{regions:e,regionsMap:t}}function Zp(a){if(!a.UTF8Encoding)return a;var e=a,t=e.UTF8Scale;t==null&&(t=1024);var r=e.features;return D(r,function(i){var n=i.geometry,o=n.encodeOffsets,s=n.coordinates;if(o)switch(n.type){case"LineString":n.coordinates=Dh(s,o,t);break;case"Polygon":li(s,o,t);break;case"MultiLineString":li(s,o,t);break;case"MultiPolygon":D(s,function(l,u){return li(l,o[u],t)})}}),e.UTF8Encoding=!1,e}function li(a,e,t){for(var r=0;r<a.length;r++)a[r]=Dh(a[r],e[r],t)}function Dh(a,e,t){for(var r=[],i=e[0],n=e[1],o=0;o<a.length;o+=2){var s=a.charCodeAt(o)-64,l=a.charCodeAt(o+1)-64;s=s>>1^-(s&1),l=l>>1^-(l&1),s+=i,l+=n,i=s,n=l,r.push([s/t,l/t])}return r}function Up(a,e){return a=Zp(a),W(Gt(a.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var r=t.properties,i=t.geometry,n=[];switch(i.type){case"Polygon":var o=i.coordinates;n.push(new ss(o[0],o.slice(1)));break;case"MultiPolygon":D(i.coordinates,function(l){l[0]&&n.push(new ss(l[0],l.slice(1)))});break;case"LineString":n.push(new ls([i.coordinates]));break;case"MultiLineString":n.push(new ls(i.coordinates))}var s=new Th(r[e||"name"],n,r.cp);return s.properties=r,s})}var ln=[126,25],us="南海诸岛",Ce=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var De=0;De<Ce.length;De++)for(var Fe=0;Fe<Ce[De].length;Fe++)Ce[De][Fe][0]/=10.5,Ce[De][Fe][1]/=-10.5/.75,Ce[De][Fe][0]+=ln[0],Ce[De][Fe][1]+=ln[1];function Yp(a,e){if(a==="china"){for(var t=0;t<e.length;t++)if(e[t].name===us)return;e.push(new Th(us,W(Ce,function(r){return{type:"polygon",exterior:r}}),ln))}}var $p={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function Xp(a,e){if(a==="china"){var t=$p[e.name];if(t){var r=e.getCenter();r[0]+=t[0]/10.5,r[1]+=-t[1]/(10.5/.75),e.setCenter(r)}}}var Kp=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function qp(a,e){a==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:Kp[0]})}var jp="name",Jp=function(){function a(e,t,r){this.type="geoJSON",this._parsedMap=rt(),this._mapName=e,this._specialAreas=r,this._geoJSON=td(t)}return a.prototype.load=function(e,t){t=t||jp;var r=this._parsedMap.get(t);if(!r){var i=this._parseToRegions(t);r=this._parsedMap.set(t,{regions:i,boundingRect:Qp(i)})}var n=rt(),o=[];return D(r.regions,function(s){var l=s.name;e&&z(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),n.set(l,s)}),{regions:o,boundingRect:r.boundingRect||new bt(0,0,0,0),regionsMap:n}},a.prototype._parseToRegions=function(e){var t=this._mapName,r=this._geoJSON,i;try{i=r?Up(r,e):[]}catch(n){throw new Error(`Invalid geoJson format
`+n.message)}return Yp(t,i),D(i,function(n){var o=n.name;Xp(t,n),qp(t,n);var s=this._specialAreas&&this._specialAreas[o];s&&n.transformTo(s.left,s.top,s.width,s.height)},this),i},a.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},a}();function Qp(a){for(var e,t=0;t<a.length;t++){var r=a[t].getBoundingRect();e=e||r.clone(),e.union(r)}return e}function td(a){return Q(a)?typeof JSON<"u"&&JSON.parse?JSON.parse(a):new Function("return ("+a+");")():a}var nr=rt();const ce={registerMap:function(a,e,t){if(e.svg){var r=new Fp(a,e.svg);nr.set(a,r)}else{var i=e.geoJson||e.geoJSON;i&&!e.features?t=e.specialAreas:i=e;var r=new Jp(a,i,t);nr.set(a,r)}},getGeoResource:function(a){return nr.get(a)},getMapForUser:function(a){var e=nr.get(a);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(a,e,t){var r=nr.get(a);if(r)return r.load(e,t)}};var Kn=["rect","circle","line","ellipse","polygon","polyline","path"],ed=rt(Kn),rd=rt(Kn.concat(["g"])),ad=rt(Kn.concat(["g"])),Lh=Dt();function Yr(a){var e=a.getItemStyle(),t=a.get("areaColor");return t!=null&&(e.fill=t),e}function hs(a){var e=a.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var Ch=function(){function a(e){var t=new $;this.uid=dc("ec_map_draw"),this._controller=new Fr(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new $),t.add(this._svgGroup=new $)}return a.prototype.draw=function(e,t,r,i,n){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(y){!s&&y.getHostGeoModel()===e&&(s=y.getData())});var l=e.coordinateSystem,u=this._regionsGroup,h=this.group,v=l.getTransformInfo(),c=v.raw,f=v.roam,p=!u.childAt(0)||n;p?(h.x=f.x,h.y=f.y,h.scaleX=f.scaleX,h.scaleY=f.scaleY,h.dirty()):mt(h,f,e);var d=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,g={api:r,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:d,isGeo:o,transformInfoRaw:c};l.resourceType==="geoJSON"?this._buildGeoJSON(g):l.resourceType==="geoSVG"&&this._buildSVG(g),this._updateController(e,t,r),this._updateMapSelectHandler(e,u,r,i)},a.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=rt(),r=rt(),i=this._regionsGroup,n=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function h(f,p){return p&&(f=p(f)),f&&[f[0]*n.scaleX+n.x,f[1]*n.scaleY+n.y]}function v(f){for(var p=[],d=!u&&l&&l.project,g=0;g<f.length;++g){var y=h(f[g],d);y&&p.push(y)}return p}function c(f){return{shape:{points:v(f)}}}i.removeAll(),D(e.geo.regions,function(f){var p=f.name,d=t.get(p),g=r.get(p)||{},y=g.dataIdx,m=g.regionModel;if(!d){d=t.set(p,new $),i.add(d),y=s?s.indexOfName(p):null,m=e.isGeo?o.getRegionModel(p):s?s.getItemModel(y):null;var S=m.get("silent",!0);S!=null&&(d.silent=S),r.set(p,{dataIdx:y,regionModel:m})}var x=[],b=[];D(f.geometries,function(T){if(T.type==="polygon"){var L=[T.exterior].concat(T.interiors||[]);u&&(L=gs(L,u)),D(L,function(C){x.push(new _e(c(C)))})}else{var A=T.points;u&&(A=gs(A,u,!0)),D(A,function(C){b.push(new Be(c(C)))})}});var _=h(f.getCenter(),l&&l.project);function w(T,L){if(T.length){var A=new Gu({culling:!0,segmentIgnoreThreshold:1,shape:{paths:T}});d.add(A),vs(e,A,y,m),cs(e,A,p,m,o,y,_),L&&(hs(A),D(A.states,hs))}}w(x),w(b,!0)}),t.each(function(f,p){var d=r.get(p),g=d.dataIdx,y=d.regionModel;fs(e,f,p,y,o,g),ps(e,f,p,y,o),ds(e,f,p,y,o)},this)},a.prototype._buildSVG=function(e){var t=e.geo.map,r=e.transformInfoRaw;this._svgGroup.x=r.x,this._svgGroup.y=r.y,this._svgGroup.scaleX=r.scaleX,this._svgGroup.scaleY=r.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var i=this._svgDispatcherMap=rt(),n=!1;D(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,h=o.svgNodeTagLower,v=o.el,c=u?u.indexOfName(s):null,f=l.getRegionModel(s);ed.get(h)!=null&&v instanceof Ke&&vs(e,v,c,f),v instanceof Ke&&(v.culling=!0);var p=f.get("silent",!0);if(p!=null&&(v.silent=p),v.z2EmphasisLift=0,!o.namedFrom&&(ad.get(h)!=null&&cs(e,v,s,f,l,c,null),fs(e,v,s,f,l,c),ps(e,v,s,f,l),rd.get(h)!=null)){var d=ds(e,v,s,f,l);d==="self"&&(n=!0);var g=i.get(s)||i.set(s,[]);g.push(v)}},this),this._enableBlurEntireSVG(n,e)},a.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var r=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),i=r.opacity;this._svgGraphicRecord.root.traverse(function(n){if(!n.isGroup){qe(n);var o=n.ensureState("blur").style||{};o.opacity==null&&i!=null&&(o.opacity=i),n.ensureState("emphasis")}})}},a.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},a.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var r=t.coordinateSystem;if(r.resourceType==="geoJSON"){var i=this._regionsGroupByName;if(i){var n=i.get(e);return n?[n]:[]}}else if(r.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},a.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},a.prototype._useSVG=function(e){var t=ce.getGeoResource(e);if(t&&t.type==="geoSVG"){var r=t.useGraphic(this.uid);this._svgGroup.add(r.root),this._svgGraphicRecord=r,this._svgMapName=e}},a.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=ce.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},a.prototype._updateController=function(e,t,r){var i=e.coordinateSystem,n=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=i.getZoom(),n.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}n.off("pan").on("pan",function(u){this._mouseDownFlag=!1,$n(o,u.dx,u.dy),r.dispatchAction(F(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),n.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,Xn(o,u.scale,u.originX,u.originY),r.dispatchAction(F(l(),{totalZoom:o.zoom,zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),n.setPointerChecker(function(u,h,v){return i.containPoint([h,v])&&!Mn(u,r,e)})},a.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=Lh(t).ignore)})},a.prototype._updateMapSelectHandler=function(e,t,r,i){var n=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){n._mouseDownFlag=!0}),t.on("click",function(o){n._mouseDownFlag&&(n._mouseDownFlag=!1)}))},a}();function vs(a,e,t,r){var i=r.getModel("itemStyle"),n=r.getModel(["emphasis","itemStyle"]),o=r.getModel(["blur","itemStyle"]),s=r.getModel(["select","itemStyle"]),l=Yr(i),u=Yr(n),h=Yr(s),v=Yr(o),c=a.data;if(c){var f=c.getItemVisual(t,"style"),p=c.getItemVisual(t,"decal");a.isVisualEncodedByVisualMap&&f.fill&&(l.fill=f.fill),p&&(l.decal=Pn(p,a.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=h,e.ensureState("blur").style=v,qe(e)}function cs(a,e,t,r,i,n,o){var s=a.data,l=a.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),n)),h=s&&s.getItemLayout(n);if(l||u||h&&h.showLabel){var v=l?t:n,c=void 0;(!s||n>=0)&&(c=i);var f=o?{normal:{align:"center",verticalAlign:"middle"}}:null;jt(e,Nt(r),{labelFetcher:c,labelDataIndex:v,defaultText:t},f);var p=e.getTextContent();if(p&&(Lh(p).ignore=p.ignore,e.textConfig&&o)){var d=e.getBoundingRect().clone();e.textConfig.layoutRect=d,e.textConfig.position=[(o[0]-d.x)/d.width*100+"%",(o[1]-d.y)/d.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function fs(a,e,t,r,i,n){a.data?a.data.setItemGraphicEl(n,e):nt(e).eventData={componentType:"geo",componentIndex:i.componentIndex,geoIndex:i.componentIndex,name:t,region:r&&r.option||{}}}function ps(a,e,t,r,i){a.data||Bu({el:e,componentModel:i,itemName:t,itemTooltipOption:r.get("tooltip")})}function ds(a,e,t,r,i){e.highDownSilentOnTouch=!!i.get("selectedMode");var n=r.getModel("emphasis"),o=n.get("focus");return St(e,o,n.get("blurScope"),n.get("disabled")),a.isGeo&&gc(e,i,t),o}function gs(a,e,t){var r=[],i;function n(){i=[]}function o(){i.length&&(r.push(i),i=[])}var s=e({polygonStart:n,polygonEnd:o,lineStart:n,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&i.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),D(a,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),r}var id=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){if(!(n&&n.type==="mapToggleSelect"&&n.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&n&&n.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),n&&n.type==="geoRoam"&&n.componentType==="series"&&n.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new Ch(i);o.add(s.group),s.draw(t,r,i,this,n),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&r.getComponent("legend")&&this._renderSymbols(t,r,i)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,r,i){var n=t.originalData,o=this.group;n.each(n.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=n.getItemLayout(l);if(!(!u||!u.point)){var h=u.point,v=u.offset,c=new za({style:{fill:t.getData().getVisual("style").fill},shape:{cx:h[0]+v*9,cy:h[1],r:3},silent:!0,z2:8+(v?0:Vr+1)});if(!v){var f=t.mainSeries.getData(),p=n.getName(l),d=f.indexOfName(p),g=n.getItemModel(l),y=g.getModel("label"),m=f.getItemGraphicEl(d);jt(c,Nt(g),{labelFetcher:{getFormattedLabel:function(S,x){return t.getFormattedLabel(d,x)}},defaultText:p}),c.disableLabelAnimation=!0,y.get("position")||c.setTextConfig({position:"bottom"}),m.onHoverStateChange=function(S){Fu(c,S)}}o.add(c)}}})},e.type="map",e}(_t),nd=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(r){if(r!=null){var i=this.getData().getName(r),n=this.coordinateSystem,o=n.getRegion(i);return o&&n.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var r=Ga(this,{coordDimensions:["value"],encodeDefaulter:it(Hu,this)}),i=rt(),n=[],o=0,s=r.count();o<s;o++){var l=r.getName(o);i.set(l,o)}var u=ce.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return D(u.regions,function(h){var v=h.name,c=i.get(v),f=h.properties&&h.properties.echartsStyle,p;c==null?(p={name:v},n.push(p)):p=r.getRawDataItem(c),f&&zt(p,f)}),r.appendData(n),r},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var r=this.getData();return r.get(r.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var r=this.getData();return r.getItemModel(r.indexOfName(t))},e.prototype.formatTooltip=function(t,r,i){for(var n=this.getData(),o=this.getRawValue(t),s=n.getName(t),l=this.seriesGroup,u=[],h=0;h<l.length;h++){var v=l[h].originalData.indexOfName(s),c=n.mapDimension("value");isNaN(l[h].originalData.get(c,v))||u.push(l[h].name)}return Zt("section",{header:u.join(", "),noHeader:!u.length,blocks:[Zt("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var r=t.icon||"roundRect",i=Pt(r,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return i.setStyle(t.itemStyle),i.style.stroke="none",r.indexOf("empty")>-1&&(i.style.stroke=i.style.fill,i.style.fill="#fff",i.style.lineWidth=2),i},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(wt);function od(a,e){var t={};return D(a,function(r){r.each(r.mapDimension("value"),function(i,n){var o="ec-"+r.getName(n);t[o]=t[o]||[],isNaN(i)||t[o].push(i)})}),a[0].map(a[0].mapDimension("value"),function(r,i){for(var n="ec-"+a[0].getName(i),o=0,s=1/0,l=-1/0,u=t[n].length,h=0;h<u;h++)s=Math.min(s,t[n][h]),l=Math.max(l,t[n][h]),o+=t[n][h];var v;return e==="min"?v=s:e==="max"?v=l:e==="average"?v=o/u:v=o,u===0?NaN:v})}function sd(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getHostGeoModel(),i=r?"o"+r.id:"i"+t.getMapType();(e[i]=e[i]||[]).push(t)}),D(e,function(t,r){for(var i=od(W(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),n=0;n<t.length;n++)t[n].originalData=t[n].getData();for(var n=0;n<t.length;n++)t[n].seriesGroup=t,t[n].needsDrawMap=n===0&&!t[n].getHostGeoModel(),t[n].setData(i.cloneShallow()),t[n].mainSeries=t[0]})}function ld(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getMapType();if(!(t.getHostGeoModel()||e[r])){var i={};D(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&a.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,h){var v=l.getName(h),c=s.getRegion(v);if(!(!c||isNaN(u))){var f=i[v]||0,p=s.dataToPoint(c.getCenter());i[v]=f+1,l.setItemLayout(h,{point:p,offset:f})}})});var n=t.getData();n.each(function(o){var s=n.getName(o),l=n.getItemLayout(o)||{};l.showLabel=!i[s],n.setItemLayout(o,l)}),e[r]=!0}})}var ys=br,Hr=function(a){E(e,a);function e(t){var r=a.call(this)||this;return r.type="view",r.dimensions=["x","y"],r._roamTransformable=new Wr,r._rawTransformable=new Wr,r.name=t,r}return e.prototype.setBoundingRect=function(t,r,i,n){return this._rect=new bt(t,r,i,n),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,r,i,n){this._transformTo(t,r,i,n),this._viewRect=new bt(t,r,i,n)},e.prototype._transformTo=function(t,r,i,n){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new bt(t,r,i,n));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,r){t&&(this._center=[O(t[0],r.getWidth()),O(t[1],r.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var r=this.zoomLimit;r&&(r.max!=null&&(t=Math.min(r.max,t)),r.min!=null&&(t=Math.max(r.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),r=t.x+t.width/2,i=t.y+t.height/2;return[r,i]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),r=this._roamTransformable,i=this.getDefaultCenter(),n=this.getCenter(),o=this.getZoom();n=br([],n,t),i=br([],i,t),r.originX=n[0],r.originY=n[1],r.x=i[0]-n[0],r.y=i[1]-n[1],r.scaleX=r.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,r=this._rawTransformable;r.parent=t,t.updateTransform(),r.updateTransform(),yc(this.transform||(this.transform=[]),r.transform||tr()),this._rawTransform=r.getLocalTransform(),this.invTransform=this.invTransform||[],Nu(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,r=this._roamTransformable,i=new Wr;return i.transform=r.transform,i.decomposeTransform(),{roam:{x:i.x,y:i.y,scaleX:i.scaleX,scaleY:i.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,r,i){var n=r?this._rawTransform:this.transform;return i=i||[],n?ys(i,t,n):It(i,t)},e.prototype.pointToData=function(t){var r=this.invTransform;return r?ys([],t,r):[t[0],t[1]]},e.prototype.convertToPixel=function(t,r,i){var n=ms(r);return n===this?n.dataToPoint(i):null},e.prototype.convertFromPixel=function(t,r,i){var n=ms(r);return n===this?n.pointToData(i):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(Wr);function ms(a){var e=a.seriesModel;return e?e.coordinateSystem:null}var ud={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},Ih=["lng","lat"],un=function(a){E(e,a);function e(t,r,i){var n=a.call(this,t)||this;n.dimensions=Ih,n.type="geo",n._nameCoordMap=rt(),n.map=r;var o=i.projection,s=ce.load(r,i.nameMap,i.nameProperty),l=ce.getGeoResource(r);n.resourceType=l?l.type:null;var u=n.regions=s.regions,h=ud[l.type];n._regionsMap=s.regionsMap,n.regions=s.regions,n.projection=o;var v;if(o)for(var c=0;c<u.length;c++){var f=u[c].getBoundingRect(o);v=v||f.clone(),v.union(f)}else v=s.boundingRect;return n.setBoundingRect(v.x,v.y,v.width,v.height),n.aspectScale=o?1:Tt(i.aspectScale,h.aspectScale),n._invertLongitute=o?!1:h.invertLongitute,n}return e.prototype._transformTo=function(t,r,i,n){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new bt(t,r,i,n));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var r=this.regions,i=0;i<r.length;i++){var n=r[i];if(n.type==="geoJSON"&&n.contain(t))return r[i]}},e.prototype.addGeoCoord=function(t,r){this._nameCoordMap.set(t,r)},e.prototype.getGeoCoord=function(t){var r=this._regionsMap.get(t);return this._nameCoordMap.get(t)||r&&r.getCenter()},e.prototype.dataToPoint=function(t,r,i){if(Q(t)&&(t=this.getGeoCoord(t)),t){var n=this.projection;return n&&(t=n.project(t)),t&&this.projectedToPoint(t,r,i)}},e.prototype.pointToData=function(t){var r=this.projection;return r&&(t=r.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return a.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,r,i){return a.prototype.dataToPoint.call(this,t,r,i)},e.prototype.convertToPixel=function(t,r,i){var n=Ss(r);return n===this?n.dataToPoint(i):null},e.prototype.convertFromPixel=function(t,r,i){var n=Ss(r);return n===this?n.pointToData(i):null},e}(Hr);ie(un,Hr);function Ss(a){var e=a.geoModel,t=a.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",er).models[0]||{}).coordinateSystem:null}function xs(a,e){var t=a.get("boundingCoords");if(t!=null){var r=t[0],i=t[1];if(isFinite(r[0])&&isFinite(r[1])&&isFinite(i[0])&&isFinite(i[1])){var n=this.projection;if(n){var o=r[0],s=r[1],l=i[0],u=i[1];r=[1/0,1/0],i=[-1/0,-1/0];var h=function(_,w,T,L){for(var A=T-_,C=L-w,I=0;I<=100;I++){var M=I/100,P=n.project([_+A*M,w+C*M]);zu(r,r,P),Ou(i,i,P)}};h(o,s,l,s),h(l,s,l,u),h(l,u,o,u),h(o,u,l,s)}this.setBoundingRect(r[0],r[1],i[0]-r[0],i[1]-r[1])}}var v=this.getBoundingRect(),c=a.get("layoutCenter"),f=a.get("layoutSize"),p=e.getWidth(),d=e.getHeight(),g=v.width/v.height*this.aspectScale,y=!1,m,S;c&&f&&(m=[O(c[0],p),O(c[1],d)],S=O(f,Math.min(p,d)),!isNaN(m[0])&&!isNaN(m[1])&&!isNaN(S)&&(y=!0));var x;if(y)x={},g>1?(x.width=S,x.height=S/g):(x.height=S,x.width=S*g),x.y=m[1]-x.height/2,x.x=m[0]-x.width/2;else{var b=a.getBoxLayoutParams();b.aspect=g,x=Yt(b,{width:p,height:d})}this.setViewRect(x.x,x.y,x.width,x.height),this.setCenter(a.get("center"),e),this.setZoom(a.get("zoom"))}function hd(a,e){D(e.get("geoCoord"),function(t,r){a.addGeoCoord(r,t)})}var vd=function(){function a(){this.dimensions=Ih}return a.prototype.create=function(e,t){var r=[];function i(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new un(l+s,l,F({nameMap:o.get("nameMap")},i(o)));u.zoomLimit=o.get("scaleLimit"),r.push(u),o.coordinateSystem=u,u.model=o,u.resize=xs,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=r[l]}});var n={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();n[s]=n[s]||[],n[s].push(o)}}),D(n,function(o,s){var l=W(o,function(h){return h.get("nameMap")}),u=new un(s,s,F({nameMap:Rn(l)},i(o[0])));u.zoomLimit=Vt.apply(null,W(o,function(h){return h.get("scaleLimit")})),r.push(u),u.resize=xs,u.resize(o[0],t),D(o,function(h){h.coordinateSystem=u,hd(u,h)})}),r},a.prototype.getFilledRegions=function(e,t,r,i){for(var n=(e||[]).slice(),o=rt(),s=0;s<n.length;s++)o.set(n[s].name,n[s]);var l=ce.load(t,r,i);return D(l.regions,function(u){var h=u.name,v=o.get(h),c=u.properties&&u.properties.echartsStyle;v||(v={name:h},n.push(v)),c&&zt(v,c)}),n},a}(),Mh=new vd,cd=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,i){var n=ce.getGeoResource(t.map);if(n&&n.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,i),Ba(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,r=this.option;r.regions=Mh.getFilledRegions(r.regions,r.map,r.nameMap,r.nameProperty);var i={};this._optionModelMap=Wu(r.regions||[],function(n,o){var s=o.name;return s&&(n.set(s,new Jt(o,t,t.ecModel)),o.selected&&(i[s]=!0)),n},rt()),r.selectedMap||(r.selectedMap=i)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Jt(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,r){var i=this.getRegionModel(t),n=r==="normal"?i.get(["label","formatter"]):i.get(["emphasis","label","formatter"]),o={name:t};if(st(n))return o.status=r,n(o);if(Q(n))return n.replace("{a}",t??"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var r=this.option,i=r.selectedMode;if(i){i!=="multiple"&&(r.selectedMap=null);var n=r.selectedMap||(r.selectedMap={});n[t]=!0}},e.prototype.unSelect=function(t){var r=this.option.selectedMap;r&&(r[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var r=this.option.selectedMap;return!!(r&&r[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}($t);function bs(a,e){return a.pointToProjected?a.pointToProjected(e):a.pointToData(e)}function qn(a,e,t,r){var i=a.getZoom(),n=a.getCenter(),o=e.zoom,s=a.projectedToPoint?a.projectedToPoint(n):a.dataToPoint(n);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,a.setCenter(bs(a,s),r)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(i*o,u),l)/i}a.scaleX*=o,a.scaleY*=o;var h=(e.originX-a.x)*(o-1),v=(e.originY-a.y)*(o-1);a.x-=h,a.y-=v,a.updateTransform(),a.setCenter(bs(a,s),r),a.setZoom(o*i)}return{center:a.getCenter(),zoom:a.getZoom()}}var fd=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,r){this._api=r},e.prototype.render=function(t,r,i,n){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new Ch(i));var o=this._mapDraw;o.draw(t,r,i,this,n),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,r,i)},e.prototype._handleRegionClick=function(t){var r;Zu(t.target,function(i){return(r=nt(i).eventData)!=null},!0),r&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:r.name})},e.prototype.updateSelectStatus=function(t,r,i){var n=this;this._mapDraw.group.traverse(function(o){var s=nt(o).eventData;if(s)return n._model.isSelected(s.name)?i.enterSelect(o):i.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(te);function pd(a,e,t){ce.registerMap(a,e,t)}function Ph(a){a.registerCoordinateSystem("geo",Mh),a.registerComponentModel(cd),a.registerComponentView(fd),a.registerImpl("registerMap",pd),a.registerImpl("getMap",function(t){return ce.getMapForUser(t)});function e(t,r){r.update="geo:updateSelectStatus",a.registerAction(r,function(i,n){var o={},s=[];return n.eachComponent({mainType:"geo",query:i},function(l){l[t](i.name);var u=l.coordinateSystem;D(u.regions,function(v){o[v.name]=l.isSelected(v.name)||!1});var h=[];D(o,function(v,c){o[c]&&h.push(c)}),s.push({geoIndex:l.componentIndex,name:h})}),{selected:o,allSelected:s,name:i.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),a.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,r,i){var n=t.componentType||"series";r.eachComponent({mainType:n,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=qn(s,t,o.get("scaleLimit"),i);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),n==="series"&&D(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function dd(a){X(Ph),a.registerChartView(id),a.registerSeriesModel(nd),a.registerLayout(ld),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,sd),mc("map",a.registerAction)}function gd(a){var e=a;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],r,i;r=t.pop();)if(i=r.children,r.isExpand&&i.length)for(var n=i.length,o=n-1;o>=0;o--){var s=i[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function yd(a,e){var t=a.isExpand?a.children:[],r=a.parentNode.children,i=a.hierNode.i?r[a.hierNode.i-1]:null;if(t.length){xd(a);var n=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;i?(a.hierNode.prelim=i.hierNode.prelim+e(a,i),a.hierNode.modifier=a.hierNode.prelim-n):a.hierNode.prelim=n}else i&&(a.hierNode.prelim=i.hierNode.prelim+e(a,i));a.parentNode.hierNode.defaultAncestor=bd(a,i,a.parentNode.hierNode.defaultAncestor||r[0],e)}function md(a){var e=a.hierNode.prelim+a.parentNode.hierNode.modifier;a.setLayout({x:e},!0),a.hierNode.modifier+=a.parentNode.hierNode.modifier}function _s(a){return arguments.length?a:Ad}function pr(a,e){return a-=Math.PI/2,{x:e*Math.cos(a),y:e*Math.sin(a)}}function Sd(a,e){return Yt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function xd(a){for(var e=a.children,t=e.length,r=0,i=0;--t>=0;){var n=e[t];n.hierNode.prelim+=r,n.hierNode.modifier+=r,i+=n.hierNode.change,r+=n.hierNode.shift+i}}function bd(a,e,t,r){if(e){for(var i=a,n=a,o=n.parentNode.children[0],s=e,l=i.hierNode.modifier,u=n.hierNode.modifier,h=o.hierNode.modifier,v=s.hierNode.modifier;s=ui(s),n=hi(n),s&&n;){i=ui(i),o=hi(o),i.hierNode.ancestor=a;var c=s.hierNode.prelim+v-n.hierNode.prelim-u+r(s,n);c>0&&(wd(_d(s,a,t),a,c),u+=c,l+=c),v+=s.hierNode.modifier,u+=n.hierNode.modifier,l+=i.hierNode.modifier,h+=o.hierNode.modifier}s&&!ui(i)&&(i.hierNode.thread=s,i.hierNode.modifier+=v-l),n&&!hi(o)&&(o.hierNode.thread=n,o.hierNode.modifier+=u-h,t=a)}return t}function ui(a){var e=a.children;return e.length&&a.isExpand?e[e.length-1]:a.hierNode.thread}function hi(a){var e=a.children;return e.length&&a.isExpand?e[0]:a.hierNode.thread}function _d(a,e,t){return a.hierNode.ancestor.parentNode===e.parentNode?a.hierNode.ancestor:t}function wd(a,e,t){var r=t/(e.hierNode.i-a.hierNode.i);e.hierNode.change-=r,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,a.hierNode.change+=r}function Ad(a,e){return a.parentNode===e.parentNode?1:2}var Td=function(){function a(){this.parentPoint=[],this.childPoints=[]}return a}(),Dd=function(a){E(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Td},e.prototype.buildPath=function(t,r){var i=r.childPoints,n=i.length,o=r.parentPoint,s=i[0],l=i[n-1];if(n===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=r.orient,h=u==="TB"||u==="BT"?0:1,v=1-h,c=O(r.forkPosition,1),f=[];f[h]=o[h],f[v]=o[v]+(l[v]-o[v])*c,t.moveTo(o[0],o[1]),t.lineTo(f[0],f[1]),t.moveTo(s[0],s[1]),f[h]=s[h],t.lineTo(f[0],f[1]),f[h]=l[h],t.lineTo(f[0],f[1]),t.lineTo(l[0],l[1]);for(var p=1;p<n-1;p++){var d=i[p];t.moveTo(d[0],d[1]),f[h]=d[h],t.lineTo(f[0],f[1])}},e}(Ut),Ld=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new $,t}return e.prototype.init=function(t,r){this._controller=new Fr(r.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,r,i){var n=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,i),this._updateController(t,r,i);var u=this._data;n.diff(u).add(function(h){ws(n,h)&&As(n,h,null,s,t)}).update(function(h,v){var c=u.getItemGraphicEl(v);if(!ws(n,h)){c&&Ds(u,v,c,s,t);return}As(n,h,c,s,t)}).remove(function(h){var v=u.getItemGraphicEl(h);v&&Ds(u,h,v,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&n.eachItemGraphicEl(function(h,v){h.off("click").on("click",function(){i.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:v})})}),this._data=n},e.prototype._updateViewCoordSys=function(t,r){var i=t.getData(),n=[];i.each(function(v){var c=i.getItemLayout(v);c&&!isNaN(c.x)&&!isNaN(c.y)&&n.push([+c.x,+c.y])});var o=[],s=[];Uu(n,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var h=t.coordinateSystem=new Hr;h.zoomLimit=t.get("scaleLimit"),h.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),h.setCenter(t.get("center"),r),h.setZoom(t.get("zoom")),this.group.attr({x:h.x,y:h.y,scaleX:h.scaleX,scaleY:h.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,r,i){var n=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,h,v){var c=l.getBoundingRect();return c.applyTransform(l.transform),c.contain(h,v)&&!Mn(u,i,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){$n(s,u.dx,u.dy),i.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){Xn(s,u.scale,u.originX,u.originY),i.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),n._updateNodeAndLinkScale(t),i.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var r=t.getData(),i=this._getNodeGlobalScale(t);r.eachItemGraphicEl(function(n,o){n.setSymbolScale(i)})},e.prototype._getNodeGlobalScale=function(t){var r=t.coordinateSystem;if(r.type!=="view")return 1;var i=this._nodeScaleRatio,n=r.scaleX||1,o=r.getZoom(),s=(o-1)*i+1;return s/n},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(_t);function ws(a,e){var t=a.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function As(a,e,t,r,i){var n=!t,o=a.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",h=a.tree.root,v=o.parentNode===h?o:o.parentNode||o,c=a.getItemGraphicEl(v.dataIndex),f=v.getLayout(),p=c?{x:c.__oldX,y:c.__oldY,rawX:c.__radialOldRawX,rawY:c.__radialOldRawY}:f,d=o.getLayout();n?(t=new Yu(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=d.rawX,t.__radialRawY=d.rawY,r.add(t),a.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,mt(t,{x:d.x,y:d.y},i);var g=t.getSymbolPath();if(i.get("layout")==="radial"){var y=h.children[0],m=y.getLayout(),S=y.children.length,x=void 0,b=void 0;if(d.x===m.x&&o.isExpand===!0&&y.children.length){var _={x:(y.children[0].getLayout().x+y.children[S-1].getLayout().x)/2,y:(y.children[0].getLayout().y+y.children[S-1].getLayout().y)/2};x=Math.atan2(_.y-m.y,_.x-m.x),x<0&&(x=Math.PI*2+x),b=_.x<m.x,b&&(x=x-Math.PI)}else x=Math.atan2(d.y-m.y,d.x-m.x),x<0&&(x=Math.PI*2+x),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(b=d.x<m.x,b&&(x=x-Math.PI)):(b=d.x>m.x,b||(x=x-Math.PI));var w=b?"left":"right",T=s.getModel("label"),L=T.get("rotate"),A=L*(Math.PI/180),C=g.getTextContent();C&&(g.setTextConfig({position:T.get("position")||w,rotation:L==null?-x:A,origin:"center"}),C.setStyle("verticalAlign","middle"))}var I=s.get(["emphasis","focus"]),M=I==="relative"?pa(o.getAncestorsIndices(),o.getDescendantIndices()):I==="ancestor"?o.getAncestorsIndices():I==="descendant"?o.getDescendantIndices():null;M&&(nt(t).focus=M),Cd(i,o,h,t,p,f,d,r),t.__edge&&(t.onHoverStateChange=function(P){if(P!=="blur"){var R=o.parentNode&&a.getItemGraphicEl(o.parentNode.dataIndex);R&&R.hoverState===Sc||Fu(t.__edge,P)}})}function Cd(a,e,t,r,i,n,o,s){var l=e.getModel(),u=a.get("edgeShape"),h=a.get("layout"),v=a.getOrient(),c=a.get(["lineStyle","curveness"]),f=a.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),d=r.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(d||(d=r.__edge=new $u({shape:hn(h,v,c,i,i)})),mt(d,{shape:hn(h,v,c,n,o)},a));else if(u==="polyline"&&h==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var g=e.children,y=[],m=0;m<g.length;m++){var S=g[m].getLayout();y.push([S.x,S.y])}d||(d=r.__edge=new Dd({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:v,forkPosition:f}})),mt(d,{shape:{parentPoint:[o.x,o.y],childPoints:y}},a)}d&&!(u==="polyline"&&!e.isExpand)&&(d.useStyle(ft({strokeNoScale:!0,fill:null},p)),Qt(d,l,"lineStyle"),qe(d),s.add(d))}function Ts(a,e,t,r,i){var n=e.tree.root,o=Rh(n,a),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(a.dataIndex);if(u){var h=e.getItemGraphicEl(s.dataIndex),v=h.__edge,c=u.__edge||(s.isExpand===!1||s.children.length===1?v:void 0),f=r.get("edgeShape"),p=r.get("layout"),d=r.get("orient"),g=r.get(["lineStyle","curveness"]);c&&(f==="curve"?da(c,{shape:hn(p,d,g,l,l),style:{opacity:0}},r,{cb:function(){t.remove(c)},removeOpt:i}):f==="polyline"&&r.get("layout")==="orthogonal"&&da(c,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},r,{cb:function(){t.remove(c)},removeOpt:i}))}}function Rh(a,e){for(var t=e.parentNode===a?e:e.parentNode||e,r;r=t.getLayout(),r==null;)t=t.parentNode===a?t:t.parentNode||t;return{source:t,sourceLayout:r}}function Ds(a,e,t,r,i){var n=a.tree.getNodeByDataIndex(e),o=a.tree.root,s=Rh(o,n).sourceLayout,l={duration:i.get("animationDurationUpdate"),easing:i.get("animationEasingUpdate")};da(t,{x:s.x+1,y:s.y+1},i,{cb:function(){r.remove(t),a.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,a.hostModel,{fadeLabel:!0,animation:l}),n.children.forEach(function(u){Ts(u,a,r,i,l)}),Ts(n,a,r,i,l)}function hn(a,e,t,r,i){var n,o,s,l,u,h,v,c;if(a==="radial"){u=r.rawX,v=r.rawY,h=i.rawX,c=i.rawY;var f=pr(u,v),p=pr(u,v+(c-v)*t),d=pr(h,c+(v-c)*t),g=pr(h,c);return{x1:f.x||0,y1:f.y||0,x2:g.x||0,y2:g.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:d.x||0,cpy2:d.y||0}}else u=r.x,v=r.y,h=i.x,c=i.y,(e==="LR"||e==="RL")&&(n=u+(h-u)*t,o=v,s=h+(u-h)*t,l=c),(e==="TB"||e==="BT")&&(n=u,o=v+(c-v)*t,s=h,l=c+(v-c)*t);return{x1:u,y1:v,x2:h,y2:c,cpx1:n,cpy1:o,cpx2:s,cpy2:l}}var Wt=Dt();function Eh(a){var e=a.mainData,t=a.datas;t||(t={main:e},a.datasAttr={main:"data"}),a.datas=a.mainData=null,Vh(e,t,a),D(t,function(r){D(e.TRANSFERABLE_METHODS,function(i){r.wrapMethod(i,it(Id,a))})}),e.wrapMethod("cloneShallow",it(Pd,a)),D(e.CHANGABLE_METHODS,function(r){e.wrapMethod(r,it(Md,a))}),_r(t[e.dataType]===e)}function Id(a,e){if(Vd(this)){var t=F({},Wt(this).datas);t[this.dataType]=e,Vh(e,t,a)}else jn(e,this.dataType,Wt(this).mainData,a);return e}function Md(a,e){return a.struct&&a.struct.update(),e}function Pd(a,e){return D(Wt(e).datas,function(t,r){t!==e&&jn(t.cloneShallow(),r,e,a)}),e}function Rd(a){var e=Wt(this).mainData;return a==null||e==null?e:Wt(e).datas[a]}function Ed(){var a=Wt(this).mainData;return a==null?[{data:a}]:W(pt(Wt(a).datas),function(e){return{type:e,data:Wt(a).datas[e]}})}function Vd(a){return Wt(a).mainData===a}function Vh(a,e,t){Wt(a).datas={},D(e,function(r,i){jn(r,i,a,t)})}function jn(a,e,t,r){Wt(t).datas[e]=a,Wt(a).mainData=t,a.dataType=e,r.struct&&(a[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=a),a.getLinkedData=Rd,a.getLinkedDataAll=Ed}var kd=function(){function a(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return a.prototype.isRemoved=function(){return this.dataIndex<0},a.prototype.eachNode=function(e,t,r){st(e)&&(r=t,t=e,e=null),e=e||{},Q(e)&&(e={order:e});var i=e.order||"preorder",n=this[e.attr||"children"],o;i==="preorder"&&(o=t.call(r,this));for(var s=0;!o&&s<n.length;s++)n[s].eachNode(e,t,r);i==="postorder"&&t.call(r,this)},a.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var r=0;r<this.children.length;r++){var i=this.children[r];i.updateDepthAndHeight(e+1),i.height>t&&(t=i.height)}this.height=t+1},a.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,r=this.children,i=r.length;t<i;t++){var n=r[t].getNodeById(e);if(n)return n}},a.prototype.contains=function(e){if(e===this)return!0;for(var t=0,r=this.children,i=r.length;t<i;t++){var n=r[t].contains(e);if(n)return n}},a.prototype.getAncestors=function(e){for(var t=[],r=e?this:this.parentNode;r;)t.push(r),r=r.parentNode;return t.reverse(),t},a.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},a.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},a.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},a.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},a.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},a.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},a.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},a.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},a.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},a.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},a.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},a.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},a}(),Jn=function(){function a(e){this.type="tree",this._nodes=[],this.hostModel=e}return a.prototype.eachNode=function(e,t,r){this.root.eachNode(e,t,r)},a.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},a.prototype.getNodeById=function(e){return this.root.getNodeById(e)},a.prototype.update=function(){for(var e=this.data,t=this._nodes,r=0,i=t.length;r<i;r++)t[r].dataIndex=-1;for(var r=0,i=e.count();r<i;r++)t[e.getRawIndex(r)].dataIndex=r},a.prototype.clearLayouts=function(){this.data.clearItemLayouts()},a.createTree=function(e,t,r){var i=new a(t),n=[],o=1;s(e);function s(h,v){var c=h.value;o=Math.max(o,H(c)?c.length:1),n.push(h);var f=new kd(oe(h.name,""),i);v?Nd(f,v):i.root=f,i._nodes.push(f);var p=h.children;if(p)for(var d=0;d<p.length;d++)s(p[d],f)}i.root.updateDepthAndHeight(0);var l=En(n,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new kt(l,t);return u.initData(n),r&&r(u),Eh({mainData:u,struct:i,structAttr:"tree"}),i.update(),i},a}();function Nd(a,e){var t=e.children;a.parentNode!==e&&(t.push(a),a.parentNode=e)}function Dr(a,e,t){if(a&&Mt(e,a.type)>=0){var r=t.getData().tree.root,i=a.targetNode;if(Q(i)&&(i=r.getNodeById(i)),i&&r.contains(i))return{node:i};var n=a.targetNodeId;if(n!=null&&(i=r.getNodeById(n)))return{node:i}}}function kh(a){for(var e=[];a;)a=a.parentNode,a&&e.push(a);return e.reverse()}function Qn(a,e){var t=kh(a);return Mt(t,e)>=0}function qa(a,e){for(var t=[];a;){var r=a.dataIndex;t.push({name:a.name,dataIndex:r,value:e.getRawValue(r)}),a=a.parentNode}return t.reverse(),t}var zd=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var r={name:t.name,children:t.data},i=t.leaves||{},n=new Jt(i,this,this.ecModel),o=Jn.createTree(r,this,s);function s(v){v.wrapMethod("getItemModel",function(c,f){var p=o.getNodeByDataIndex(f);return p&&p.children.length&&p.isExpand||(c.parentModel=n),c})}var l=0;o.eachNode("preorder",function(v){v.depth>l&&(l=v.depth)});var u=t.expandAndCollapse,h=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(v){var c=v.hostTree.data.getRawDataItem(v.dataIndex);v.isExpand=c&&c.collapsed!=null?!c.collapsed:v.depth<=h}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,r,i){for(var n=this.getData().tree,o=n.root.children[0],s=n.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return Zt("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=qa(i,this),r.collapsed=!i.isExpand,r},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(wt);function Od(a,e,t){for(var r=[a],i=[],n;n=r.pop();)if(i.push(n),n.isExpand){var o=n.children;if(o.length)for(var s=0;s<o.length;s++)r.push(o[s])}for(;n=i.pop();)e(n,t)}function or(a,e){for(var t=[a],r;r=t.pop();)if(e(r),r.isExpand){var i=r.children;if(i.length)for(var n=i.length-1;n>=0;n--)t.push(i[n])}}function Gd(a,e){a.eachSeriesByType("tree",function(t){Bd(t,e)})}function Bd(a,e){var t=Sd(a,e);a.layoutInfo=t;var r=a.get("layout"),i=0,n=0,o=null;r==="radial"?(i=2*Math.PI,n=Math.min(t.height,t.width)/2,o=_s(function(S,x){return(S.parentNode===x.parentNode?1:2)/S.depth})):(i=t.width,n=t.height,o=_s());var s=a.getData().tree.root,l=s.children[0];if(l){gd(s),Od(l,yd,o),s.hierNode.modifier=-l.hierNode.prelim,or(l,md);var u=l,h=l,v=l;or(l,function(S){var x=S.getLayout().x;x<u.getLayout().x&&(u=S),x>h.getLayout().x&&(h=S),S.depth>v.depth&&(v=S)});var c=u===h?1:o(u,h)/2,f=c-u.getLayout().x,p=0,d=0,g=0,y=0;if(r==="radial")p=i/(h.getLayout().x+c+f),d=n/(v.depth-1||1),or(l,function(S){g=(S.getLayout().x+f)*p,y=(S.depth-1)*d;var x=pr(g,y);S.setLayout({x:x.x,y:x.y,rawX:g,rawY:y},!0)});else{var m=a.getOrient();m==="RL"||m==="LR"?(d=n/(h.getLayout().x+c+f),p=i/(v.depth-1||1),or(l,function(S){y=(S.getLayout().x+f)*d,g=m==="LR"?(S.depth-1)*p:i-(S.depth-1)*p,S.setLayout({x:g,y},!0)})):(m==="TB"||m==="BT")&&(p=i/(h.getLayout().x+c+f),d=n/(v.depth-1||1),or(l,function(S){g=(S.getLayout().x+f)*p,y=m==="TB"?(S.depth-1)*d:n-(S.depth-1)*d,S.setLayout({x:g,y},!0)}))}}}function Fd(a){a.eachSeriesByType("tree",function(e){var t=e.getData(),r=t.tree;r.eachNode(function(i){var n=i.getModel(),o=n.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(i.dataIndex,"style");F(s,o)})})}function Hd(a){a.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(r){var i=e.dataIndex,n=r.getData().tree,o=n.getNodeByDataIndex(i);o.isExpand=!o.isExpand})}),a.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,r){t.eachComponent({mainType:"series",subType:"tree",query:e},function(i){var n=i.coordinateSystem,o=qn(n,e,void 0,r);i.setCenter&&i.setCenter(o.center),i.setZoom&&i.setZoom(o.zoom)})})}function Wd(a){a.registerChartView(Ld),a.registerSeriesModel(zd),a.registerLayout(Gd),a.registerVisual(Fd),Hd(a)}var Ls=["treemapZoomToNode","treemapRender","treemapMove"];function Zd(a){for(var e=0;e<Ls.length;e++)a.registerAction({type:Ls[e],update:"updateView"},Ge);a.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,r){r.eachComponent({mainType:"series",subType:"treemap",query:t},i);function i(n,o){var s=["treemapZoomToNode","treemapRootToNode"],l=Dr(t,s,n);if(l){var u=n.getViewRoot();u&&(t.direction=Qn(u,l.node)?"rollUp":"drillDown"),n.resetViewRoot(l.node)}}})}function Nh(a){var e=a.getData(),t=e.tree,r={};t.eachNode(function(i){for(var n=i;n&&n.depth>1;)n=n.parentNode;var o=tn(a.ecModel,n.name||n.dataIndex+"",r);i.setVisual("decal",o)})}var Ud=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,r){var i={name:t.name,children:t.data};zh(i);var n=t.levels||[],o=this.designatedVisualItemStyle={},s=new Jt({itemStyle:o},this,r);n=t.levels=Yd(n,r);var l=W(n||[],function(v){return new Jt(v,s,r)},this),u=Jn.createTree(i,this,h);function h(v){v.wrapMethod("getItemModel",function(c,f){var p=u.getNodeByDataIndex(f),d=p?l[p.depth]:null;return c.parentModel=d||s,c})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=this.getRawValue(t),s=n.getName(t);return Zt("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=qa(i,this),r.treePathInfo=r.treeAncestors,r},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},F(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var r=this._idIndexMap;r||(r=this._idIndexMap=rt(),this._idIndexMapCount=0);var i=r.get(t);return i==null&&r.set(t,i=this._idIndexMapCount++),i},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){Nh(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(wt);function zh(a){var e=0;D(a.children,function(r){zh(r);var i=r.value;H(i)&&(i=i[0]),e+=i});var t=a.value;H(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),H(a.value)?a.value[0]=t:a.value=t}function Yd(a,e){var t=ue(e.get("color")),r=ue(e.get(["aria","decal","decals"]));if(t){a=a||[];var i,n;D(a,function(s){var l=new Jt(s),u=l.get("color"),h=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(i=!0),(l.get(["itemStyle","decal"])||h&&h!=="none")&&(n=!0)});var o=a[0]||(a[0]={});return i||(o.color=t.slice()),!n&&r&&(o.decal=r.slice()),a}}var $d=8,Cs=8,vi=5,Xd=function(){function a(e){this.group=new $,e.add(this.group)}return a.prototype.render=function(e,t,r,i){var n=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!n.get("show")||!r)){var s=n.getModel("itemStyle"),l=n.getModel("emphasis"),u=s.getModel("textStyle"),h=l.getModel(["itemStyle","textStyle"]),v={pos:{left:n.get("left"),right:n.get("right"),top:n.get("top"),bottom:n.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:n.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(r,v,u),this._renderContent(e,v,s,l,u,h,i),Vn(o,v.pos,v.box)}},a.prototype._prepare=function(e,t,r){for(var i=e;i;i=i.parentNode){var n=oe(i.getModel().get("name"),""),o=r.getTextRect(n),s=Math.max(o.width+$d*2,t.emptyItemWidth);t.totalWidth+=s+Cs,t.renderList.push({node:i,text:n,width:s})}},a.prototype._renderContent=function(e,t,r,i,n,o,s){for(var l=0,u=t.emptyItemWidth,h=e.get(["breadcrumb","height"]),v=xc(t.pos,t.box),c=t.totalWidth,f=t.renderList,p=i.getModel("itemStyle").getItemStyle(),d=f.length-1;d>=0;d--){var g=f[d],y=g.node,m=g.width,S=g.text;c>v.width&&(c-=m-u,m=u,S=null);var x=new _e({shape:{points:Kd(l,0,m,h,d===f.length-1,d===0)},style:ft(r.getItemStyle(),{lineJoin:"bevel"}),textContent:new ut({style:ct(n,{text:S})}),textConfig:{position:"inside"},z2:Vr*1e4,onclick:it(s,y)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=ct(o,{text:S}),x.ensureState("emphasis").style=p,St(x,i.get("focus"),i.get("blurScope"),i.get("disabled")),this.group.add(x),qd(x,e,y),l+=m+Cs}},a.prototype.remove=function(){this.group.removeAll()},a}();function Kd(a,e,t,r,i,n){var o=[[i?a:a-vi,e],[a+t,e],[a+t,e+r],[i?a:a-vi,e+r]];return!n&&o.splice(2,0,[a+t+vi,e+r/2]),!i&&o.push([a,e+r/2]),o}function qd(a,e,t){nt(a).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&qa(t,e)}}var jd=function(){function a(){this._storage=[],this._elExistsMap={}}return a.prototype.add=function(e,t,r,i,n){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:r,delay:i,easing:n}),!0)},a.prototype.finished=function(e){return this._finishedCallback=e,this},a.prototype.start=function(){for(var e=this,t=this._storage.length,r=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},i=0,n=this._storage.length;i<n;i++){var o=this._storage[i];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:r,aborted:r})}return this},a}();function Jd(){return new jd}var vn=$,Is=gt,Ms=3,Ps="label",Rs="upperLabel",Qd=Vr*10,tg=Vr*2,eg=Vr*3,Ie=Ku([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Es=function(a){var e=Ie(a);return e.stroke=e.fill=e.lineWidth=null,e},wa=Dt(),rg=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=sr(),t}return e.prototype.render=function(t,r,i,n){var o=r.findComponents({mainType:"series",subType:"treemap",query:n});if(!(Mt(o,t)<0)){this.seriesModel=t,this.api=i,this.ecModel=r;var s=["treemapZoomToNode","treemapRootToNode"],l=Dr(n,s,t),u=n&&n.type,h=t.layoutInfo,v=!this._oldTree,c=this._storage,f=u==="treemapRootToNode"&&l&&c?{rootNodeGroup:c.nodeGroup[l.node.getRawIndex()],direction:n.direction}:null,p=this._giveContainerGroup(h),d=t.get("animation"),g=this._doRender(p,t,f);d&&!v&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,g,t,f):g.renderFinally(),this._resetController(i),this._renderBreadcrumb(t,i,l)}},e.prototype._giveContainerGroup=function(t){var r=this._containerGroup;return r||(r=this._containerGroup=new vn,this._initEvents(r),this.group.add(r)),r.x=t.x,r.y=t.y,r},e.prototype._doRender=function(t,r,i){var n=r.getData().tree,o=this._oldTree,s=sr(),l=sr(),u=this._storage,h=[];function v(m,S,x,b){return ag(r,l,u,i,s,h,m,S,x,b)}d(n.root?[n.root]:[],o&&o.root?[o.root]:[],t,n===o||!o,0);var c=g(u);if(this._oldTree=n,this._storage=l,this._controllerHost){var f=this.seriesModel.layoutInfo,p=n.root.getLayout();p.width===f.width&&p.height===f.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:s,willDeleteEls:c,renderFinally:y};function d(m,S,x,b,_){b?(S=m,D(m,function(L,A){!L.isRemoved()&&T(A,A)})):new Fa(S,m,w,w).add(T).update(T).remove(it(T,null)).execute();function w(L){return L.getId()}function T(L,A){var C=L!=null?m[L]:null,I=A!=null?S[A]:null,M=v(C,I,x,_);M&&d(C&&C.viewChildren||[],I&&I.viewChildren||[],M,b,_+1)}}function g(m){var S=sr();return m&&D(m,function(x,b){var _=S[b];D(x,function(w){w&&(_.push(w),wa(w).willDelete=!0)})}),S}function y(){D(c,function(m){D(m,function(S){S.parent&&S.parent.remove(S)})}),D(h,function(m){m.invisible=!0,m.dirty()})}},e.prototype._doAnimation=function(t,r,i,n){var o=i.get("animationDurationUpdate"),s=i.get("animationEasing"),l=(st(o)?0:o)||0,u=(st(s)?null:s)||"cubicOut",h=Jd();D(r.willDeleteEls,function(v,c){D(v,function(f,p){if(!f.invisible){var d=f.parent,g,y=wa(d);if(n&&n.direction==="drillDown")g=d===n.rootNodeGroup?{shape:{x:0,y:0,width:y.nodeWidth,height:y.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var m=0,S=0;y.willDelete||(m=y.nodeWidth/2,S=y.nodeHeight/2),g=c==="nodeGroup"?{x:m,y:S,style:{opacity:0}}:{shape:{x:m,y:S,width:0,height:0},style:{opacity:0}}}g&&h.add(f,g,l,0,u)}})}),D(this._storage,function(v,c){D(v,function(f,p){var d=r.lastsForAnimation[c][p],g={};d&&(f instanceof $?d.oldX!=null&&(g.x=f.x,g.y=f.y,f.x=d.oldX,f.y=d.oldY):(d.oldShape&&(g.shape=F({},f.shape),f.setShape(d.oldShape)),d.fadein?(f.setStyle("opacity",0),g.style={opacity:1}):f.style.opacity!==1&&(g.style={opacity:1})),h.add(f,g,l,0,u))})},this),this._state="animating",h.finished(B(function(){this._state="ready",r.renderFinally()},this)).start()},e.prototype._resetController=function(t){var r=this._controller,i=this._controllerHost;i||(this._controllerHost={target:this.group},i=this._controllerHost),r||(r=this._controller=new Fr(t.getZr()),r.enable(this.seriesModel.get("roam")),i.zoomLimit=this.seriesModel.get("scaleLimit"),i.zoom=this.seriesModel.get("zoom"),r.on("pan",B(this._onPan,this)),r.on("zoom",B(this._onZoom,this)));var n=new bt(0,0,t.getWidth(),t.getHeight());r.setPointerChecker(function(o,s,l){return n.contain(s,l)})},e.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>Ms||Math.abs(t.dy)>Ms)){var r=this.seriesModel.getData().tree.root;if(!r)return;var i=r.getLayout();if(!i)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:i.x+t.dx,y:i.y+t.dy,width:i.width,height:i.height}})}},e.prototype._onZoom=function(t){var r=t.originX,i=t.originY,n=t.scale;if(this._state!=="animating"){var o=this.seriesModel.getData().tree.root;if(!o)return;var s=o.getLayout();if(!s)return;var l=new bt(s.x,s.y,s.width,s.height),u=null,h=this._controllerHost;u=h.zoomLimit;var v=h.zoom=h.zoom||1;if(v*=n,u){var c=u.min||0,f=u.max||1/0;v=Math.max(Math.min(f,v),c)}var p=v/h.zoom;h.zoom=v;var d=this.seriesModel.layoutInfo;r-=d.x,i-=d.y;var g=tr();Oe(g,g,[-r,-i]),ku(g,g,[p,p]),Oe(g,g,[r,i]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},e.prototype._initEvents=function(t){var r=this;t.on("click",function(i){if(r._state==="ready"){var n=r.seriesModel.get("nodeClick",!0);if(n){var o=r.findTarget(i.offsetX,i.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)r._rootToNode(o);else if(n==="zoomToNode")r._zoomToNode(o);else if(n==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),h=l.get("target",!0)||"blank";u&&Xu(u,h)}}}}},this)},e.prototype._renderBreadcrumb=function(t,r,i){var n=this;i||(i=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(r.getWidth()/2,r.getHeight()/2),i||(i={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new Xd(this.group))).render(t,r,i.node,function(o){n._state!=="animating"&&(Qn(t.getViewRoot(),o)?n._rootToNode({node:o}):n._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=sr(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,r){var i,n=this.seriesModel.getViewRoot();return n.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,r),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)i={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),i},e.type="treemap",e}(_t);function sr(){return{nodeGroup:[],background:[],content:[]}}function ag(a,e,t,r,i,n,o,s,l,u){if(!o)return;var h=o.getLayout(),v=a.getData(),c=o.getModel();if(v.setItemGraphicEl(o.dataIndex,null),!h||!h.isInView)return;var f=h.width,p=h.height,d=h.borderWidth,g=h.invisible,y=o.getRawIndex(),m=s&&s.getRawIndex(),S=o.viewChildren,x=h.upperHeight,b=S&&S.length,_=c.getModel("itemStyle"),w=c.getModel(["emphasis","itemStyle"]),T=c.getModel(["blur","itemStyle"]),L=c.getModel(["select","itemStyle"]),A=_.get("borderRadius")||0,C=tt("nodeGroup",vn);if(!C)return;if(l.add(C),C.x=h.x||0,C.y=h.y||0,C.markRedraw(),wa(C).nodeWidth=f,wa(C).nodeHeight=p,h.isAboveViewRoot)return C;var I=tt("background",Is,u,tg);I&&Z(C,I,b&&h.upperLabelHeight);var M=c.getModel("emphasis"),P=M.get("focus"),R=M.get("blurScope"),V=M.get("disabled"),k=P==="ancestor"?o.getAncestorsIndices():P==="descendant"?o.getDescendantIndices():P;if(b)Vo(C)&&cr(C,!1),I&&(cr(I,!V),v.setItemGraphicEl(o.dataIndex,I),ko(I,k,R));else{var N=tt("content",Is,u,eg);N&&U(C,N),I.disableMorphing=!0,I&&Vo(I)&&cr(I,!1),cr(C,!V),v.setItemGraphicEl(o.dataIndex,C);var G=c.getShallow("cursor");G&&N.attr("cursor",G),ko(C,k,R)}return C;function Z(j,Y,ot){var et=nt(Y);if(et.dataIndex=o.dataIndex,et.seriesIndex=a.seriesIndex,Y.setShape({x:0,y:0,width:f,height:p,r:A}),g)K(Y);else{Y.invisible=!1;var lt=o.getVisual("style"),xt=lt.stroke,Ct=Es(_);Ct.fill=xt;var vt=Ie(w);vt.fill=w.get("borderColor");var At=Ie(T);At.fill=T.get("borderColor");var Rt=Ie(L);if(Rt.fill=L.get("borderColor"),ot){var Xt=f-2*d;J(Y,xt,lt.opacity,{x:d,y:0,width:Xt,height:x})}else Y.removeTextContent();Y.setStyle(Ct),Y.ensureState("emphasis").style=vt,Y.ensureState("blur").style=At,Y.ensureState("select").style=Rt,qe(Y)}j.add(Y)}function U(j,Y){var ot=nt(Y);ot.dataIndex=o.dataIndex,ot.seriesIndex=a.seriesIndex;var et=Math.max(f-2*d,0),lt=Math.max(p-2*d,0);if(Y.culling=!0,Y.setShape({x:d,y:d,width:et,height:lt,r:A}),g)K(Y);else{Y.invisible=!1;var xt=o.getVisual("style"),Ct=xt.fill,vt=Es(_);vt.fill=Ct,vt.decal=xt.decal;var At=Ie(w),Rt=Ie(T),Xt=Ie(L);J(Y,Ct,xt.opacity,null),Y.setStyle(vt),Y.ensureState("emphasis").style=At,Y.ensureState("blur").style=Rt,Y.ensureState("select").style=Xt,qe(Y)}j.add(Y)}function K(j){!j.invisible&&n.push(j)}function J(j,Y,ot,et){var lt=c.getModel(et?Rs:Ps),xt=oe(c.get("name"),null),Ct=lt.getShallow("show");jt(j,Nt(c,et?Rs:Ps),{defaultText:Ct?xt:null,inheritColor:Y,defaultOpacity:ot,labelFetcher:a,labelDataIndex:o.dataIndex});var vt=j.getTextContent();if(vt){var At=vt.style,Rt=bc(At.padding||0);et&&(j.setTextConfig({layoutRect:et}),vt.disableLabelLayout=!0),vt.beforeUpdate=function(){var Te=Math.max((et?et.width:j.shape.width)-Rt[1]-Rt[3],0),ir=Math.max((et?et.height:j.shape.height)-Rt[0]-Rt[2],0);(At.width!==Te||At.height!==ir)&&vt.setStyle({width:Te,height:ir})},At.truncateMinChar=2,At.lineOverflow="truncate",q(At,et,h);var Xt=vt.getState("emphasis");q(Xt?Xt.style:null,et,h)}}function q(j,Y,ot){var et=j?j.text:null;if(!Y&&ot.isLeafRoot&&et!=null){var lt=a.get("drillDownIcon",!0);j.text=lt?lt+" "+et:et}}function tt(j,Y,ot,et){var lt=m!=null&&t[j][m],xt=i[j];return lt?(t[j][m]=null,Lt(xt,lt)):g||(lt=new Y,lt instanceof Ke&&(lt.z2=ig(ot,et)),ge(xt,lt)),e[j][y]=lt}function Lt(j,Y){var ot=j[y]={};Y instanceof vn?(ot.oldX=Y.x,ot.oldY=Y.y):ot.oldShape=F({},Y.shape)}function ge(j,Y){var ot=j[y]={},et=o.parentNode,lt=Y instanceof $;if(et&&(!r||r.direction==="drillDown")){var xt=0,Ct=0,vt=i.background[et.getRawIndex()];!r&&vt&&vt.oldShape&&(xt=vt.oldShape.width,Ct=vt.oldShape.height),lt?(ot.oldX=0,ot.oldY=Ct):ot.oldShape={x:xt,y:Ct,width:0,height:0}}ot.fadein=!lt}}function ig(a,e){return a*Qd+e}var Lr=D,ng=Ot,Aa=-1,yt=function(){function a(e){var t=e.mappingMethod,r=e.type,i=this.option=ht(e);this.type=r,this.mappingMethod=t,this._normalizeData=lg[t];var n=a.visualHandlers[r];this.applyVisual=n.applyVisual,this.getColorMapper=n.getColorMapper,this._normalizedToVisual=n._normalizedToVisual[t],t==="piecewise"?(ci(i),og(i)):t==="category"?i.categories?sg(i):ci(i,!0):(_r(t!=="linear"||i.dataExtent),ci(i))}return a.prototype.mapValueToVisual=function(e){var t=this._normalizeData(e);return this._normalizedToVisual(t,e)},a.prototype.getNormalizer=function(){return B(this._normalizeData,this)},a.listVisualTypes=function(){return pt(a.visualHandlers)},a.isValidType=function(e){return a.visualHandlers.hasOwnProperty(e)},a.eachVisual=function(e,t,r){Ot(e)?D(e,t,r):t.call(r,e)},a.mapVisual=function(e,t,r){var i,n=H(e)?[]:Ot(e)?{}:(i=!0,null);return a.eachVisual(e,function(o,s){var l=t.call(r,o,s);i?n=l:n[s]=l}),n},a.retrieveVisuals=function(e){var t={},r;return e&&Lr(a.visualHandlers,function(i,n){e.hasOwnProperty(n)&&(t[n]=e[n],r=!0)}),r?t:null},a.prepareVisualTypes=function(e){if(H(e))e=e.slice();else if(ng(e)){var t=[];Lr(e,function(r,i){t.push(i)}),e=t}else return[];return e.sort(function(r,i){return i==="color"&&r!=="color"&&r.indexOf("color")===0?1:-1}),e},a.dependsOn=function(e,t){return t==="color"?!!(e&&e.indexOf(t)===0):e===t},a.findPieceIndex=function(e,t,r){for(var i,n=1/0,o=0,s=t.length;o<s;o++){var l=t[o].value;if(l!=null){if(l===e||Q(l)&&l===e+"")return o;r&&c(l,o)}}for(var o=0,s=t.length;o<s;o++){var u=t[o],h=u.interval,v=u.close;if(h){if(h[0]===-1/0){if(Xr(v[1],e,h[1]))return o}else if(h[1]===1/0){if(Xr(v[0],h[0],e))return o}else if(Xr(v[0],h[0],e)&&Xr(v[1],e,h[1]))return o;r&&c(h[0],o),r&&c(h[1],o)}}if(r)return e===1/0?t.length-1:e===-1/0?0:i;function c(f,p){var d=Math.abs(f-e);d<n&&(n=d,i=p)}},a.visualHandlers={color:{applyVisual:lr("color"),getColorMapper:function(){var e=this.option;return B(e.mappingMethod==="category"?function(t,r){return!r&&(t=this._normalizeData(t)),dr.call(this,t)}:function(t,r,i){var n=!!i;return!r&&(t=this._normalizeData(t)),i=ei(t,e.parsedVisual,i),n?i:ti(i,"rgba")},this)},_normalizedToVisual:{linear:function(e){return ti(ei(e,this.option.parsedVisual),"rgba")},category:dr,piecewise:function(e,t){var r=fn.call(this,t);return r==null&&(r=ti(ei(e,this.option.parsedVisual),"rgba")),r},fixed:Me}},colorHue:$r(function(e,t){return Sr(e,t)}),colorSaturation:$r(function(e,t){return Sr(e,null,t)}),colorLightness:$r(function(e,t){return Sr(e,null,null,t)}),colorAlpha:$r(function(e,t){return ga(e,t)}),decal:{applyVisual:lr("decal"),_normalizedToVisual:{linear:null,category:dr,piecewise:null,fixed:null}},opacity:{applyVisual:lr("opacity"),_normalizedToVisual:cn([0,1])},liftZ:{applyVisual:lr("liftZ"),_normalizedToVisual:{linear:Me,category:Me,piecewise:Me,fixed:Me}},symbol:{applyVisual:function(e,t,r){var i=this.mapValueToVisual(e);r("symbol",i)},_normalizedToVisual:{linear:Vs,category:dr,piecewise:function(e,t){var r=fn.call(this,t);return r==null&&(r=Vs.call(this,e)),r},fixed:Me}},symbolSize:{applyVisual:lr("symbolSize"),_normalizedToVisual:cn([0,1])}},a}();function og(a){var e=a.pieceList;a.hasSpecialVisual=!1,D(e,function(t,r){t.originIndex=r,t.visual!=null&&(a.hasSpecialVisual=!0)})}function sg(a){var e=a.categories,t=a.categoryMap={},r=a.visual;if(Lr(e,function(o,s){t[o]=s}),!H(r)){var i=[];Ot(r)?Lr(r,function(o,s){var l=t[s];i[l??Aa]=o}):i[Aa]=r,r=Oh(a,i)}for(var n=e.length-1;n>=0;n--)r[n]==null&&(delete t[e[n]],e.pop())}function ci(a,e){var t=a.visual,r=[];Ot(t)?Lr(t,function(n){r.push(n)}):t!=null&&r.push(t);var i={color:1,symbol:1};!e&&r.length===1&&!i.hasOwnProperty(a.type)&&(r[1]=r[0]),Oh(a,r)}function $r(a){return{applyVisual:function(e,t,r){var i=this.mapValueToVisual(e);r("color",a(t("color"),i))},_normalizedToVisual:cn([0,1])}}function Vs(a){var e=this.option.visual;return e[Math.round(at(a,[0,1],[0,e.length-1],!0))]||{}}function lr(a){return function(e,t,r){r(a,this.mapValueToVisual(e))}}function dr(a){var e=this.option.visual;return e[this.option.loop&&a!==Aa?a%e.length:a]}function Me(){return this.option.visual[0]}function cn(a){return{linear:function(e){return at(e,a,this.option.visual,!0)},category:dr,piecewise:function(e,t){var r=fn.call(this,t);return r==null&&(r=at(e,a,this.option.visual,!0)),r},fixed:Me}}function fn(a){var e=this.option,t=e.pieceList;if(e.hasSpecialVisual){var r=yt.findPieceIndex(a,t),i=t[r];if(i&&i.visual)return i.visual[this.type]}}function Oh(a,e){return a.visual=e,a.type==="color"&&(a.parsedVisual=W(e,function(t){var r=_c(t);return r||[0,0,0,1]})),e}var lg={linear:function(a){return at(a,this.option.dataExtent,[0,1],!0)},piecewise:function(a){var e=this.option.pieceList,t=yt.findPieceIndex(a,e,!0);if(t!=null)return at(t,[0,e.length-1],[0,1],!0)},category:function(a){var e=this.option.categories?this.option.categoryMap[a]:a;return e??Aa},fixed:Ge};function Xr(a,e,t){return a?e<=t:e<t}var ug="itemStyle",Gh=Dt();const hg={seriesType:"treemap",reset:function(a){var e=a.getData().tree,t=e.root;t.isRemoved()||Bh(t,{},a.getViewRoot().getAncestors(),a)}};function Bh(a,e,t,r){var i=a.getModel(),n=a.getLayout(),o=a.hostTree.data;if(!(!n||n.invisible||!n.isInView)){var s=i.getModel(ug),l=vg(s,e,r),u=o.ensureUniqueItemVisual(a.dataIndex,"style"),h=s.get("borderColor"),v=s.get("borderColorSaturation"),c;v!=null&&(c=ks(l),h=cg(v,c)),u.stroke=h;var f=a.viewChildren;if(!f||!f.length)c=ks(l),u.fill=c;else{var p=fg(a,i,n,s,l,f);D(f,function(d,g){if(d.depth>=t.length||d===t[d.depth]){var y=pg(i,l,d,g,p,r);Bh(d,y,t,r)}})}}}function vg(a,e,t){var r=F({},e),i=t.designatedVisualItemStyle;return D(["color","colorAlpha","colorSaturation"],function(n){i[n]=e[n];var o=a.get(n);i[n]=null,o!=null&&(r[n]=o)}),r}function ks(a){var e=fi(a,"color");if(e){var t=fi(a,"colorAlpha"),r=fi(a,"colorSaturation");return r&&(e=Sr(e,null,null,r)),t&&(e=ga(e,t)),e}}function cg(a,e){return e!=null?Sr(e,null,null,a):null}function fi(a,e){var t=a[e];if(t!=null&&t!=="none")return t}function fg(a,e,t,r,i,n){if(!(!n||!n.length)){var o=pi(e,"color")||i.color!=null&&i.color!=="none"&&(pi(e,"colorAlpha")||pi(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var h=e.get("colorMappingBy"),v={type:o.name,dataExtent:u,visual:o.range};v.type==="color"&&(h==="index"||h==="id")?(v.mappingMethod="category",v.loop=!0):v.mappingMethod="linear";var c=new yt(v);return Gh(c).drColorMappingBy=h,c}}}function pi(a,e){var t=a.get(e);return H(t)&&t.length?{name:e,range:t}:null}function pg(a,e,t,r,i,n){var o=F({},e);if(i){var s=i.type,l=s==="color"&&Gh(i).drColorMappingBy,u=l==="index"?r:l==="id"?n.mapIdToIndex(t.getId()):t.getValue(a.get("visualDimension"));o[s]=i.mapValueToVisual(u)}return o}var Cr=Math.max,Ta=Math.min,Ns=Vt,to=D,Fh=["itemStyle","borderWidth"],dg=["itemStyle","gapWidth"],gg=["upperLabel","show"],yg=["upperLabel","height"];const mg={seriesType:"treemap",reset:function(a,e,t,r){var i=t.getWidth(),n=t.getHeight(),o=a.option,s=Yt(a.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=O(Ns(s.width,l[0]),i),h=O(Ns(s.height,l[1]),n),v=r&&r.type,c=["treemapZoomToNode","treemapRootToNode"],f=Dr(r,c,a),p=v==="treemapRender"||v==="treemapMove"?r.rootRect:null,d=a.getViewRoot(),g=kh(d);if(v!=="treemapMove"){var y=v==="treemapZoomToNode"?Ag(a,f,d,u,h):p?[p.width,p.height]:[u,h],m=o.sort;m&&m!=="asc"&&m!=="desc"&&(m="desc");var S={squareRatio:o.squareRatio,sort:m,leafDepth:o.leafDepth};d.hostTree.clearLayouts();var x={x:0,y:0,width:y[0],height:y[1],area:y[0]*y[1]};d.setLayout(x),Hh(d,S,!1,0),x=d.getLayout(),to(g,function(_,w){var T=(g[w+1]||d).getValue();_.setLayout(F({dataExtent:[T,T],borderWidth:0,upperHeight:0},x))})}var b=a.getData().tree.root;b.setLayout(Tg(s,p,f),!0),a.setLayoutInfo(s),Wh(b,new bt(-s.x,-s.y,i,n),g,d,0)}};function Hh(a,e,t,r){var i,n;if(!a.isRemoved()){var o=a.getLayout();i=o.width,n=o.height;var s=a.getModel(),l=s.get(Fh),u=s.get(dg)/2,h=Zh(s),v=Math.max(l,h),c=l-u,f=v-u;a.setLayout({borderWidth:l,upperHeight:v,upperLabelHeight:h},!0),i=Cr(i-2*c,0),n=Cr(n-c-f,0);var p=i*n,d=Sg(a,s,p,e,t,r);if(d.length){var g={x:c,y:f,width:i,height:n},y=Ta(i,n),m=1/0,S=[];S.area=0;for(var x=0,b=d.length;x<b;){var _=d[x];S.push(_),S.area+=_.getLayout().area;var w=wg(S,y,e.squareRatio);w<=m?(x++,m=w):(S.area-=S.pop().getLayout().area,zs(S,y,g,u,!1),y=Ta(g.width,g.height),S.length=S.area=0,m=1/0)}if(S.length&&zs(S,y,g,u,!0),!t){var T=s.get("childrenVisibleMin");T!=null&&p<T&&(t=!0)}for(var x=0,b=d.length;x<b;x++)Hh(d[x],e,t,r+1)}}}function Sg(a,e,t,r,i,n){var o=a.children||[],s=r.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=r.leafDepth!=null&&r.leafDepth<=n;if(i&&!l)return a.viewChildren=[];o=Gt(o,function(f){return!f.isRemoved()}),bg(o,s);var u=_g(e,o,s);if(u.sum===0)return a.viewChildren=[];if(u.sum=xg(e,t,u.sum,s,o),u.sum===0)return a.viewChildren=[];for(var h=0,v=o.length;h<v;h++){var c=o[h].getValue()/u.sum*t;o[h].setLayout({area:c})}return l&&(o.length&&a.setLayout({isLeafRoot:!0},!0),o.length=0),a.viewChildren=o,a.setLayout({dataExtent:u.dataExtent},!0),o}function xg(a,e,t,r,i){if(!r)return t;for(var n=a.get("visibleMin"),o=i.length,s=o,l=o-1;l>=0;l--){var u=i[r==="asc"?o-l-1:l].getValue();u/t*e<n&&(s=l,t-=u)}return r==="asc"?i.splice(0,o-s):i.splice(s,o-s),t}function bg(a,e){return e&&a.sort(function(t,r){var i=e==="asc"?t.getValue()-r.getValue():r.getValue()-t.getValue();return i===0?e==="asc"?t.dataIndex-r.dataIndex:r.dataIndex-t.dataIndex:i}),a}function _g(a,e,t){for(var r=0,i=0,n=e.length;i<n;i++)r+=e[i].getValue();var o=a.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],to(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:r,dataExtent:s}}function wg(a,e,t){for(var r=0,i=1/0,n=0,o=void 0,s=a.length;n<s;n++)o=a[n].getLayout().area,o&&(o<i&&(i=o),o>r&&(r=o));var l=a.area*a.area,u=e*e*t;return l?Cr(u*r/l,l/(u*i)):1/0}function zs(a,e,t,r,i){var n=e===t.width?0:1,o=1-n,s=["x","y"],l=["width","height"],u=t[s[n]],h=e?a.area/e:0;(i||h>t[l[o]])&&(h=t[l[o]]);for(var v=0,c=a.length;v<c;v++){var f=a[v],p={},d=h?f.getLayout().area/h:0,g=p[l[o]]=Cr(h-2*r,0),y=t[s[n]]+t[l[n]]-u,m=v===c-1||y<d?y:d,S=p[l[n]]=Cr(m-2*r,0);p[s[o]]=t[s[o]]+Ta(r,g/2),p[s[n]]=u+Ta(r,S/2),u+=m,f.setLayout(p,!0)}t[s[o]]+=h,t[l[o]]-=h}function Ag(a,e,t,r,i){var n=(e||{}).node,o=[r,i];if(!n||n===t)return o;for(var s,l=r*i,u=l*a.option.zoomToNodeRatio;s=n.parentNode;){for(var h=0,v=s.children,c=0,f=v.length;c<f;c++)h+=v[c].getValue();var p=n.getValue();if(p===0)return o;u*=h/p;var d=s.getModel(),g=d.get(Fh),y=Math.max(g,Zh(d));u+=4*g*g+(3*g+y)*Math.pow(u,.5),u>No&&(u=No),n=s}u<l&&(u=l);var m=Math.pow(u/l,.5);return[r*m,i*m]}function Tg(a,e,t){if(e)return{x:e.x,y:e.y};var r={x:0,y:0};if(!t)return r;var i=t.node,n=i.getLayout();if(!n)return r;for(var o=[n.width/2,n.height/2],s=i;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:a.width/2-o[0],y:a.height/2-o[1]}}function Wh(a,e,t,r,i){var n=a.getLayout(),o=t[i],s=o&&o===a;if(!(o&&!s||i===t.length&&a!==r)){a.setLayout({isInView:!0,invisible:!s&&!e.intersect(n),isAboveViewRoot:s},!0);var l=new bt(e.x-n.x,e.y-n.y,e.width,e.height);to(a.viewChildren||[],function(u){Wh(u,l,t,r,i+1)})}}function Zh(a){return a.get(gg)?a.get(yg):0}function Dg(a){a.registerSeriesModel(Ud),a.registerChartView(rg),a.registerVisual(hg),a.registerLayout(mg),Zd(a)}function Lg(a){var e=a.findComponents({mainType:"legend"});!e||!e.length||a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),i=t.getGraph(),n=i.data,o=r.mapArray(r.getName);n.filterSelf(function(s){var l=n.getItemModel(s),u=l.getShallow("category");if(u!=null){he(u)&&(u=o[u]);for(var h=0;h<e.length;h++)if(!e[h].isSelected(u))return!1}return!0})})}function Cg(a){var e={};a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),i=t.getData(),n={};r.each(function(o){var s=r.getName(o);n["ec-"+s]=o;var l=r.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),r.setItemVisual(o,"style",u);for(var h=["symbol","symbolSize","symbolKeepAspect"],v=0;v<h.length;v++){var c=l.getShallow(h[v],!0);c!=null&&r.setItemVisual(o,h[v],c)}}),r.count()&&i.each(function(o){var s=i.getItemModel(o),l=s.getShallow("category");if(l!=null){Q(l)&&(l=n["ec-"+l]);var u=r.getItemVisual(l,"style"),h=i.ensureUniqueItemVisual(o,"style");F(h,u);for(var v=["symbol","symbolSize","symbolKeepAspect"],c=0;c<v.length;c++)i.setItemVisual(o,v[c],r.getItemVisual(l,v[c]))}})})}function Kr(a){return a instanceof Array||(a=[a,a]),a}function Ig(a){a.eachSeriesByType("graph",function(e){var t=e.getGraph(),r=e.getEdgeData(),i=Kr(e.get("edgeSymbol")),n=Kr(e.get("edgeSymbolSize"));r.setVisual("fromSymbol",i&&i[0]),r.setVisual("toSymbol",i&&i[1]),r.setVisual("fromSymbolSize",n&&n[0]),r.setVisual("toSymbolSize",n&&n[1]),r.setVisual("style",e.getModel("lineStyle").getLineStyle()),r.each(function(o){var s=r.getItemModel(o),l=t.getEdgeByIndex(o),u=Kr(s.getShallow("symbol",!0)),h=Kr(s.getShallow("symbolSize",!0)),v=s.getModel("lineStyle").getLineStyle(),c=r.ensureUniqueItemVisual(o,"style");switch(F(c,v),c.stroke){case"source":{var f=l.node1.getVisual("style");c.stroke=f&&f.fill;break}case"target":{var f=l.node2.getVisual("style");c.stroke=f&&f.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),h[0]&&l.setVisual("fromSymbolSize",h[0]),h[1]&&l.setVisual("toSymbolSize",h[1])})})}var pn="-->",ja=function(a){return a.get("autoCurveness")||null},Uh=function(a,e){var t=ja(a),r=20,i=[];if(he(t))r=t;else if(H(t)){a.__curvenessList=t;return}e>r&&(r=e);var n=r%2?r+2:r+3;i=[];for(var o=0;o<n;o++)i.push((o%2?o+1:o)/10*(o%2?-1:1));a.__curvenessList=i},Ir=function(a,e,t){var r=[a.id,a.dataIndex].join("."),i=[e.id,e.dataIndex].join(".");return[t.uid,r,i].join(pn)},Yh=function(a){var e=a.split(pn);return[e[0],e[2],e[1]].join(pn)},Mg=function(a,e){var t=Ir(a.node1,a.node2,e);return e.__edgeMap[t]},Pg=function(a,e){var t=dn(Ir(a.node1,a.node2,e),e),r=dn(Ir(a.node2,a.node1,e),e);return t+r},dn=function(a,e){var t=e.__edgeMap;return t[a]?t[a].length:0};function Rg(a){ja(a)&&(a.__curvenessList=[],a.__edgeMap={},Uh(a))}function Eg(a,e,t,r){if(ja(t)){var i=Ir(a,e,t),n=t.__edgeMap,o=n[Yh(i)];n[i]&&!o?n[i].isForward=!0:o&&n[i]&&(o.isForward=!0,n[i].isForward=!1),n[i]=n[i]||[],n[i].push(r)}}function eo(a,e,t,r){var i=ja(e),n=H(i);if(!i)return null;var o=Mg(a,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=Pg(a,e);Uh(e,u),a.lineStyle=a.lineStyle||{};var h=Ir(a.node1,a.node2,e),v=e.__curvenessList,c=n||u%2?0:1;if(o.isForward)return v[c+s];var f=Yh(h),p=dn(f,e),d=v[s+p+c];return r?n?i&&i[0]===0?(p+c)%2?d:-d:((p%2?0:1)+c)%2?d:-d:(p+c)%2?d:-d:v[s+p+c]}function $h(a){var e=a.coordinateSystem;if(!(e&&e.type!=="view")){var t=a.getGraph();t.eachNode(function(r){var i=r.getModel();r.setLayout([+i.get("x"),+i.get("y")])}),ro(t,a)}}function ro(a,e){a.eachEdge(function(t,r){var i=kr(t.getModel().get(["lineStyle","curveness"]),-eo(t,e,r,!0),0),n=Se(t.node1.getLayout()),o=Se(t.node2.getLayout()),s=[n,o];+i&&s.push([(n[0]+o[0])/2-(n[1]-o[1])*i,(n[1]+o[1])/2-(o[0]-n[0])*i]),t.setLayout(s)})}function Vg(a,e){a.eachSeriesByType("graph",function(t){var r=t.get("layout"),i=t.coordinateSystem;if(i&&i.type!=="view"){var n=t.getData(),o=[];D(i.dimensions,function(c){o=o.concat(n.mapDimensionsAll(c))});for(var s=0;s<n.count();s++){for(var l=[],u=!1,h=0;h<o.length;h++){var v=n.get(o[h],s);isNaN(v)||(u=!0),l.push(v)}u?n.setItemLayout(s,i.dataToPoint(l)):n.setItemLayout(s,[NaN,NaN])}ro(n.graph,t)}else(!r||r==="none")&&$h(t)})}function gr(a){var e=a.coordinateSystem;if(e.type!=="view")return 1;var t=a.option.nodeScaleRatio,r=e.scaleX,i=e.getZoom(),n=(i-1)*t+1;return n/r}function yr(a){var e=a.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Os=Math.PI,di=[];function ao(a,e,t,r){var i=a.coordinateSystem;if(!(i&&i.type!=="view")){var n=i.getBoundingRect(),o=a.getData(),s=o.graph,l=n.width/2+n.x,u=n.height/2+n.y,h=Math.min(n.width,n.height)/2,v=o.count();if(o.setLayout({cx:l,cy:u}),!!v){if(t){var c=i.pointToData(r),f=c[0],p=c[1],d=[f-l,p-u];Nr(d,d),wc(d,d,h),t.setLayout([l+d[0],u+d[1]],!0);var g=a.get(["circular","rotateLabel"]);Xh(t,g,l,u)}kg[e](a,s,o,h,l,u,v),s.eachEdge(function(y,m){var S=kr(y.getModel().get(["lineStyle","curveness"]),eo(y,a,m),0),x=Se(y.node1.getLayout()),b=Se(y.node2.getLayout()),_,w=(x[0]+b[0])/2,T=(x[1]+b[1])/2;+S&&(S*=3,_=[l*S+w*(1-S),u*S+T*(1-S)]),y.setLayout([x,b,_])})}}}var kg={value:function(a,e,t,r,i,n,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(h){var v=h.getValue("value"),c=u*(l?v:1)/2;s+=c,h.setLayout([r*Math.cos(s)+i,r*Math.sin(s)+n]),s+=c})},symbolSize:function(a,e,t,r,i,n,o){var s=0;di.length=o;var l=gr(a);e.eachNode(function(v){var c=yr(v);isNaN(c)&&(c=2),c<0&&(c=0),c*=l;var f=Math.asin(c/2/r);isNaN(f)&&(f=Os/2),di[v.dataIndex]=f,s+=f*2});var u=(2*Os-s)/o/2,h=0;e.eachNode(function(v){var c=u+di[v.dataIndex];h+=c,(!v.getLayout()||!v.getLayout().fixed)&&v.setLayout([r*Math.cos(h)+i,r*Math.sin(h)+n]),h+=c})}};function Xh(a,e,t,r){var i=a.getGraphicEl();if(i){var n=a.getModel(),o=n.get(["label","rotate"])||0,s=i.getSymbolPath();if(e){var l=a.getLayout(),u=Math.atan2(l[1]-r,l[0]-t);u<0&&(u=Math.PI*2+u);var h=l[0]<t;h&&(u=u-Math.PI);var v=h?"left":"right";s.setTextConfig({rotation:-u,position:v,origin:"center"});var c=s.ensureState("emphasis");F(c.textConfig||(c.textConfig={}),{position:v})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function Ng(a){a.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&ao(e,"symbolSize")})}var He=en;function zg(a,e,t){for(var r=a,i=e,n=t.rect,o=n.width,s=n.height,l=[n.x+o/2,n.y+s/2],u=t.gravity==null?.1:t.gravity,h=0;h<r.length;h++){var v=r[h];v.p||(v.p=Ac(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),v.pp=Se(v.p),v.edges=null}var c=t.friction==null?.6:t.friction,f=c,p,d;return{warmUp:function(){f=c*.8},setFixed:function(g){r[g].fixed=!0},setUnfixed:function(g){r[g].fixed=!1},beforeStep:function(g){p=g},afterStep:function(g){d=g},step:function(g){p&&p(r,i);for(var y=[],m=r.length,S=0;S<i.length;S++){var x=i[S];if(!x.ignoreForceLayout){var b=x.n1,_=x.n2;$e(y,_.p,b.p);var w=zo(y)-x.d,T=_.w/(b.w+_.w);isNaN(T)&&(T=0),Nr(y,y),!b.fixed&&He(b.p,b.p,y,T*w*f),!_.fixed&&He(_.p,_.p,y,-(1-T)*w*f)}}for(var S=0;S<m;S++){var L=r[S];L.fixed||($e(y,l,L.p),He(L.p,L.p,y,u*f))}for(var S=0;S<m;S++)for(var b=r[S],A=S+1;A<m;A++){var _=r[A];$e(y,_.p,b.p);var w=zo(y);w===0&&(Tc(y,Math.random()-.5,Math.random()-.5),w=1);var C=(b.rep+_.rep)/w/w;!b.fixed&&He(b.pp,b.pp,y,C),!_.fixed&&He(_.pp,_.pp,y,-C)}for(var I=[],S=0;S<m;S++){var L=r[S];L.fixed||($e(I,L.p,L.pp),He(L.p,L.p,I,f),It(L.pp,L.p))}f=f*.992;var M=f<.01;d&&d(r,i,M),g&&g(M)}}}function Og(a){a.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var r=e.preservedPoints||{},i=e.getGraph(),n=i.data,o=i.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?n.each(function(S){var x=n.getId(S);n.setItemLayout(S,r[x]||[NaN,NaN])}):!l||l==="none"?$h(e):l==="circular"&&ao(e,"value");var u=n.getDataExtent("value"),h=o.getDataExtent("value"),v=s.get("repulsion"),c=s.get("edgeLength"),f=H(v)?v:[v,v],p=H(c)?c:[c,c];p=[p[1],p[0]];var d=n.mapArray("value",function(S,x){var b=n.getItemLayout(x),_=at(S,u,f);return isNaN(_)&&(_=(f[0]+f[1])/2),{w:_,rep:_,fixed:n.getItemModel(x).get("fixed"),p:!b||isNaN(b[0])||isNaN(b[1])?null:b}}),g=o.mapArray("value",function(S,x){var b=i.getEdgeByIndex(x),_=at(S,h,p);isNaN(_)&&(_=(p[0]+p[1])/2);var w=b.getModel(),T=kr(b.getModel().get(["lineStyle","curveness"]),-eo(b,e,x,!0),0);return{n1:d[b.node1.dataIndex],n2:d[b.node2.dataIndex],d:_,curveness:T,ignoreForceLayout:w.get("ignoreForceLayout")}}),y=t.getBoundingRect(),m=zg(d,g,{rect:y,gravity:s.get("gravity"),friction:s.get("friction")});m.beforeStep(function(S,x){for(var b=0,_=S.length;b<_;b++)S[b].fixed&&It(S[b].p,i.getNodeByIndex(b).getLayout())}),m.afterStep(function(S,x,b){for(var _=0,w=S.length;_<w;_++)S[_].fixed||i.getNodeByIndex(_).setLayout(S[_].p),r[n.getId(_)]=S[_].p;for(var _=0,w=x.length;_<w;_++){var T=x[_],L=i.getEdgeByIndex(_),A=T.n1.p,C=T.n2.p,I=L.getLayout();I=I?I.slice():[],I[0]=I[0]||[],I[1]=I[1]||[],It(I[0],A),It(I[1],C),+T.curveness&&(I[2]=[(A[0]+C[0])/2-(A[1]-C[1])*T.curveness,(A[1]+C[1])/2-(C[0]-A[0])*T.curveness]),L.setLayout(I)}}),e.forceLayout=m,e.preservedPoints=r,m.step()}else e.forceLayout=null})}function Gg(a,e,t){var r=F(a.getBoxLayoutParams(),{aspect:t});return Yt(r,{width:e.getWidth(),height:e.getHeight()})}function Bg(a,e){var t=[];return a.eachSeriesByType("graph",function(r){var i=r.get("coordinateSystem");if(!i||i==="view"){var n=r.getData(),o=n.mapArray(function(g){var y=n.getItemModel(g);return[+y.get("x"),+y.get("y")]}),s=[],l=[];Uu(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),h=Gg(r,e,u);isNaN(u)&&(s=[h.x,h.y],l=[h.x+h.width,h.y+h.height]);var v=l[0]-s[0],c=l[1]-s[1],f=h.width,p=h.height,d=r.coordinateSystem=new Hr;d.zoomLimit=r.get("scaleLimit"),d.setBoundingRect(s[0],s[1],v,c),d.setViewRect(h.x,h.y,f,p),d.setCenter(r.get("center"),e),d.setZoom(r.get("zoom")),t.push(d)}}),t}var Gs=Kt.prototype,gi=$u.prototype,Kh=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return a}();(function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e})(Kh);function yi(a){return isNaN(+a.cpx1)||isNaN(+a.cpy1)}var Fg=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-line",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Kh},e.prototype.buildPath=function(t,r){yi(r)?Gs.buildPath.call(this,t,r):gi.buildPath.call(this,t,r)},e.prototype.pointAt=function(t){return yi(this.shape)?Gs.pointAt.call(this,t):gi.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var r=this.shape,i=yi(r)?[r.x2-r.x1,r.y2-r.y1]:gi.tangentAt.call(this,t);return Nr(i,i)},e}(Ut),mi=["fromSymbol","toSymbol"];function Bs(a){return"_"+a+"Type"}function Fs(a,e,t){var r=e.getItemVisual(t,a);if(!r||r==="none")return r;var i=e.getItemVisual(t,a+"Size"),n=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=Ha(i),u=zr(o||0,l);return r+l+u+(n||"")+(s||"")}function Hs(a,e,t){var r=e.getItemVisual(t,a);if(!(!r||r==="none")){var i=e.getItemVisual(t,a+"Size"),n=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=Ha(i),u=zr(o||0,l),h=Pt(r,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return h.__specifiedRotation=n==null||isNaN(n)?void 0:+n*Math.PI/180||0,h.name=a,h}}function Hg(a){var e=new Fg({name:"line",subPixelOptimize:!0});return gn(e.shape,a),e}function gn(a,e){a.x1=e[0][0],a.y1=e[0][1],a.x2=e[1][0],a.y2=e[1][1],a.percent=1;var t=e[2];t?(a.cpx1=t[0],a.cpy1=t[1]):(a.cpx1=NaN,a.cpy1=NaN)}var io=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;return n._createLine(t,r,i),n}return e.prototype._createLine=function(t,r,i){var n=t.hostModel,o=t.getItemLayout(r),s=Hg(o);s.shape.percent=0,ve(s,{shape:{percent:1}},n,r),this.add(s),D(mi,function(l){var u=Hs(l,t,r);this.add(u),this[Bs(l)]=Fs(l,t,r)},this),this._updateCommonStl(t,r,i)},e.prototype.updateData=function(t,r,i){var n=t.hostModel,o=this.childOfName("line"),s=t.getItemLayout(r),l={shape:{}};gn(l.shape,s),mt(o,l,n,r),D(mi,function(u){var h=Fs(u,t,r),v=Bs(u);if(this[v]!==h){this.remove(this.childOfName(u));var c=Hs(u,t,r);this.add(c)}this[v]=h},this),this._updateCommonStl(t,r,i)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,r,i){var n=t.hostModel,o=this.childOfName("line"),s=i&&i.emphasisLineStyle,l=i&&i.blurLineStyle,u=i&&i.selectLineStyle,h=i&&i.labelStatesModels,v=i&&i.emphasisDisabled,c=i&&i.focus,f=i&&i.blurScope;if(!i||t.hasItemOption){var p=t.getItemModel(r),d=p.getModel("emphasis");s=d.getModel("lineStyle").getLineStyle(),l=p.getModel(["blur","lineStyle"]).getLineStyle(),u=p.getModel(["select","lineStyle"]).getLineStyle(),v=d.get("disabled"),c=d.get("focus"),f=d.get("blurScope"),h=Nt(p)}var g=t.getItemVisual(r,"style"),y=g.stroke;o.useStyle(g),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=s,o.ensureState("blur").style=l,o.ensureState("select").style=u,D(mi,function(_){var w=this.childOfName(_);if(w){w.setColor(y),w.style.opacity=g.opacity;for(var T=0;T<rn.length;T++){var L=rn[T],A=o.getState(L);if(A){var C=A.style||{},I=w.ensureState(L),M=I.style||(I.style={});C.stroke!=null&&(M[w.__isEmptyBrush?"stroke":"fill"]=C.stroke),C.opacity!=null&&(M.opacity=C.opacity)}}w.markRedraw()}},this);var m=n.getRawValue(r);jt(this,h,{labelDataIndex:r,labelFetcher:{getFormattedLabel:function(_,w){return n.getFormattedLabel(_,w,t.dataType)}},inheritColor:y||"#000",defaultOpacity:g.opacity,defaultText:(m==null?t.getName(r):isFinite(m)?kn(m):m)+""});var S=this.getTextContent();if(S){var x=h.normal;S.__align=S.style.align,S.__verticalAlign=S.style.verticalAlign,S.__position=x.get("position")||"middle";var b=x.get("distance");H(b)||(b=[b,b]),S.__labelDistance=b}this.setTextConfig({position:null,local:!0,inside:!1}),St(this,c,f,v)},e.prototype.highlight=function(){Nn(this)},e.prototype.downplay=function(){zn(this)},e.prototype.updateLayout=function(t,r){this.setLinePoints(t.getItemLayout(r))},e.prototype.setLinePoints=function(t){var r=this.childOfName("line");gn(r.shape,t),r.dirty()},e.prototype.beforeUpdate=function(){var t=this,r=t.childOfName("fromSymbol"),i=t.childOfName("toSymbol"),n=t.getTextContent();if(!r&&!i&&(!n||n.ignore))return;for(var o=1,s=this.parent;s;)s.scaleX&&(o/=s.scaleX),s=s.parent;var l=t.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,h=l.pointAt(0),v=l.pointAt(u),c=$e([],v,h);Nr(c,c);function f(A,C){var I=A.__specifiedRotation;if(I==null){var M=l.tangentAt(C);A.attr("rotation",(C===1?-1:1)*Math.PI/2-Math.atan2(M[1],M[0]))}else A.attr("rotation",I)}if(r&&(r.setPosition(h),f(r,0),r.scaleX=r.scaleY=o*u,r.markRedraw()),i&&(i.setPosition(v),f(i,1),i.scaleX=i.scaleY=o*u,i.markRedraw()),n&&!n.ignore){n.x=n.y=0,n.originX=n.originY=0;var p=void 0,d=void 0,g=n.__labelDistance,y=g[0]*o,m=g[1]*o,S=u/2,x=l.tangentAt(S),b=[x[1],-x[0]],_=l.pointAt(S);b[1]>0&&(b[0]=-b[0],b[1]=-b[1]);var w=x[0]<0?-1:1;if(n.__position!=="start"&&n.__position!=="end"){var T=-Math.atan2(x[1],x[0]);v[0]<h[0]&&(T=Math.PI+T),n.rotation=T}var L=void 0;switch(n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":L=-m,d="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":L=m,d="top";break;default:L=0,d="middle"}switch(n.__position){case"end":n.x=c[0]*y+v[0],n.y=c[1]*m+v[1],p=c[0]>.8?"left":c[0]<-.8?"right":"center",d=c[1]>.8?"top":c[1]<-.8?"bottom":"middle";break;case"start":n.x=-c[0]*y+h[0],n.y=-c[1]*m+h[1],p=c[0]>.8?"right":c[0]<-.8?"left":"center",d=c[1]>.8?"bottom":c[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":n.x=y*w+h[0],n.y=h[1]+L,p=x[0]<0?"right":"left",n.originX=-y*w,n.originY=-L;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":n.x=_[0],n.y=_[1]+L,p="center",n.originY=-L;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":n.x=-y*w+v[0],n.y=v[1]+L,p=x[0]>=0?"right":"left",n.originX=y*w,n.originY=-L;break}n.scaleX=n.scaleY=o,n.setStyle({verticalAlign:n.__verticalAlign||d,align:n.__align||p})}},e}($),no=function(){function a(e){this.group=new $,this._LineCtor=e||io}return a.prototype.updateData=function(e){var t=this;this._progressiveEls=null;var r=this,i=r.group,n=r._lineData;r._lineData=e,n||i.removeAll();var o=Ws(e);e.diff(n).add(function(s){t._doAdd(e,s,o)}).update(function(s,l){t._doUpdate(n,e,l,s,o)}).remove(function(s){i.remove(n.getItemGraphicEl(s))}).execute()},a.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(t,r){t.updateLayout(e,r)},this)},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=Ws(e),this._lineData=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t){this._progressiveEls=[];function r(s){!s.isGroup&&!Wg(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var i=e.start;i<e.end;i++){var n=t.getItemLayout(i);if(Si(n)){var o=new this._LineCtor(t,i,this._seriesScope);o.traverse(r),this.group.add(o),t.setItemGraphicEl(i,o),this._progressiveEls.push(o)}}},a.prototype.remove=function(){this.group.removeAll()},a.prototype.eachRendered=function(e){Wa(this._progressiveEls||this.group,e)},a.prototype._doAdd=function(e,t,r){var i=e.getItemLayout(t);if(Si(i)){var n=new this._LineCtor(e,t,r);e.setItemGraphicEl(t,n),this.group.add(n)}},a.prototype._doUpdate=function(e,t,r,i,n){var o=e.getItemGraphicEl(r);if(!Si(t.getItemLayout(i))){this.group.remove(o);return}o?o.updateData(t,i,n):o=new this._LineCtor(t,i,n),t.setItemGraphicEl(i,o),this.group.add(o)},a}();function Wg(a){return a.animators&&a.animators.length>0}function Ws(a){var e=a.hostModel,t=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:t.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:t.get("disabled"),blurScope:t.get("blurScope"),focus:t.get("focus"),labelStatesModels:Nt(e)}}function Zs(a){return isNaN(a[0])||isNaN(a[1])}function Si(a){return a&&!Zs(a[0])&&!Zs(a[1])}var xi=[],bi=[],_i=[],We=qu,wi=Lc,Us=Math.abs;function Ys(a,e,t){for(var r=a[0],i=a[1],n=a[2],o=1/0,s,l=t*t,u=.1,h=.1;h<=.9;h+=.1){xi[0]=We(r[0],i[0],n[0],h),xi[1]=We(r[1],i[1],n[1],h);var v=Us(wi(xi,e)-l);v<o&&(o=v,s=h)}for(var c=0;c<32;c++){var f=s+u;bi[0]=We(r[0],i[0],n[0],s),bi[1]=We(r[1],i[1],n[1],s),_i[0]=We(r[0],i[0],n[0],f),_i[1]=We(r[1],i[1],n[1],f);var v=wi(bi,e)-l;if(Us(v)<.01)break;var p=wi(_i,e)-l;u/=2,v<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function Ai(a,e){var t=[],r=Dc,i=[[],[],[]],n=[[],[]],o=[];e/=2,a.eachEdge(function(s,l){var u=s.getLayout(),h=s.getVisual("fromSymbol"),v=s.getVisual("toSymbol");u.__original||(u.__original=[Se(u[0]),Se(u[1])],u[2]&&u.__original.push(Se(u[2])));var c=u.__original;if(u[2]!=null){if(It(i[0],c[0]),It(i[1],c[2]),It(i[2],c[1]),h&&h!=="none"){var f=yr(s.node1),p=Ys(i,c[0],f*e);r(i[0][0],i[1][0],i[2][0],p,t),i[0][0]=t[3],i[1][0]=t[4],r(i[0][1],i[1][1],i[2][1],p,t),i[0][1]=t[3],i[1][1]=t[4]}if(v&&v!=="none"){var f=yr(s.node2),p=Ys(i,c[1],f*e);r(i[0][0],i[1][0],i[2][0],p,t),i[1][0]=t[1],i[2][0]=t[2],r(i[0][1],i[1][1],i[2][1],p,t),i[1][1]=t[1],i[2][1]=t[2]}It(u[0],i[0]),It(u[1],i[2]),It(u[2],i[1])}else{if(It(n[0],c[0]),It(n[1],c[1]),$e(o,n[1],n[0]),Nr(o,o),h&&h!=="none"){var f=yr(s.node1);en(n[0],n[0],o,f*e)}if(v&&v!=="none"){var f=yr(s.node2);en(n[1],n[1],o,-f*e)}It(u[0],n[0]),It(u[1],n[1])}})}function $s(a){return a.type==="view"}var Zg=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){var i=new Na,n=new no,o=this.group;this._controller=new Fr(r.getZr()),this._controllerHost={target:o},o.add(i.group),o.add(n.group),this._symbolDraw=i,this._lineDraw=n,this._firstRender=!0},e.prototype.render=function(t,r,i){var n=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if($s(o)){var h={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(h):mt(u,h,t)}Ai(t.getGraph(),gr(t));var v=t.getData();s.updateData(v);var c=t.getEdgeData();l.updateData(c),this._updateNodeAndLinkScale(),this._updateController(t,r,i),clearTimeout(this._layoutTimeout);var f=t.forceLayout,p=t.get(["force","layoutAnimation"]);f&&this._startForceLayoutIteration(f,p);var d=t.get("layout");v.graph.eachNode(function(S){var x=S.dataIndex,b=S.getGraphicEl(),_=S.getModel();if(b){b.off("drag").off("dragend");var w=_.get("draggable");w&&b.on("drag",function(L){switch(d){case"force":f.warmUp(),!n._layouting&&n._startForceLayoutIteration(f,p),f.setFixed(x),v.setItemLayout(x,[b.x,b.y]);break;case"circular":v.setItemLayout(x,[b.x,b.y]),S.setLayout({fixed:!0},!0),ao(t,"symbolSize",S,[L.offsetX,L.offsetY]),n.updateLayout(t);break;case"none":default:v.setItemLayout(x,[b.x,b.y]),ro(t.getGraph(),t),n.updateLayout(t);break}}).on("dragend",function(){f&&f.setUnfixed(x)}),b.setDraggable(w,!!_.get("cursor"));var T=_.get(["emphasis","focus"]);T==="adjacency"&&(nt(b).focus=S.getAdjacentDataIndices())}}),v.graph.eachEdge(function(S){var x=S.getGraphicEl(),b=S.getModel().get(["emphasis","focus"]);x&&b==="adjacency"&&(nt(x).focus={edge:[S.dataIndex],node:[S.node1.dataIndex,S.node2.dataIndex]})});var g=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),y=v.getLayout("cx"),m=v.getLayout("cy");v.graph.eachNode(function(S){Xh(S,g,y,m)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,r){var i=this;(function n(){t.step(function(o){i.updateLayout(i._model),(i._layouting=!o)&&(r?i._layoutTimeout=setTimeout(n,16):n())})})()},e.prototype._updateController=function(t,r,i){var n=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,h,v){var c=l.getBoundingRect();return c.applyTransform(l.transform),c.contain(h,v)&&!Mn(u,i,t)}),!$s(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){$n(s,u.dx,u.dy),i.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){Xn(s,u.scale,u.originX,u.originY),i.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),n._updateNodeAndLinkScale(),Ai(t.getGraph(),gr(t)),n._lineDraw.updateLayout(),i.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,r=t.getData(),i=gr(t);r.eachItemGraphicEl(function(n,o){n&&n.setSymbolScale(i)})},e.prototype.updateLayout=function(t){Ai(t.getGraph(),gr(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(_t);function Ze(a){return"_EC_"+a}var Ug=function(){function a(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return a.prototype.isDirected=function(){return this._directed},a.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var r=this._nodesMap;if(!r[Ze(e)]){var i=new Pe(e,t);return i.hostGraph=this,this.nodes.push(i),r[Ze(e)]=i,i}},a.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},a.prototype.getNodeById=function(e){return this._nodesMap[Ze(e)]},a.prototype.addEdge=function(e,t,r){var i=this._nodesMap,n=this._edgesMap;if(he(e)&&(e=this.nodes[e]),he(t)&&(t=this.nodes[t]),e instanceof Pe||(e=i[Ze(e)]),t instanceof Pe||(t=i[Ze(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new qh(e,t,r);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),n[o]=s,s}},a.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},a.prototype.getEdge=function(e,t){e instanceof Pe&&(e=e.id),t instanceof Pe&&(t=t.id);var r=this._edgesMap;return this._directed?r[e+"-"+t]:r[e+"-"+t]||r[t+"-"+e]},a.prototype.eachNode=function(e,t){for(var r=this.nodes,i=r.length,n=0;n<i;n++)r[n].dataIndex>=0&&e.call(t,r[n],n)},a.prototype.eachEdge=function(e,t){for(var r=this.edges,i=r.length,n=0;n<i;n++)r[n].dataIndex>=0&&r[n].node1.dataIndex>=0&&r[n].node2.dataIndex>=0&&e.call(t,r[n],n)},a.prototype.breadthFirstTraverse=function(e,t,r,i){if(t instanceof Pe||(t=this._nodesMap[Ze(t)]),!!t){for(var n=r==="out"?"outEdges":r==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(i,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[n],o=0;o<u.length;o++){var h=u[o],v=h.node1===l?h.node2:h.node1;if(!v.__visited){if(e.call(i,v,l))return;s.push(v),v.__visited=!0}}}},a.prototype.update=function(){for(var e=this.data,t=this.edgeData,r=this.nodes,i=this.edges,n=0,o=r.length;n<o;n++)r[n].dataIndex=-1;for(var n=0,o=e.count();n<o;n++)r[e.getRawIndex(n)].dataIndex=n;t.filterSelf(function(s){var l=i[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var n=0,o=i.length;n<o;n++)i[n].dataIndex=-1;for(var n=0,o=t.count();n<o;n++)i[t.getRawIndex(n)].dataIndex=n},a.prototype.clone=function(){for(var e=new a(this._directed),t=this.nodes,r=this.edges,i=0;i<t.length;i++)e.addNode(t[i].id,t[i].dataIndex);for(var i=0;i<r.length;i++){var n=r[i];e.addEdge(n.node1.id,n.node2.id,n.dataIndex)}return e},a}(),Pe=function(){function a(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e??"",this.dataIndex=t??-1}return a.prototype.degree=function(){return this.edges.length},a.prototype.inDegree=function(){return this.inEdges.length},a.prototype.outDegree=function(){return this.outEdges.length},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var r=this.edges[t];r.dataIndex<0||(e.edge.push(r.dataIndex),e.node.push(r.node1.dataIndex,r.node2.dataIndex))}return e},a.prototype.getTrajectoryDataIndices=function(){for(var e=rt(),t=rt(),r=0;r<this.edges.length;r++){var i=this.edges[r];if(!(i.dataIndex<0)){e.set(i.dataIndex,!0);for(var n=[i.node1],o=[i.node2],s=0;s<n.length;){var l=n[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),n.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var h=o[s];s++,t.set(h.dataIndex,!0);for(var u=0;u<h.outEdges.length;u++)e.set(h.outEdges[u].dataIndex,!0),o.push(h.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},a}(),qh=function(){function a(e,t,r){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=r??-1}return a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.edgeData.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},a.prototype.getTrajectoryDataIndices=function(){var e=rt(),t=rt();e.set(this.dataIndex,!0);for(var r=[this.node1],i=[this.node2],n=0;n<r.length;){var o=r[n];n++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),r.push(o.inEdges[s].node1)}for(n=0;n<i.length;){var l=i[n];n++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),i.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},a}();function jh(a,e){return{getValue:function(t){var r=this[a][e];return r.getStore().get(r.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,r){this.dataIndex>=0&&this[a][e].setItemVisual(this.dataIndex,t,r)},getVisual:function(t){return this[a][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,r){this.dataIndex>=0&&this[a][e].setItemLayout(this.dataIndex,t,r)},getLayout:function(){return this[a][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[a][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[a][e].getRawIndex(this.dataIndex)}}}ie(Pe,jh("hostGraph","data"));ie(qh,jh("hostGraph","edgeData"));function Jh(a,e,t,r,i){for(var n=new Ug(r),o=0;o<a.length;o++)n.addNode(Vt(a[o].id,a[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var h=e[o],v=h.source,c=h.target;n.addEdge(v,c,u)&&(l.push(h),s.push(Vt(oe(h.id,null),v+" > "+c)),u++)}var f=t.get("coordinateSystem"),p;if(f==="cartesian2d"||f==="polar")p=Qe(a,t);else{var d=ju.get(f),g=d?d.dimensions||[]:[];Mt(g,"value")<0&&g.concat(["value"]);var y=En(a,{coordDimensions:g,encodeDefine:t.getEncode()}).dimensions;p=new kt(y,t),p.initData(a)}var m=new kt(["value"],t);return m.initData(l,s),i&&i(p,m),Eh({mainData:p,struct:n,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),n.update(),n}var Yg=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments);var r=this;function i(){return r._categoriesData}this.legendVisualProvider=new On(i,i),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){a.prototype.mergeDefaultAndTheme.apply(this,arguments),Ba(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,r){var i=t.edges||t.links||[],n=t.data||t.nodes||[],o=this;if(n&&i){Rg(this);var s=Jh(n,i,this,!0,l);return D(s.edges,function(u){Eg(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,h){u.wrapMethod("getItemModel",function(p){var d=o._categoriesModels,g=p.getShallow("category"),y=d[g];return y&&(y.parentModel=p.parentModel,p.parentModel=y),p});var v=Jt.prototype.getModel;function c(p,d){var g=v.call(this,p,d);return g.resolveParentPath=f,g}h.wrapMethod("getItemModel",function(p){return p.resolveParentPath=f,p.getModel=c,p});function f(p){if(p&&(p[0]==="label"||p[1]==="label")){var d=p.slice();return p[0]==="label"?d[0]="edgeLabel":p[1]==="label"&&(d[1]="edgeLabel"),d}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,r,i){if(i==="edge"){var n=this.getData(),o=this.getDataParams(t,i),s=n.graph.getEdgeByIndex(t),l=n.getName(s.node1.dataIndex),u=n.getName(s.node2.dataIndex),h=[];return l!=null&&h.push(l),u!=null&&h.push(u),Zt("nameValue",{name:h.join(" > "),value:o.value,noValue:o.value==null})}var v=Cc({series:this,dataIndex:t,multipleSeries:r});return v},e.prototype._updateCategoriesData=function(){var t=W(this.option.categories||[],function(i){return i.value!=null?i:F({value:0},i)}),r=new kt(["value"],this);r.initData(t),this._categoriesData=r,this._categoriesModels=r.mapArray(function(i){return r.getItemModel(i)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return a.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(wt),$g={type:"graphRoam",event:"graphRoam",update:"none"};function Xg(a){a.registerChartView(Zg),a.registerSeriesModel(Yg),a.registerProcessor(Lg),a.registerVisual(Cg),a.registerVisual(Ig),a.registerLayout(Vg),a.registerLayout(a.PRIORITY.VISUAL.POST_CHART_LAYOUT,Ng),a.registerLayout(Og),a.registerCoordinateSystem("graphView",{dimensions:Hr.dimensions,create:Bg}),a.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},Ge),a.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},Ge),a.registerAction($g,function(e,t,r){t.eachComponent({mainType:"series",query:e},function(i){var n=i.coordinateSystem,o=qn(n,e,void 0,r);i.setCenter&&i.setCenter(o.center),i.setZoom&&i.setZoom(o.zoom)})})}var Kg=function(){function a(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return a}(),qg=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="pointer",r}return e.prototype.getDefaultShape=function(){return new Kg},e.prototype.buildPath=function(t,r){var i=Math.cos,n=Math.sin,o=r.r,s=r.width,l=r.angle,u=r.x-i(l)*s*(s>=o/3?1:2),h=r.y-n(l)*s*(s>=o/3?1:2);l=r.angle-Math.PI/2,t.moveTo(u,h),t.lineTo(r.x+i(l)*s,r.y+n(l)*s),t.lineTo(r.x+i(r.angle)*o,r.y+n(r.angle)*o),t.lineTo(r.x-i(l)*s,r.y-n(l)*s),t.lineTo(u,h)},e}(Ut);function jg(a,e){var t=a.get("center"),r=e.getWidth(),i=e.getHeight(),n=Math.min(r,i),o=O(t[0],e.getWidth()),s=O(t[1],e.getHeight()),l=O(a.get("radius"),n/2);return{cx:o,cy:s,r:l}}function qr(a,e){var t=a==null?"":a+"";return e&&(Q(e)?t=e.replace("{value}",t):st(e)&&(t=e(a))),t}var Jg=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this.group.removeAll();var n=t.get(["axisLine","lineStyle","color"]),o=jg(t,i);this._renderMain(t,r,i,n,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,r,i,n,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,h=-t.get("endAngle")/180*Math.PI,v=t.getModel("axisLine"),c=v.get("roundCap"),f=c?Oo:wr,p=v.get("show"),d=v.getModel("lineStyle"),g=d.get("width"),y=[u,h];Ic(y,!l),u=y[0],h=y[1];for(var m=h-u,S=u,x=[],b=0;p&&b<n.length;b++){var _=Math.min(Math.max(n[b][0],0),1);h=u+m*_;var w=new f({shape:{startAngle:S,endAngle:h,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-g,r:o.r},silent:!0});w.setStyle({fill:n[b][1]}),w.setStyle(d.getLineStyle(["color","width"])),x.push(w),S=h}x.reverse(),D(x,function(L){return s.add(L)});var T=function(L){if(L<=0)return n[0][1];var A;for(A=0;A<n.length;A++)if(n[A][0]>=L&&(A===0?0:n[A-1][0])<L)return n[A][1];return n[A-1][1]};this._renderTicks(t,r,i,T,o,u,h,l,g),this._renderTitleAndDetail(t,r,i,T,o),this._renderAnchor(t,o),this._renderPointer(t,r,i,T,o,u,h,l,g)},e.prototype._renderTicks=function(t,r,i,n,o,s,l,u,h){for(var v=this.group,c=o.cx,f=o.cy,p=o.r,d=+t.get("min"),g=+t.get("max"),y=t.getModel("splitLine"),m=t.getModel("axisTick"),S=t.getModel("axisLabel"),x=t.get("splitNumber"),b=m.get("splitNumber"),_=O(y.get("length"),p),w=O(m.get("length"),p),T=s,L=(l-s)/x,A=L/b,C=y.getModel("lineStyle").getLineStyle(),I=m.getModel("lineStyle").getLineStyle(),M=y.get("distance"),P,R,V=0;V<=x;V++){if(P=Math.cos(T),R=Math.sin(T),y.get("show")){var k=M?M+h:h,N=new Kt({shape:{x1:P*(p-k)+c,y1:R*(p-k)+f,x2:P*(p-_-k)+c,y2:R*(p-_-k)+f},style:C,silent:!0});C.stroke==="auto"&&N.setStyle({stroke:n(V/x)}),v.add(N)}if(S.get("show")){var k=S.get("distance")+M,G=qr(kn(V/x*(g-d)+d),S.get("formatter")),Z=n(V/x),U=P*(p-_-k)+c,K=R*(p-_-k)+f,J=S.get("rotate"),q=0;J==="radial"?(q=-T+2*Math.PI,q>Math.PI/2&&(q+=Math.PI)):J==="tangential"?q=-T-Math.PI/2:he(J)&&(q=J*Math.PI/180),q===0?v.add(new ut({style:ct(S,{text:G,x:U,y:K,verticalAlign:R<-.8?"top":R>.8?"bottom":"middle",align:P<-.4?"left":P>.4?"right":"center"},{inheritColor:Z}),silent:!0})):v.add(new ut({style:ct(S,{text:G,x:U,y:K,verticalAlign:"middle",align:"center"},{inheritColor:Z}),silent:!0,originX:U,originY:K,rotation:q}))}if(m.get("show")&&V!==x){var k=m.get("distance");k=k?k+h:h;for(var tt=0;tt<=b;tt++){P=Math.cos(T),R=Math.sin(T);var Lt=new Kt({shape:{x1:P*(p-k)+c,y1:R*(p-k)+f,x2:P*(p-w-k)+c,y2:R*(p-w-k)+f},silent:!0,style:I});I.stroke==="auto"&&Lt.setStyle({stroke:n((V+tt/b)/x)}),v.add(Lt),T+=A}T-=A}else T+=L}},e.prototype._renderPointer=function(t,r,i,n,o,s,l,u,h){var v=this.group,c=this._data,f=this._progressEls,p=[],d=t.get(["pointer","show"]),g=t.getModel("progress"),y=g.get("show"),m=t.getData(),S=m.mapDimension("value"),x=+t.get("min"),b=+t.get("max"),_=[x,b],w=[s,l];function T(A,C){var I=m.getItemModel(A),M=I.getModel("pointer"),P=O(M.get("width"),o.r),R=O(M.get("length"),o.r),V=t.get(["pointer","icon"]),k=M.get("offsetCenter"),N=O(k[0],o.r),G=O(k[1],o.r),Z=M.get("keepAspect"),U;return V?U=Pt(V,N-P/2,G-R,P,R,null,Z):U=new qg({shape:{angle:-Math.PI/2,width:P,r:R,x:N,y:G}}),U.rotation=-(C+Math.PI/2),U.x=o.cx,U.y=o.cy,U}function L(A,C){var I=g.get("roundCap"),M=I?Oo:wr,P=g.get("overlap"),R=P?g.get("width"):h/m.count(),V=P?o.r-R:o.r-(A+1)*R,k=P?o.r:o.r-A*R,N=new M({shape:{startAngle:s,endAngle:C,cx:o.cx,cy:o.cy,clockwise:u,r0:V,r:k}});return P&&(N.z2=at(m.get(S,A),[x,b],[100,0],!0)),N}(y||d)&&(m.diff(c).add(function(A){var C=m.get(S,A);if(d){var I=T(A,s);ve(I,{rotation:-((isNaN(+C)?w[0]:at(C,_,w,!0))+Math.PI/2)},t),v.add(I),m.setItemGraphicEl(A,I)}if(y){var M=L(A,s),P=g.get("clip");ve(M,{shape:{endAngle:at(C,_,w,P)}},t),v.add(M),Go(t.seriesIndex,m.dataType,A,M),p[A]=M}}).update(function(A,C){var I=m.get(S,A);if(d){var M=c.getItemGraphicEl(C),P=M?M.rotation:s,R=T(A,P);R.rotation=P,mt(R,{rotation:-((isNaN(+I)?w[0]:at(I,_,w,!0))+Math.PI/2)},t),v.add(R),m.setItemGraphicEl(A,R)}if(y){var V=f[C],k=V?V.shape.endAngle:s,N=L(A,k),G=g.get("clip");mt(N,{shape:{endAngle:at(I,_,w,G)}},t),v.add(N),Go(t.seriesIndex,m.dataType,A,N),p[A]=N}}).execute(),m.each(function(A){var C=m.getItemModel(A),I=C.getModel("emphasis"),M=I.get("focus"),P=I.get("blurScope"),R=I.get("disabled");if(d){var V=m.getItemGraphicEl(A),k=m.getItemVisual(A,"style"),N=k.fill;if(V instanceof pe){var G=V.style;V.useStyle(F({image:G.image,x:G.x,y:G.y,width:G.width,height:G.height},k))}else V.useStyle(k),V.type!=="pointer"&&V.setColor(N);V.setStyle(C.getModel(["pointer","itemStyle"]).getItemStyle()),V.style.fill==="auto"&&V.setStyle("fill",n(at(m.get(S,A),_,[0,1],!0))),V.z2EmphasisLift=0,Qt(V,C),St(V,M,P,R)}if(y){var Z=p[A];Z.useStyle(m.getItemVisual(A,"style")),Z.setStyle(C.getModel(["progress","itemStyle"]).getItemStyle()),Z.z2EmphasisLift=0,Qt(Z,C),St(Z,M,P,R)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,r){var i=t.getModel("anchor"),n=i.get("show");if(n){var o=i.get("size"),s=i.get("icon"),l=i.get("offsetCenter"),u=i.get("keepAspect"),h=Pt(s,r.cx-o/2+O(l[0],r.r),r.cy-o/2+O(l[1],r.r),o,o,null,u);h.z2=i.get("showAbove")?1:0,h.setStyle(i.getModel("itemStyle").getItemStyle()),this.group.add(h)}},e.prototype._renderTitleAndDetail=function(t,r,i,n,o){var s=this,l=t.getData(),u=l.mapDimension("value"),h=+t.get("min"),v=+t.get("max"),c=new $,f=[],p=[],d=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(y){f[y]=new ut({silent:!0}),p[y]=new ut({silent:!0})}).update(function(y,m){f[y]=s._titleEls[m],p[y]=s._detailEls[m]}).execute(),l.each(function(y){var m=l.getItemModel(y),S=l.get(u,y),x=new $,b=n(at(S,[h,v],[0,1],!0)),_=m.getModel("title");if(_.get("show")){var w=_.get("offsetCenter"),T=o.cx+O(w[0],o.r),L=o.cy+O(w[1],o.r),A=f[y];A.attr({z2:g?0:2,style:ct(_,{x:T,y:L,text:l.getName(y),align:"center",verticalAlign:"middle"},{inheritColor:b})}),x.add(A)}var C=m.getModel("detail");if(C.get("show")){var I=C.get("offsetCenter"),M=o.cx+O(I[0],o.r),P=o.cy+O(I[1],o.r),R=O(C.get("width"),o.r),V=O(C.get("height"),o.r),k=t.get(["progress","show"])?l.getItemVisual(y,"style").fill:b,A=p[y],N=C.get("formatter");A.attr({z2:g?0:2,style:ct(C,{x:M,y:P,text:qr(S,N),width:isNaN(R)?null:R,height:isNaN(V)?null:V,align:"center",verticalAlign:"middle"},{inheritColor:k})}),Mc(A,{normal:C},S,function(Z){return qr(Z,N)}),d&&Pc(A,y,l,t,{getFormattedLabel:function(Z,U,K,J,q,tt){return qr(tt?tt.interpolatedValue:S,N)}}),x.add(A)}c.add(x)}),this.group.add(c),this._titleEls=f,this._detailEls=p},e.type="gauge",e}(_t),Qg=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,r){return Ga(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(wt);function ty(a){a.registerChartView(Jg),a.registerSeriesModel(Qg)}var ey=["itemStyle","opacity"],ry=function(a){E(e,a);function e(t,r){var i=a.call(this)||this,n=i,o=new Be,s=new ut;return n.setTextContent(s),i.setTextGuideLine(o),i.updateData(t,r,!0),i}return e.prototype.updateData=function(t,r,i){var n=this,o=t.hostModel,s=t.getItemModel(r),l=t.getItemLayout(r),u=s.getModel("emphasis"),h=s.get(ey);h=h??1,i||rr(n),n.useStyle(t.getItemVisual(r,"style")),n.style.lineJoin="round",i?(n.setShape({points:l.points}),n.style.opacity=0,ve(n,{style:{opacity:h}},o,r)):mt(n,{style:{opacity:h},shape:{points:l.points}},o,r),Qt(n,s),this._updateLabel(t,r),St(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r){var i=this,n=this.getTextGuideLine(),o=i.getTextContent(),s=t.hostModel,l=t.getItemModel(r),u=t.getItemLayout(r),h=u.label,v=t.getItemVisual(r,"style"),c=v.fill;jt(o,Nt(l),{labelFetcher:t.hostModel,labelDataIndex:r,defaultOpacity:v.opacity,defaultText:t.getName(r)},{normal:{align:h.textAlign,verticalAlign:h.verticalAlign}}),i.setTextConfig({local:!0,inside:!!h.inside,insideStroke:c,outsideFill:c});var f=h.linePoints;n.setShape({points:f}),i.textGuideLineConfig={anchor:f?new Ju(f[0][0],f[0][1]):null},mt(o,{style:{x:h.x,y:h.y}},s,r),o.attr({rotation:h.rotation,originX:h.x,originY:h.y,z2:10}),Ec(i,Vc(l),{stroke:c})},e}(_e),ay=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._data,s=this.group;n.diff(o).add(function(l){var u=new ry(n,l);n.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var h=o.getItemGraphicEl(u);h.updateData(n,l),s.add(h),n.setItemGraphicEl(l,h)}).remove(function(l){var u=o.getItemGraphicEl(l);Rc(u,t,l)}).execute(),this._data=n},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(_t),iy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new On(B(this.getData,this),B(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,r){return Ga(this,{coordDimensions:["value"],encodeDefaulter:it(Hu,this)})},e.prototype._defaultLabelLine=function(t){Ba(t,"labelLine",["show"]);var r=t.labelLine,i=t.emphasis.labelLine;r.show=r.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var r=this.getData(),i=a.prototype.getDataParams.call(this,t),n=r.mapDimension("value"),o=r.getSum(n);return i.percent=o?+(r.get(n,t)/o*100).toFixed(2):0,i.$vars.push("percent"),i},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(wt);function ny(a,e){return Yt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function oy(a,e){for(var t=a.mapDimension("value"),r=a.mapArray(t,function(l){return l}),i=[],n=e==="ascending",o=0,s=a.count();o<s;o++)i[o]=o;return st(e)?i.sort(e):e!=="none"&&i.sort(function(l,u){return n?r[l]-r[u]:r[u]-r[l]}),i}function sy(a){var e=a.hostModel,t=e.get("orient");a.each(function(r){var i=a.getItemModel(r),n=i.getModel("label"),o=n.get("position"),s=i.getModel("labelLine"),l=a.getItemLayout(r),u=l.points,h=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",v,c,f,p;if(h)o==="insideLeft"?(c=(u[0][0]+u[3][0])/2+5,f=(u[0][1]+u[3][1])/2,v="left"):o==="insideRight"?(c=(u[1][0]+u[2][0])/2-5,f=(u[1][1]+u[2][1])/2,v="right"):(c=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,f=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,v="center"),p=[[c,f],[c,f]];else{var d=void 0,g=void 0,y=void 0,m=void 0,S=s.get("length");o==="left"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,y=d-S,c=y-5,v="right"):o==="right"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,y=d+S,c=y+5,v="left"):o==="top"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,m=g-S,f=m-5,v="center"):o==="bottom"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,m=g+S,f=m+5,v="center"):o==="rightTop"?(d=t==="horizontal"?u[3][0]:u[1][0],g=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(m=g-S,f=m-5,v="center"):(y=d+S,c=y+5,v="top")):o==="rightBottom"?(d=u[2][0],g=u[2][1],t==="horizontal"?(m=g+S,f=m+5,v="center"):(y=d+S,c=y+5,v="bottom")):o==="leftTop"?(d=u[0][0],g=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(m=g-S,f=m-5,v="center"):(y=d-S,c=y-5,v="right")):o==="leftBottom"?(d=t==="horizontal"?u[1][0]:u[3][0],g=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(m=g+S,f=m+5,v="center"):(y=d-S,c=y-5,v="right")):(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,t==="horizontal"?(m=g+S,f=m+5,v="center"):(y=d+S,c=y+5,v="left")),t==="horizontal"?(y=d,c=y):(m=g,f=m),p=[[d,g],[y,m]]}l.label={linePoints:p,x:c,y:f,verticalAlign:"middle",textAlign:v,inside:h}})}function ly(a,e){a.eachSeriesByType("funnel",function(t){var r=t.getData(),i=r.mapDimension("value"),n=t.get("sort"),o=ny(t,e),s=t.get("orient"),l=o.width,u=o.height,h=oy(r,n),v=o.x,c=o.y,f=s==="horizontal"?[O(t.get("minSize"),u),O(t.get("maxSize"),u)]:[O(t.get("minSize"),l),O(t.get("maxSize"),l)],p=r.getDataExtent(i),d=t.get("min"),g=t.get("max");d==null&&(d=Math.min(p[0],0)),g==null&&(g=p[1]);var y=t.get("funnelAlign"),m=t.get("gap"),S=s==="horizontal"?l:u,x=(S-m*(r.count()-1))/r.count(),b=function(P,R){if(s==="horizontal"){var V=r.get(i,P)||0,k=at(V,[d,g],f,!0),N=void 0;switch(y){case"top":N=c;break;case"center":N=c+(u-k)/2;break;case"bottom":N=c+(u-k);break}return[[R,N],[R,N+k]]}var G=r.get(i,P)||0,Z=at(G,[d,g],f,!0),U;switch(y){case"left":U=v;break;case"center":U=v+(l-Z)/2;break;case"right":U=v+l-Z;break}return[[U,R],[U+Z,R]]};n==="ascending"&&(x=-x,m=-m,s==="horizontal"?v+=l:c+=u,h=h.reverse());for(var _=0;_<h.length;_++){var w=h[_],T=h[_+1],L=r.getItemModel(w);if(s==="horizontal"){var A=L.get(["itemStyle","width"]);A==null?A=x:(A=O(A,l),n==="ascending"&&(A=-A));var C=b(w,v),I=b(T,v+A);v+=A+m,r.setItemLayout(w,{points:C.concat(I.slice().reverse())})}else{var M=L.get(["itemStyle","height"]);M==null?M=x:(M=O(M,u),n==="ascending"&&(M=-M));var C=b(w,c),I=b(T,c+M);c+=M+m,r.setItemLayout(w,{points:C.concat(I.slice().reverse())})}}sy(r)})}function uy(a){a.registerChartView(ay),a.registerSeriesModel(iy),a.registerLayout(ly),a.registerProcessor(Gn("funnel"))}var hy=.3,vy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new $,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,r,i,n){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,h=u.dimensions,v=Ks(t);s.diff(l).add(c).update(f).remove(p).execute();function c(g){var y=Xs(s,o,g,h,u);Ti(y,s,g,v)}function f(g,y){var m=l.getItemGraphicEl(y),S=Qh(s,g,h,u);s.setItemGraphicEl(g,m),mt(m,{shape:{points:S}},t,g),rr(m),Ti(m,s,g,v)}function p(g){var y=l.getItemGraphicEl(g);o.remove(y)}if(!this._initialized){this._initialized=!0;var d=cy(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(d)}this._data=s},e.prototype.incrementalPrepareRender=function(t,r,i){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,r,i){for(var n=r.getData(),o=r.coordinateSystem,s=o.dimensions,l=Ks(r),u=this._progressiveEls=[],h=t.start;h<t.end;h++){var v=Xs(n,this._dataGroup,h,s,o);v.incremental=!0,Ti(v,n,h,l),u.push(v)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(_t);function cy(a,e,t){var r=a.model,i=a.getRect(),n=new gt({shape:{x:i.x,y:i.y,width:i.width,height:i.height}}),o=r.get("layout")==="horizontal"?"width":"height";return n.setShape(o,0),ve(n,{shape:{width:i.width,height:i.height}},e,t),n}function Qh(a,e,t,r){for(var i=[],n=0;n<t.length;n++){var o=t[n],s=a.get(a.mapDimension(o),e);fy(s,r.getAxis(o).type)||i.push(r.dataToPoint(s,o))}return i}function Xs(a,e,t,r,i){var n=Qh(a,t,r,i),o=new Be({shape:{points:n},z2:10});return e.add(o),a.setItemGraphicEl(t,o),o}function Ks(a){var e=a.get("smooth",!0);return e===!0&&(e=hy),e=kc(e),Nc(e)&&(e=0),{smooth:e}}function Ti(a,e,t,r){a.useStyle(e.getItemVisual(t,"style")),a.style.fill=null,a.setShape("smooth",r.smooth);var i=e.getItemModel(t),n=i.getModel("emphasis");Qt(a,i,"lineStyle"),St(a,n.get("focus"),n.get("blurScope"),n.get("disabled"))}function fy(a,e){return e==="category"?a==null:a==null||isNaN(a)}var py=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,r){return Qe(null,this,{useEncodeDefaulter:B(dy,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var r=this.coordinateSystem,i=this.getData(),n=[];return r.eachActiveState(i,function(o,s){t===o&&n.push(i.getRawIndex(s))}),n},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(wt);function dy(a){var e=a.ecModel.getComponent("parallel",a.get("parallelIndex"));if(e){var t={};return D(e.dimensions,function(r){var i=gy(r);t[r]=i}),t}}function gy(a){return+a.replace("dim","")}var yy=["lineStyle","opacity"],my={seriesType:"parallel",reset:function(a,e){var t=a.coordinateSystem,r={normal:a.get(["lineStyle","opacity"]),active:a.get("activeOpacity"),inactive:a.get("inactiveOpacity")};return{progress:function(i,n){t.eachActiveState(n,function(o,s){var l=r[o];if(o==="normal"&&n.hasItemOption){var u=n.getItemModel(s).get(yy,!0);u!=null&&(l=u)}var h=n.ensureUniqueItemVisual(s,"style");h.opacity=l},i.start,i.end)}}}};function Sy(a){xy(a),by(a)}function xy(a){if(!a.parallel){var e=!1;D(a.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(a.parallel=[{}])}}function by(a){var e=ue(a.parallelAxis);D(e,function(t){if(Ot(t)){var r=t.parallelIndex||0,i=ue(a.parallel)[r];i&&i.parallelAxisDefault&&zt(t,i.parallelAxisDefault,!1)}})}var _y=5,wy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this._model=t,this._api=i,this._handlers||(this._handlers={},D(Ay,function(n,o){i.getZr().on(o,this._handlers[o]=B(n,this))},this)),Za(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,r){Qu(this,"_throttledDispatchExpand"),D(this._handlers,function(i,n){r.getZr().off(n,i)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(F({type:"parallelAxisExpand"},t))},e.type="parallel",e}(te),Ay={mousedown:function(a){Di(this,"click")&&(this._mouseDownPoint=[a.offsetX,a.offsetY])},mouseup:function(a){var e=this._mouseDownPoint;if(Di(this,"click")&&e){var t=[a.offsetX,a.offsetY],r=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(r>_y)return;var i=this._model.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]);i.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:i.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(a){if(!(this._mouseDownPoint||!Di(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]),r=t.behavior;r==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(r==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:r==="jump"?null:{duration:0}})}}};function Di(a,e){var t=a._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}var Ty=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){a.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var r=this.option;t&&zt(r,t,!0),this._initDimensions()},e.prototype.contains=function(t,r){var i=t.get("parallelIndex");return i!=null&&r.getComponent("parallel",i)===this},e.prototype.setAxisExpand=function(t){D(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(r){t.hasOwnProperty(r)&&(this.option[r]=t[r])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],r=this.parallelAxisIndex=[],i=Gt(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(n){return(n.get("parallelIndex")||0)===this.componentIndex},this);D(i,function(n){t.push("dim"+n.get("dim")),r.push(n.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}($t),Dy=function(a){E(e,a);function e(t,r,i,n,o){var s=a.call(this,t,r,i)||this;return s.type=n||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(de),Li=D,tv=Math.min,ev=Math.max,qs=Math.floor,Ly=Math.ceil,js=kn,Cy=Math.PI,Iy=function(){function a(e,t,r){this.type="parallel",this._axesMap=rt(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var i=e.dimensions,n=e.parallelAxisIndex;Li(i,function(o,s){var l=n[s],u=t.getComponent("parallelAxis",l),h=this._axesMap.set(o,new Dy(o,Bn(u),[0,0],u.get("type"),l)),v=h.type==="category";h.onBand=v&&u.get("boundaryGap"),h.inverse=u.get("inverse"),u.axis=h,h.model=u,h.coordinateSystem=u.coordinateSystem=this},this)},a.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},a.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),r=t.axisBase,i=t.layoutBase,n=t.pixelDimIndex,o=e[1-n],s=e[n];return o>=r&&o<=r+t.axisLength&&s>=i&&s<=i+t.layoutLength},a.prototype.getModel=function(){return this._model},a.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(r){if(e.contains(r,t)){var i=r.getData();Li(this.dimensions,function(n){var o=this._axesMap.get(n);o.scale.unionExtentFromData(i,i.mapDimension(n)),ya(o.scale,o.model)},this)}},this)},a.prototype.resize=function(e,t){this._rect=Yt(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},a.prototype.getRect=function(){return this._rect},a.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,r=["x","y"],i=["width","height"],n=e.get("layout"),o=n==="horizontal"?0:1,s=t[i[o]],l=[0,s],u=this.dimensions.length,h=jr(e.get("axisExpandWidth"),l),v=jr(e.get("axisExpandCount")||0,[0,u]),c=e.get("axisExpandable")&&u>3&&u>v&&v>1&&h>0&&s>0,f=e.get("axisExpandWindow"),p;if(f)p=jr(f[1]-f[0],l),f[1]=f[0]+p;else{p=jr(h*(v-1),l);var d=e.get("axisExpandCenter")||qs(u/2);f=[h*d-p/2],f[1]=f[0]+p}var g=(s-p)/(u-v);g<3&&(g=0);var y=[qs(js(f[0]/h,1))+1,Ly(js(f[1]/h,1))-1],m=g/h*f[0];return{layout:n,pixelDimIndex:o,layoutBase:t[r[o]],layoutLength:s,axisBase:t[r[1-o]],axisLength:t[i[1-o]],axisExpandable:c,axisExpandWidth:h,axisCollapseWidth:g,axisExpandWindow:f,axisCount:u,winInnerIndices:y,axisExpandWindow0Pos:m}},a.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,r=this.dimensions,i=this._makeLayoutInfo(),n=i.layout;t.each(function(o){var s=[0,i.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),Li(r,function(o,s){var l=(i.axisExpandable?Py:My)(s,i),u={horizontal:{x:l.position,y:i.axisLength},vertical:{x:0,y:l.position}},h={horizontal:Cy/2,vertical:0},v=[u[n].x+e.x,u[n].y+e.y],c=h[n],f=tr();Oa(f,f,c),Oe(f,f,v),this._axesLayout[o]={position:v,rotation:c,transform:f,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},a.prototype.getAxis=function(e){return this._axesMap.get(e)},a.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},a.prototype.eachActiveState=function(e,t,r,i){r==null&&(r=0),i==null&&(i=e.count());var n=this._axesMap,o=this.dimensions,s=[],l=[];D(o,function(g){s.push(e.mapDimension(g)),l.push(n.get(g).model)});for(var u=this.hasAxisBrushed(),h=r;h<i;h++){var v=void 0;if(!u)v="normal";else{v="active";for(var c=e.getValues(s,h),f=0,p=o.length;f<p;f++){var d=l[f].getActiveState(c[f]);if(d==="inactive"){v="inactive";break}}}t(v,h)}},a.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,r=!1,i=0,n=e.length;i<n;i++)t.get(e[i]).model.getActiveState()!=="normal"&&(r=!0);return r},a.prototype.axisCoordToPoint=function(e,t){var r=this._axesLayout[t];return xe([e,0],r.transform)},a.prototype.getAxisLayout=function(e){return ht(this._axesLayout[e])},a.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),r=t.pixelDimIndex,i=t.axisExpandWindow.slice(),n=i[1]-i[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:i};var s=e[r]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",h=t.axisCollapseWidth,v=this._model.get("axisExpandSlideTriggerArea"),c=v[0]!=null;if(h)c&&h&&s<n*v[0]?(u="jump",l=s-n*v[2]):c&&h&&s>n*(1-v[0])?(u="jump",l=s-n*(1-v[2])):(l=s-n*v[1])>=0&&(l=s-n*(1-v[1]))<=0&&(l=0),l*=t.axisExpandWidth/h,l?Or(l,i,o,"all"):u="none";else{var f=i[1]-i[0],p=o[1]*s/f;i=[ev(0,p-f/2)],i[1]=tv(o[1],i[0]+f),i[0]=i[1]-f}return{axisExpandWindow:i,behavior:u}},a}();function jr(a,e){return tv(ev(a,e[0]),e[1])}function My(a,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*a,axisNameAvailableWidth:t,axisLabelShow:!0}}function Py(a,e){var t=e.layoutLength,r=e.axisExpandWidth,i=e.axisCount,n=e.axisCollapseWidth,o=e.winInnerIndices,s,l=n,u=!1,h;return a<o[0]?(s=a*n,h=n):a<=o[1]?(s=e.axisExpandWindow0Pos+a*r-e.axisExpandWindow[0],l=r,u=!0):(s=t-(i-1-a)*n,h=n),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:h}}function Ry(a,e){var t=[];return a.eachComponent("parallel",function(r,i){var n=new Iy(r,a,e);n.name="parallel_"+i,n.resize(r,e),r.coordinateSystem=n,n.model=r,t.push(n)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="parallel"){var i=r.getReferringComponents("parallel",er).models[0];r.coordinateSystem=i.coordinateSystem}}),t}var Ey={create:Ry},yn=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return Ku([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var r=this.activeIntervals=ht(t);if(r)for(var i=r.length-1;i>=0;i--)qt(r[i])},e.prototype.getActiveState=function(t){var r=this.activeIntervals;if(!r.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(r.length===1){var i=r[0];if(i[0]<=t&&t<=i[1])return"active"}else for(var n=0,o=r.length;n<o;n++)if(r[n][0]<=t&&t<=r[n][1])return"active";return"inactive"},e}($t);ie(yn,Fn);var Vy=["axisLine","axisTickLabel","axisName"],ky=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){a.prototype.init.apply(this,arguments),(this._brushController=new th(r.getZr())).on("brush",B(this._onBrush,this))},e.prototype.render=function(t,r,i,n){if(!Ny(t,r,n)){this.axisModel=t,this.api=i,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new $,this.group.add(this._axisGroup),!!t.get("show")){var s=Oy(t,r),l=s.coordinateSystem,u=t.getAreaSelectStyle(),h=u.width,v=t.axis.dim,c=l.getAxisLayout(v),f=F({strokeContainThreshold:h},c),p=new je(t,f);D(Vy,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(f,u,t,s,h,i),Hn(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,r,i,n,o,s){var l=i.axis.getExtent(),u=l[1]-l[0],h=Math.min(30,Math.abs(u)*.1),v=bt.create({x:l[0],y:-o/2,width:u,height:o});v.x-=h,v.width+=2*h,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:Gc(v),isTargetByCursor:Oc(v,s,n),getLinearBrushOtherExtent:zc(v,0)}]).enableBrush({brushType:"lineX",brushStyle:r,removeOnClick:!0}).updateCovers(zy(i))},e.prototype._onBrush=function(t){var r=t.areas,i=this.axisModel,n=i.axis,o=W(r,function(s){return[n.coordToData(s.range[0],!0),n.coordToData(s.range[1],!0)]});(!i.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:i.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(te);function Ny(a,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===a}function zy(a){var e=a.axis;return W(a.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function Oy(a,e){return e.getComponent("parallel",a.get("parallelIndex"))}var Gy={type:"axisAreaSelect",event:"axisAreaSelected"};function By(a){a.registerAction(Gy,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(r){r.axis.model.setActiveIntervals(e.intervals)})}),a.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(r){r.setAxisExpand(e)})})}var Fy={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function rv(a){a.registerComponentView(wy),a.registerComponentModel(Ty),a.registerCoordinateSystem("parallel",Ey),a.registerPreprocessor(Sy),a.registerComponentModel(yn),a.registerComponentView(ky),ma(a,"parallel",yn,Fy),By(a)}function Hy(a){X(rv),a.registerChartView(vy),a.registerSeriesModel(py),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,my)}var Wy=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return a}(),Zy=function(a){E(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new Wy},e.prototype.buildPath=function(t,r){var i=r.extent;t.moveTo(r.x1,r.y1),t.bezierCurveTo(r.cpx1,r.cpy1,r.cpx2,r.cpy2,r.x2,r.y2),r.orient==="vertical"?(t.lineTo(r.x2+i,r.y2),t.bezierCurveTo(r.cpx2+i,r.cpy2,r.cpx1+i,r.cpy1,r.x1+i,r.y1)):(t.lineTo(r.x2,r.y2+i),t.bezierCurveTo(r.cpx2,r.cpy2+i,r.cpx1,r.cpy1+i,r.x1,r.y1+i)),t.closePath()},e.prototype.highlight=function(){Nn(this)},e.prototype.downplay=function(){zn(this)},e}(Ut),Uy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,r,i){var n=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,h=l.height,v=t.getData(),c=t.getData("edge"),f=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var d=new Zy,g=nt(d);g.dataIndex=p.dataIndex,g.seriesIndex=t.seriesIndex,g.dataType="edge";var y=p.getModel(),m=y.getModel("lineStyle"),S=m.get("curveness"),x=p.node1.getLayout(),b=p.node1.getModel(),_=b.get("localX"),w=b.get("localY"),T=p.node2.getLayout(),L=p.node2.getModel(),A=L.get("localX"),C=L.get("localY"),I=p.getLayout(),M,P,R,V,k,N,G,Z;d.shape.extent=Math.max(1,I.dy),d.shape.orient=f,f==="vertical"?(M=(_!=null?_*u:x.x)+I.sy,P=(w!=null?w*h:x.y)+x.dy,R=(A!=null?A*u:T.x)+I.ty,V=C!=null?C*h:T.y,k=M,N=P*(1-S)+V*S,G=R,Z=P*S+V*(1-S)):(M=(_!=null?_*u:x.x)+x.dx,P=(w!=null?w*h:x.y)+I.sy,R=A!=null?A*u:T.x,V=(C!=null?C*h:T.y)+I.ty,k=M*(1-S)+R*S,N=P,G=M*S+R*(1-S),Z=V),d.setShape({x1:M,y1:P,x2:R,y2:V,cpx1:k,cpy1:N,cpx2:G,cpy2:Z}),d.useStyle(m.getItemStyle()),Js(d.style,f,p);var U=""+y.get("value"),K=Nt(y,"edgeLabel");jt(d,K,{labelFetcher:{getFormattedLabel:function(tt,Lt,ge,j,Y,ot){return t.getFormattedLabel(tt,Lt,"edge",j,kr(Y,K.normal&&K.normal.get("formatter"),U),ot)}},labelDataIndex:p.dataIndex,defaultText:U}),d.setTextConfig({position:"inside"});var J=y.getModel("emphasis");Qt(d,y,"lineStyle",function(tt){var Lt=tt.getItemStyle();return Js(Lt,f,p),Lt}),s.add(d),c.setItemGraphicEl(p.dataIndex,d);var q=J.get("focus");St(d,q==="adjacency"?p.getAdjacentDataIndices():q==="trajectory"?p.getTrajectoryDataIndices():q,J.get("blurScope"),J.get("disabled"))}),o.eachNode(function(p){var d=p.getLayout(),g=p.getModel(),y=g.get("localX"),m=g.get("localY"),S=g.getModel("emphasis"),x=g.get(["itemStyle","borderRadius"])||0,b=new gt({shape:{x:y!=null?y*u:d.x,y:m!=null?m*h:d.y,width:d.dx,height:d.dy,r:x},style:g.getModel("itemStyle").getItemStyle(),z2:10});jt(b,Nt(g),{labelFetcher:{getFormattedLabel:function(w,T){return t.getFormattedLabel(w,T,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),b.disableLabelAnimation=!0,b.setStyle("fill",p.getVisual("color")),b.setStyle("decal",p.getVisual("style").decal),Qt(b,g),s.add(b),v.setItemGraphicEl(p.dataIndex,b),nt(b).dataType="node";var _=S.get("focus");St(b,_==="adjacency"?p.getAdjacentDataIndices():_==="trajectory"?p.getTrajectoryDataIndices():_,S.get("blurScope"),S.get("disabled"))}),v.eachItemGraphicEl(function(p,d){var g=v.getItemModel(d);g.get("draggable")&&(p.drift=function(y,m){n._focusAdjacencyDisabled=!0,this.shape.x+=y,this.shape.y+=m,this.dirty(),i.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:v.getRawIndex(d),localX:this.shape.x/u,localY:this.shape.y/h})},p.ondragend=function(){n._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(Yy(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(_t);function Js(a,e,t){switch(a.fill){case"source":a.fill=t.node1.getVisual("color"),a.decal=t.node1.getVisual("style").decal;break;case"target":a.fill=t.node2.getVisual("color"),a.decal=t.node2.getVisual("style").decal;break;case"gradient":var r=t.node1.getVisual("color"),i=t.node2.getVisual("color");Q(r)&&Q(i)&&(a.fill=new In(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:r,offset:0},{color:i,offset:1}]))}}function Yy(a,e,t){var r=new gt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return ve(r,{shape:{width:a.width+20}},e,t),r}var $y=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){var i=t.edges||t.links||[],n=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Jt(o[l],this,r));var u=Jh(n,i,this,!0,h);return u.data;function h(v,c){v.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getData().getItemLayout(p);if(g){var y=g.depth,m=d.levelModels[y];m&&(f.parentModel=m)}return f}),c.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getGraph().getEdgeByIndex(p),y=g.node1.getLayout();if(y){var m=y.depth,S=d.levelModels[m];S&&(f.parentModel=S)}return f})}},e.prototype.setNodePosition=function(t,r){var i=this.option.data||this.option.nodes,n=i[t];n.localX=r[0],n.localY=r[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,r,i){function n(f){return isNaN(f)||f==null}if(i==="edge"){var o=this.getDataParams(t,i),s=o.data,l=o.value,u=s.source+" -- "+s.target;return Zt("nameValue",{name:u,value:l,noValue:n(l)})}else{var h=this.getGraph().getNodeByIndex(t),v=h.getLayout().value,c=this.getDataParams(t,i).data.name;return Zt("nameValue",{name:c!=null?c+"":null,value:v,noValue:n(v)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,r){var i=a.prototype.getDataParams.call(this,t,r);if(i.value==null&&r==="node"){var n=this.getGraph().getNodeByIndex(t),o=n.getLayout().value;i.value=o}return i},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(wt);function Xy(a,e){a.eachSeriesByType("sankey",function(t){var r=t.get("nodeWidth"),i=t.get("nodeGap"),n=Ky(t,e);t.layoutInfo=n;var o=n.width,s=n.height,l=t.getGraph(),u=l.nodes,h=l.edges;jy(u);var v=Gt(u,function(d){return d.getLayout().value===0}),c=v.length!==0?0:t.get("layoutIterations"),f=t.get("orient"),p=t.get("nodeAlign");qy(u,h,r,i,o,s,c,f,p)})}function Ky(a,e){return Yt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function qy(a,e,t,r,i,n,o,s,l){Jy(a,e,t,i,n,s,l),rm(a,e,n,i,r,o,s),vm(a,s)}function jy(a){D(a,function(e){var t=be(e.outEdges,Da),r=be(e.inEdges,Da),i=e.getValue()||0,n=Math.max(t,r,i);e.setLayout({value:n},!0)})}function Jy(a,e,t,r,i,n,o){for(var s=[],l=[],u=[],h=[],v=0,c=0;c<e.length;c++)s[c]=1;for(var c=0;c<a.length;c++)l[c]=a[c].inEdges.length,l[c]===0&&u.push(a[c]);for(var f=-1;u.length;){for(var p=0;p<u.length;p++){var d=u[p],g=d.hostGraph.data.getRawDataItem(d.dataIndex),y=g.depth!=null&&g.depth>=0;y&&g.depth>f&&(f=g.depth),d.setLayout({depth:y?g.depth:v},!0),n==="vertical"?d.setLayout({dy:t},!0):d.setLayout({dx:t},!0);for(var m=0;m<d.outEdges.length;m++){var S=d.outEdges[m],x=e.indexOf(S);s[x]=0;var b=S.node2,_=a.indexOf(b);--l[_]===0&&h.indexOf(b)<0&&h.push(b)}}++v,u=h,h=[]}for(var c=0;c<s.length;c++)if(s[c]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var w=f>v-1?f:v-1;o&&o!=="left"&&Qy(a,o,n,w);var T=n==="vertical"?(i-t)/w:(r-t)/w;em(a,T,n)}function av(a){var e=a.hostGraph.data.getRawDataItem(a.dataIndex);return e.depth!=null&&e.depth>=0}function Qy(a,e,t,r){if(e==="right"){for(var i=[],n=a,o=0;n.length;){for(var s=0;s<n.length;s++){var l=n[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var h=l.inEdges[u];i.indexOf(h.node1)<0&&i.push(h.node1)}}n=i,i=[],++o}D(a,function(v){av(v)||v.setLayout({depth:Math.max(0,r-v.getLayout().skNodeHeight)},!0)})}else e==="justify"&&tm(a,r)}function tm(a,e){D(a,function(t){!av(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function em(a,e,t){D(a,function(r){var i=r.getLayout().depth*e;t==="vertical"?r.setLayout({y:i},!0):r.setLayout({x:i},!0)})}function rm(a,e,t,r,i,n,o){var s=am(a,o);im(s,e,t,r,i,o),Ci(s,i,t,r,o);for(var l=1;n>0;n--)l*=.99,nm(s,l,o),Ci(s,i,t,r,o),hm(s,l,o),Ci(s,i,t,r,o)}function am(a,e){var t=[],r=e==="vertical"?"y":"x",i=an(a,function(n){return n.getLayout()[r]});return i.keys.sort(function(n,o){return n-o}),D(i.keys,function(n){t.push(i.buckets.get(n))}),t}function im(a,e,t,r,i,n){var o=1/0;D(a,function(s){var l=s.length,u=0;D(s,function(v){u+=v.getLayout().value});var h=n==="vertical"?(r-(l-1)*i)/u:(t-(l-1)*i)/u;h<o&&(o=h)}),D(a,function(s){D(s,function(l,u){var h=l.getLayout().value*o;n==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:h},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:h},!0))})}),D(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function Ci(a,e,t,r,i){var n=i==="vertical"?"x":"y";D(a,function(o){o.sort(function(d,g){return d.getLayout()[n]-g.getLayout()[n]});for(var s,l,u,h=0,v=o.length,c=i==="vertical"?"dx":"dy",f=0;f<v;f++)l=o[f],u=h-l.getLayout()[n],u>0&&(s=l.getLayout()[n]+u,i==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),h=l.getLayout()[n]+l.getLayout()[c]+e;var p=i==="vertical"?r:t;if(u=h-e-p,u>0){s=l.getLayout()[n]-u,i==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),h=s;for(var f=v-2;f>=0;--f)l=o[f],u=l.getLayout()[n]+l.getLayout()[c]+e-h,u>0&&(s=l.getLayout()[n]-u,i==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),h=l.getLayout()[n]}})}function nm(a,e,t){D(a.slice().reverse(),function(r){D(r,function(i){if(i.outEdges.length){var n=be(i.outEdges,om,t)/be(i.outEdges,Da);if(isNaN(n)){var o=i.outEdges.length;n=o?be(i.outEdges,sm,t)/o:0}if(t==="vertical"){var s=i.getLayout().x+(n-we(i,t))*e;i.setLayout({x:s},!0)}else{var l=i.getLayout().y+(n-we(i,t))*e;i.setLayout({y:l},!0)}}})})}function om(a,e){return we(a.node2,e)*a.getValue()}function sm(a,e){return we(a.node2,e)}function lm(a,e){return we(a.node1,e)*a.getValue()}function um(a,e){return we(a.node1,e)}function we(a,e){return e==="vertical"?a.getLayout().x+a.getLayout().dx/2:a.getLayout().y+a.getLayout().dy/2}function Da(a){return a.getValue()}function be(a,e,t){for(var r=0,i=a.length,n=-1;++n<i;){var o=+e(a[n],t);isNaN(o)||(r+=o)}return r}function hm(a,e,t){D(a,function(r){D(r,function(i){if(i.inEdges.length){var n=be(i.inEdges,lm,t)/be(i.inEdges,Da);if(isNaN(n)){var o=i.inEdges.length;n=o?be(i.inEdges,um,t)/o:0}if(t==="vertical"){var s=i.getLayout().x+(n-we(i,t))*e;i.setLayout({x:s},!0)}else{var l=i.getLayout().y+(n-we(i,t))*e;i.setLayout({y:l},!0)}}})})}function vm(a,e){var t=e==="vertical"?"x":"y";D(a,function(r){r.outEdges.sort(function(i,n){return i.node2.getLayout()[t]-n.node2.getLayout()[t]}),r.inEdges.sort(function(i,n){return i.node1.getLayout()[t]-n.node1.getLayout()[t]})}),D(a,function(r){var i=0,n=0;D(r.outEdges,function(o){o.setLayout({sy:i},!0),i+=o.getLayout().dy}),D(r.inEdges,function(o){o.setLayout({ty:n},!0),n+=o.getLayout().dy})})}function cm(a){a.eachSeriesByType("sankey",function(e){var t=e.getGraph(),r=t.nodes,i=t.edges;if(r.length){var n=1/0,o=-1/0;D(r,function(s){var l=s.getLayout().value;l<n&&(n=l),l>o&&(o=l)}),D(r,function(s){var l=new yt({type:"color",mappingMethod:"linear",dataExtent:[n,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),h=s.getModel().get(["itemStyle","color"]);h!=null?(s.setVisual("color",h),s.setVisual("style",{fill:h})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}i.length&&D(i,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function fm(a){a.registerChartView(Uy),a.registerSeriesModel($y),a.registerLayout(Xy),a.registerVisual(cm),a.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(r){r.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}var iv=function(){function a(){}return a.prototype._hasEncodeRule=function(e){var t=this.getEncode();return t&&t.get(e)!=null},a.prototype.getInitialData=function(e,t){var r,i=t.getComponent("xAxis",this.get("xAxisIndex")),n=t.getComponent("yAxis",this.get("yAxisIndex")),o=i.get("type"),s=n.get("type"),l;o==="category"?(e.layout="horizontal",r=i.getOrdinalMeta(),l=!this._hasEncodeRule("x")):s==="category"?(e.layout="vertical",r=n.getOrdinalMeta(),l=!this._hasEncodeRule("y")):e.layout=e.layout||"horizontal";var u=["x","y"],h=e.layout==="horizontal"?0:1,v=this._baseAxisDim=u[h],c=u[1-h],f=[i,n],p=f[h].get("type"),d=f[1-h].get("type"),g=e.data;if(g&&l){var y=[];D(g,function(x,b){var _;H(x)?(_=x.slice(),x.unshift(b)):H(x.value)?(_=F({},x),_.value=_.value.slice(),x.value.unshift(b)):_=x,y.push(_)}),e.data=y}var m=this.defaultValueDimensions,S=[{name:v,type:nn(p),ordinalMeta:r,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:c,type:nn(d),dimsDef:m.slice()}];return Ga(this,{coordDimensions:S,dimensionsCount:m.length+1,encodeDefaulter:it(Bc,S,this)})},a.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},a}(),nv=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],t.visualDrawType="stroke",t}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(wt);ie(nv,iv,!0);var pm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this.group,s=this._data;this._data||o.removeAll();var l=t.get("layout")==="horizontal"?1:0;n.diff(s).add(function(u){if(n.hasValue(u)){var h=n.getItemLayout(u),v=Qs(h,n,u,l,!0);n.setItemGraphicEl(u,v),o.add(v)}}).update(function(u,h){var v=s.getItemGraphicEl(h);if(!n.hasValue(u)){o.remove(v);return}var c=n.getItemLayout(u);v?(rr(v),ov(c,v,n,u)):v=Qs(c,n,u,l),o.add(v),n.setItemGraphicEl(u,v)}).remove(function(u){var h=s.getItemGraphicEl(u);h&&o.remove(h)}).execute(),this._data=n},e.prototype.remove=function(t){var r=this.group,i=this._data;this._data=null,i&&i.eachItemGraphicEl(function(n){n&&r.remove(n)})},e.type="boxplot",e}(_t),dm=function(){function a(){}return a}(),gm=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="boxplotBoxPath",r}return e.prototype.getDefaultShape=function(){return new dm},e.prototype.buildPath=function(t,r){var i=r.points,n=0;for(t.moveTo(i[n][0],i[n][1]),n++;n<4;n++)t.lineTo(i[n][0],i[n][1]);for(t.closePath();n<i.length;n++)t.moveTo(i[n][0],i[n][1]),n++,t.lineTo(i[n][0],i[n][1])},e}(Ut);function Qs(a,e,t,r,i){var n=a.ends,o=new gm({shape:{points:i?ym(n,r,a):n}});return ov(a,o,e,t,i),o}function ov(a,e,t,r,i){var n=t.hostModel,o=Gr[i?"initProps":"updateProps"];o(e,{shape:{points:a.ends}},n,r),e.useStyle(t.getItemVisual(r,"style")),e.style.strokeNoScale=!0,e.z2=100;var s=t.getItemModel(r),l=s.getModel("emphasis");Qt(e,s),St(e,l.get("focus"),l.get("blurScope"),l.get("disabled"))}function ym(a,e,t){return W(a,function(r){return r=r.slice(),r[e]=t.initBaseline,r})}var xr=D;function mm(a){var e=Sm(a);xr(e,function(t){var r=t.seriesModels;r.length&&(xm(t),xr(r,function(i,n){bm(i,t.boxOffsetList[n],t.boxWidthList[n])}))})}function Sm(a){var e=[],t=[];return a.eachSeriesByType("boxplot",function(r){var i=r.getBaseAxis(),n=Mt(t,i);n<0&&(n=t.length,t[n]=i,e[n]={axis:i,seriesModels:[]}),e[n].seriesModels.push(r)}),e}function xm(a){var e=a.axis,t=a.seriesModels,r=t.length,i=a.boxWidthList=[],n=a.boxOffsetList=[],o=[],s;if(e.type==="category")s=e.getBandWidth();else{var l=0;xr(t,function(p){l=Math.max(l,p.getData().count())});var u=e.getExtent();s=Math.abs(u[1]-u[0])/l}xr(t,function(p){var d=p.get("boxWidth");H(d)||(d=[d,d]),o.push([O(d[0],s)||0,O(d[1],s)||0])});var h=s*.8-2,v=h/r*.3,c=(h-v*(r-1))/r,f=c/2-h/2;xr(t,function(p,d){n.push(f),f+=v+c,i.push(Math.min(Math.max(c,o[d][0]),o[d][1]))})}function bm(a,e,t){var r=a.coordinateSystem,i=a.getData(),n=t/2,o=a.get("layout")==="horizontal"?0:1,s=1-o,l=["x","y"],u=i.mapDimension(l[o]),h=i.mapDimensionsAll(l[s]);if(u==null||h.length<5)return;for(var v=0;v<i.count();v++){var c=i.get(u,v),f=S(c,h[2],v),p=S(c,h[0],v),d=S(c,h[1],v),g=S(c,h[3],v),y=S(c,h[4],v),m=[];x(m,d,!1),x(m,g,!0),m.push(p,d,y,g),b(m,p),b(m,y),b(m,f),i.setItemLayout(v,{initBaseline:f[s],ends:m})}function S(_,w,T){var L=i.get(w,T),A=[];A[o]=_,A[s]=L;var C;return isNaN(_)||isNaN(L)?C=[NaN,NaN]:(C=r.dataToPoint(A),C[o]+=e),C}function x(_,w,T){var L=w.slice(),A=w.slice();L[o]+=n,A[o]-=n,T?_.push(L,A):_.push(A,L)}function b(_,w){var T=w.slice(),L=w.slice();T[o]-=n,L[o]+=n,_.push(T,L)}}function _m(a,e){e=e||{};for(var t=[],r=[],i=e.boundIQR,n=i==="none"||i===0,o=0;o<a.length;o++){var s=qt(a[o].slice()),l=ri(s,.25),u=ri(s,.5),h=ri(s,.75),v=s[0],c=s[s.length-1],f=(i??1.5)*(h-l),p=n?v:Math.max(v,l-f),d=n?c:Math.min(c,h+f),g=e.itemNameFormatter,y=st(g)?g({value:o}):Q(g)?g.replace("{value}",o+""):o+"";t.push([y,p,l,u,h,d]);for(var m=0;m<s.length;m++){var S=s[m];if(S<p||S>d){var x=[y,S];r.push(x)}}}return{boxData:t,outliers:r}}var wm={type:"echarts:boxplot",transform:function(e){var t=e.upstream;if(t.sourceFormat!==Fc){var r="";eh(r)}var i=_m(t.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:i.boxData},{data:i.outliers}]}};function Am(a){a.registerSeriesModel(nv),a.registerChartView(pm),a.registerLayout(mm),a.registerTransform(wm)}var Tm=["itemStyle","borderColor"],Dm=["itemStyle","borderColor0"],Lm=["itemStyle","borderColorDoji"],Cm=["itemStyle","color"],Im=["itemStyle","color0"];function oo(a,e){return e.get(a>0?Cm:Im)}function so(a,e){return e.get(a===0?Lm:a>0?Tm:Dm)}var Mm={seriesType:"candlestick",plan:Wn(),performRawSeries:!0,reset:function(a,e){if(!e.isSeriesFiltered(a)){var t=a.pipelineContext.large;return!t&&{progress:function(r,i){for(var n;(n=r.next())!=null;){var o=i.getItemModel(n),s=i.getItemLayout(n).sign,l=o.getItemStyle();l.fill=oo(s,o),l.stroke=so(s,o)||l.fill;var u=i.ensureUniqueItemVisual(n,"style");F(u,l)}}}}}},Pm=["color","borderColor"],Rm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,r,i){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,r,i,n){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,r):this._incrementalRenderNormal(t,r)},e.prototype.eachRendered=function(t){Wa(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t){var r=t.getData(),i=this._data,n=this.group,o=r.getLayout("isSimpleBox"),s=t.get("clip",!0),l=t.coordinateSystem,u=l.getArea&&l.getArea();this._data||n.removeAll(),r.diff(i).add(function(h){if(r.hasValue(h)){var v=r.getItemLayout(h);if(s&&tl(u,v))return;var c=Ii(v,h,!0);ve(c,{shape:{points:v.ends}},t,h),Mi(c,r,h,o),n.add(c),r.setItemGraphicEl(h,c)}}).update(function(h,v){var c=i.getItemGraphicEl(v);if(!r.hasValue(h)){n.remove(c);return}var f=r.getItemLayout(h);if(s&&tl(u,f)){n.remove(c);return}c?(mt(c,{shape:{points:f.ends}},t,h),rr(c)):c=Ii(f),Mi(c,r,h,o),n.add(c),r.setItemGraphicEl(h,c)}).remove(function(h){var v=i.getItemGraphicEl(h);v&&n.remove(v)}).execute(),this._data=r},e.prototype._renderLarge=function(t){this._clear(),el(t,this.group);var r=t.get("clip",!0)?Ua(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,r){for(var i=r.getData(),n=i.getLayout("isSimpleBox"),o;(o=t.next())!=null;){var s=i.getItemLayout(o),l=Ii(s);Mi(l,i,o,n),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(t,r){el(r,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(_t),Em=function(){function a(){}return a}(),Vm=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="normalCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new Em},e.prototype.buildPath=function(t,r){var i=r.points;this.__simpleBox?(t.moveTo(i[4][0],i[4][1]),t.lineTo(i[6][0],i[6][1])):(t.moveTo(i[0][0],i[0][1]),t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]),t.lineTo(i[3][0],i[3][1]),t.closePath(),t.moveTo(i[4][0],i[4][1]),t.lineTo(i[5][0],i[5][1]),t.moveTo(i[6][0],i[6][1]),t.lineTo(i[7][0],i[7][1]))},e}(Ut);function Ii(a,e,t){var r=a.ends;return new Vm({shape:{points:t?km(r,a):r},z2:100})}function tl(a,e){for(var t=!0,r=0;r<e.ends.length;r++)if(a.contain(e.ends[r][0],e.ends[r][1])){t=!1;break}return t}function Mi(a,e,t,r){var i=e.getItemModel(t);a.useStyle(e.getItemVisual(t,"style")),a.style.strokeNoScale=!0,a.__simpleBox=r,Qt(a,i);var n=e.getItemLayout(t).sign;D(a.states,function(s,l){var u=i.getModel(l),h=oo(n,u),v=so(n,u)||h,c=s.style||(s.style={});h&&(c.fill=h),v&&(c.stroke=v)});var o=i.getModel("emphasis");St(a,o.get("focus"),o.get("blurScope"),o.get("disabled"))}function km(a,e){return W(a,function(t){return t=t.slice(),t[1]=e.initBaseline,t})}var Nm=function(){function a(){}return a}(),Pi=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new Nm},e.prototype.buildPath=function(t,r){for(var i=r.points,n=0;n<i.length;)if(this.__sign===i[n++]){var o=i[n++];t.moveTo(o,i[n++]),t.lineTo(o,i[n++])}else n+=3},e}(Ut);function el(a,e,t,r){var i=a.getData(),n=i.getLayout("largePoints"),o=new Pi({shape:{points:n},__sign:1,ignoreCoarsePointer:!0});e.add(o);var s=new Pi({shape:{points:n},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new Pi({shape:{points:n},__sign:0,ignoreCoarsePointer:!0});e.add(l),Ri(1,o,a),Ri(-1,s,a),Ri(0,l,a),r&&(o.incremental=!0,s.incremental=!0),t&&t.push(o,s)}function Ri(a,e,t,r){var i=so(a,t)||oo(a,t),n=t.getModel("itemStyle").getItemStyle(Pm);e.useStyle(n),e.style.fill=null,e.style.stroke=i}var sv=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],t}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,r,i){var n=r.getItemLayout(t);return n&&i.rect(n.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(wt);ie(sv,iv,!0);function zm(a){!a||!H(a.series)||D(a.series,function(e){Ot(e)&&e.type==="k"&&(e.type="candlestick")})}var Om={seriesType:"candlestick",plan:Wn(),reset:function(a){var e=a.coordinateSystem,t=a.getData(),r=Gm(a,t),i=0,n=1,o=["x","y"],s=t.getDimensionIndex(t.mapDimension(o[i])),l=W(t.mapDimensionsAll(o[n]),t.getDimensionIndex,t),u=l[0],h=l[1],v=l[2],c=l[3];if(t.setLayout({candleWidth:r,isSimpleBox:r<=1.3}),s<0||l.length<4)return;return{progress:a.pipelineContext.large?p:f};function f(d,g){for(var y,m=g.getStore();(y=d.next())!=null;){var S=m.get(s,y),x=m.get(u,y),b=m.get(h,y),_=m.get(v,y),w=m.get(c,y),T=Math.min(x,b),L=Math.max(x,b),A=k(T,S),C=k(L,S),I=k(_,S),M=k(w,S),P=[];N(P,C,0),N(P,A,1),P.push(Z(M),Z(C),Z(I),Z(A));var R=g.getItemModel(y),V=!!R.get(["itemStyle","borderColorDoji"]);g.setItemLayout(y,{sign:rl(m,y,x,b,h,V),initBaseline:x>b?C[n]:A[n],ends:P,brushRect:G(_,w,S)})}function k(U,K){var J=[];return J[i]=K,J[n]=U,isNaN(K)||isNaN(U)?[NaN,NaN]:e.dataToPoint(J)}function N(U,K,J){var q=K.slice(),tt=K.slice();q[i]=ai(q[i]+r/2,1,!1),tt[i]=ai(tt[i]-r/2,1,!0),J?U.push(q,tt):U.push(tt,q)}function G(U,K,J){var q=k(U,J),tt=k(K,J);return q[i]-=r/2,tt[i]-=r/2,{x:q[0],y:q[1],width:r,height:tt[1]-q[1]}}function Z(U){return U[i]=ai(U[i],1),U}}function p(d,g){for(var y=Hc(d.count*4),m=0,S,x=[],b=[],_,w=g.getStore(),T=!!a.get(["itemStyle","borderColorDoji"]);(_=d.next())!=null;){var L=w.get(s,_),A=w.get(u,_),C=w.get(h,_),I=w.get(v,_),M=w.get(c,_);if(isNaN(L)||isNaN(I)||isNaN(M)){y[m++]=NaN,m+=3;continue}y[m++]=rl(w,_,A,C,h,T),x[i]=L,x[n]=I,S=e.dataToPoint(x,null,b),y[m++]=S?S[0]:NaN,y[m++]=S?S[1]:NaN,x[n]=M,S=e.dataToPoint(x,null,b),y[m++]=S?S[1]:NaN}g.setLayout("largePoints",y)}}};function rl(a,e,t,r,i,n){var o;return t>r?o=-1:t<r?o=1:o=n?0:e>0?a.get(i,e-1)<=r?1:-1:1,o}function Gm(a,e){var t=a.getBaseAxis(),r,i=t.type==="category"?t.getBandWidth():(r=t.getExtent(),Math.abs(r[1]-r[0])/e.count()),n=O(Tt(a.get("barMaxWidth"),i),i),o=O(Tt(a.get("barMinWidth"),1),i),s=a.get("barWidth");return s!=null?O(s,i):Math.max(Math.min(i/2,n),o)}function Bm(a){a.registerChartView(Rm),a.registerSeriesModel(sv),a.registerPreprocessor(zm),a.registerVisual(Mm),a.registerLayout(Om)}function al(a,e){var t=e.rippleEffectColor||e.color;a.eachChild(function(r){r.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var Fm=function(a){E(e,a);function e(t,r){var i=a.call(this)||this,n=new Yu(t,r),o=new $;return i.add(n),i.add(o),i.updateData(t,r),i}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var r=t.symbolType,i=t.color,n=t.rippleNumber,o=this.childAt(1),s=0;s<n;s++){var l=Pt(r,-1,-1,2,2,i);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/n*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}al(o,t)},e.prototype.updateEffectAnimation=function(t){for(var r=this._effectCfg,i=this.childAt(1),n=["symbolType","period","rippleScale","rippleNumber"],o=0;o<n.length;o++){var s=n[o];if(r[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}al(i,t)},e.prototype.highlight=function(){Nn(this)},e.prototype.downplay=function(){zn(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,r){var i=this,n=t.hostModel;this.childAt(0).updateData(t,r);var o=this.childAt(1),s=t.getItemModel(r),l=t.getItemVisual(r,"symbol"),u=Ha(t.getItemVisual(r,"symbolSize")),h=t.getItemVisual(r,"style"),v=h&&h.fill,c=s.getModel("emphasis");o.setScale(u),o.traverse(function(g){g.setStyle("fill",v)});var f=zr(t.getItemVisual(r,"symbolOffset"),u);f&&(o.x=f[0],o.y=f[1]);var p=t.getItemVisual(r,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var d={};d.showEffectOn=n.get("showEffectOn"),d.rippleScale=s.get(["rippleEffect","scale"]),d.brushType=s.get(["rippleEffect","brushType"]),d.period=s.get(["rippleEffect","period"])*1e3,d.effectOffset=r/t.count(),d.z=n.getShallow("z")||0,d.zlevel=n.getShallow("zlevel")||0,d.symbolType=l,d.color=v,d.rippleEffectColor=s.get(["rippleEffect","color"]),d.rippleNumber=s.get(["rippleEffect","number"]),d.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(g){g==="emphasis"?d.showEffectOn!=="render"&&i.startEffectAnimation(d):g==="normal"&&d.showEffectOn!=="render"&&i.stopEffectAnimation()}),this._effectCfg=d,St(this,c.get("focus"),c.get("blurScope"),c.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}($),Hm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new Na(Fm)},e.prototype.render=function(t,r,i){var n=t.getData(),o=this._symbolDraw;o.updateData(n,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var r=t.coordinateSystem,i=r&&r.getArea&&r.getArea();return t.get("clip",!0)?i:null},e.prototype.updateTransform=function(t,r,i){var n=t.getData();this.group.dirty();var o=ka("").reset(t,r,i);o.progress&&o.progress({start:0,end:n.count(),count:n.count()},n),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var r=t.coordinateSystem;r&&r.getRoamTransform&&(this.group.transform=Wc(r.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(_t),Wm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Qe(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,r,i){return i.point(r.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(wt);function Zm(a){a.registerChartView(Hm),a.registerSeriesModel(Wm),a.registerLayout(ka("effectScatter"))}var lv=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;return n.add(n.createLine(t,r,i)),n._updateEffectSymbol(t,r),n}return e.prototype.createLine=function(t,r,i){return new io(t,r,i)},e.prototype._updateEffectSymbol=function(t,r){var i=t.getItemModel(r),n=i.getModel("effect"),o=n.get("symbolSize"),s=n.get("symbol");H(o)||(o=[o,o]);var l=t.getItemVisual(r,"style"),u=n.get("color")||l&&l.stroke,h=this.childAt(1);this._symbolType!==s&&(this.remove(h),h=Pt(s,-.5,-.5,1,1,u),h.z2=100,h.culling=!0,this.add(h)),h&&(h.setStyle("shadowColor",u),h.setStyle(n.getItemStyle(["color"])),h.scaleX=o[0],h.scaleY=o[1],h.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,n,r))},e.prototype._updateEffectAnimation=function(t,r,i){var n=this.childAt(1);if(n){var o=t.getItemLayout(i),s=r.get("period")*1e3,l=r.get("loop"),u=r.get("roundTrip"),h=r.get("constantSpeed"),v=Vt(r.get("delay"),function(f){return f/t.count()*s/3});if(n.ignore=!0,this._updateAnimationPoints(n,o),h>0&&(s=this._getLineLength(n)/h*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){n.stopAnimation();var c=void 0;st(v)?c=v(i):c=v,n.__t>0&&(c=-s*n.__t),this._animateSymbol(n,s,c,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,r,i,n,o){if(r>0){t.__t=0;var s=this,l=t.animate("",n).when(o?r*2:r,{__t:o?2:1}).delay(i).during(function(){s._updateSymbolPosition(t)});n||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return fr(t.__p1,t.__cp1)+fr(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,r){t.__p1=r[0],t.__p2=r[1],t.__cp1=r[2]||[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]},e.prototype.updateData=function(t,r,i){this.childAt(0).updateData(t,r,i),this._updateEffectSymbol(t,r)},e.prototype._updateSymbolPosition=function(t){var r=t.__p1,i=t.__p2,n=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=qu,h=Zc;s[0]=u(r[0],n[0],i[0],o),s[1]=u(r[1],n[1],i[1],o);var v=t.__t<1?h(r[0],n[0],i[0],o):h(i[0],n[0],r[0],1-o),c=t.__t<1?h(r[1],n[1],i[1],o):h(i[1],n[1],r[1],1-o);t.rotation=-Math.atan2(c,v)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=fr(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*fr(r,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,r){this.childAt(0).updateLayout(t,r);var i=t.getItemModel(r).getModel("effect");this._updateEffectAnimation(t,i,r)},e}($),uv=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;return n._createPolyline(t,r,i),n}return e.prototype._createPolyline=function(t,r,i){var n=t.getItemLayout(r),o=new Be({shape:{points:n}});this.add(o),this._updateCommonStl(t,r,i)},e.prototype.updateData=function(t,r,i){var n=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(r)}};mt(o,s,n,r),this._updateCommonStl(t,r,i)},e.prototype._updateCommonStl=function(t,r,i){var n=this.childAt(0),o=t.getItemModel(r),s=i&&i.emphasisLineStyle,l=i&&i.focus,u=i&&i.blurScope,h=i&&i.emphasisDisabled;if(!i||t.hasItemOption){var v=o.getModel("emphasis");s=v.getModel("lineStyle").getLineStyle(),h=v.get("disabled"),l=v.get("focus"),u=v.get("blurScope")}n.useStyle(t.getItemVisual(r,"style")),n.style.fill=null,n.style.strokeNoScale=!0;var c=n.ensureState("emphasis");c.style=s,St(this,l,u,h)},e.prototype.updateLayout=function(t,r){var i=this.childAt(0);i.setShape("points",t.getItemLayout(r))},e}($),Um=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,r,i){return new uv(t,r,i)},e.prototype._updateAnimationPoints=function(t,r){this._points=r;for(var i=[0],n=0,o=1;o<r.length;o++){var s=r[o-1],l=r[o];n+=fr(s,l),i.push(n)}if(n===0){this._length=0;return}for(var o=0;o<i.length;o++)i[o]/=n;this._offsets=i,this._length=n},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var r=t.__t<1?t.__t:2-t.__t,i=this._points,n=this._offsets,o=i.length;if(n){var s=this._lastFrame,l;if(r<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(n[l]<=r);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(n[l]>r);l++);l=Math.min(l-1,o-2)}var h=(r-n[l])/(n[l+1]-n[l]),v=i[l],c=i[l+1];t.x=v[0]*(1-h)+h*c[0],t.y=v[1]*(1-h)+h*c[1];var f=t.__t<1?c[0]-v[0]:v[0]-c[0],p=t.__t<1?c[1]-v[1]:v[1]-c[1];t.rotation=-Math.atan2(p,f)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=r,t.ignore=!1}},e}(lv),Ym=function(){function a(){this.polyline=!1,this.curveness=0,this.segs=[]}return a}(),$m=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Ym},e.prototype.buildPath=function(t,r){var i=r.segs,n=r.curveness,o;if(r.polyline)for(o=this._off;o<i.length;){var s=i[o++];if(s>0){t.moveTo(i[o++],i[o++]);for(var l=1;l<s;l++)t.lineTo(i[o++],i[o++])}}else for(o=this._off;o<i.length;){var u=i[o++],h=i[o++],v=i[o++],c=i[o++];if(t.moveTo(u,h),n>0){var f=(u+v)/2-(h-c)*n,p=(h+c)/2-(v-u)*n;t.quadraticCurveTo(f,p,v,c)}else t.lineTo(v,c)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,r){var i=this.shape,n=i.segs,o=i.curveness,s=this.style.lineWidth;if(i.polyline)for(var l=0,u=0;u<n.length;){var h=n[u++];if(h>0)for(var v=n[u++],c=n[u++],f=1;f<h;f++){var p=n[u++],d=n[u++];if(Bo(v,c,p,d,s,t,r))return l}l++}else for(var l=0,u=0;u<n.length;){var v=n[u++],c=n[u++],p=n[u++],d=n[u++];if(o>0){var g=(v+p)/2-(c-d)*o,y=(c+d)/2-(p-v)*o;if(Uc(v,c,g,y,p,d,s,t,r))return l}else if(Bo(v,c,p,d,s,t,r))return l;l++}return-1},e.prototype.contain=function(t,r){var i=this.transformCoordToLocal(t,r),n=this.getBoundingRect();if(t=i[0],r=i[1],n.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,i=r.segs,n=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<i.length;){var h=i[u++],v=i[u++];n=Math.min(h,n),s=Math.max(h,s),o=Math.min(v,o),l=Math.max(v,l)}t=this._rect=new bt(n,o,s,l)}return t},e}(Ut),Xm=function(){function a(){this.group=new $}return a.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},a.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},a.prototype.incrementalUpdate=function(e,t){var r=this._newAdded[0],i=t.getLayout("linesPoints"),n=r&&r.shape.segs;if(n&&n.length<2e4){var o=n.length,s=new Float32Array(o+i.length);s.set(n),s.set(i,o),r.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:i}),this._setCommon(l,t),l.__startIndex=e.start}},a.prototype.remove=function(){this._clear()},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new $m({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},a.prototype._setCommon=function(e,t,r){var i=t.hostModel;e.setShape({polyline:i.get("polyline"),curveness:i.get(["lineStyle","curveness"])}),e.useStyle(i.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var n=t.getVisual("style");n&&n.stroke&&e.setStyle("stroke",n.stroke),e.setStyle("fill",null);var o=nt(e);o.seriesIndex=i.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),hv={seriesType:"lines",plan:Wn(),reset:function(a){var e=a.coordinateSystem;if(e){var t=a.get("polyline"),r=a.pipelineContext.large;return{progress:function(i,n){var o=[];if(r){var s=void 0,l=i.end-i.start;if(t){for(var u=0,h=i.start;h<i.end;h++)u+=a.getLineCoordsCount(h);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var v=0,c=[],h=i.start;h<i.end;h++){var f=a.getLineCoords(h,o);t&&(s[v++]=f);for(var p=0;p<f;p++)c=e.dataToPoint(o[p],!1,c),s[v++]=c[0],s[v++]=c[1]}n.setLayout("linesPoints",s)}else for(var h=i.start;h<i.end;h++){var d=n.getItemModel(h),f=a.getLineCoords(h,o),g=[];if(t)for(var y=0;y<f;y++)g.push(e.dataToPoint(o[y]));else{g[0]=e.dataToPoint(o[0]),g[1]=e.dataToPoint(o[1]);var m=d.get(["lineStyle","curveness"]);+m&&(g[2]=[(g[0][0]+g[1][0])/2-(g[0][1]-g[1][1])*m,(g[0][1]+g[1][1])/2-(g[1][0]-g[0][0])*m])}n.setItemLayout(h,g)}}}}}},Km=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._updateLineDraw(n,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=i.getZr(),h=u.painter.getType()==="svg";h||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!h&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(h||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(n);var v=t.get("clip",!0)&&Ua(t.coordinateSystem,!1,t);v?this.group.setClipPath(v):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,i){var n=t.getData(),o=this._updateLineDraw(n,t);o.incrementalPrepareUpdate(n),this._clearLayer(i),this._finished=!1},e.prototype.incrementalRender=function(t,r,i){this._lineDraw.incrementalUpdate(t,r.getData()),this._finished=t.end===r.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,r,i){var n=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=hv.reset(t,r,i);s.progress&&s.progress({start:0,end:n.count(),count:n.count()},n),this._lineDraw.updateLayout(),this._clearLayer(i)},e.prototype._updateLineDraw=function(t,r){var i=this._lineDraw,n=this._showEffect(r),o=!!r.get("polyline"),s=r.pipelineContext,l=s.large;return(!i||n!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(i&&i.remove(),i=this._lineDraw=l?new Xm:new no(o?n?Um:uv:n?lv:io),this._hasEffet=n,this._isPolyline=o,this._isLargeDraw=l),this.group.add(i.group),i},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var r=t.getZr(),i=r.painter.getType()==="svg";!i&&this._lastZlevel!=null&&r.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,r){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(r)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.type="lines",e}(_t),qm=typeof Uint32Array>"u"?Array:Uint32Array,jm=typeof Float64Array>"u"?Array:Float64Array;function il(a){var e=a.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(a.data=W(e,function(t){var r=[t[0].coord,t[1].coord],i={coords:r};return t[0].name&&(i.fromName=t[0].name),t[1].name&&(i.toName=t[1].name),Rn([i,t[0],t[1]])}))}var Jm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],il(t);var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count)),a.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if(il(t),t.data){var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count))}a.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var r=this._processFlatCoordsArray(t.data);r.flatCoords&&(this._flatCoords?(this._flatCoords=pa(this._flatCoords,r.flatCoords),this._flatCoordsOffset=pa(this._flatCoordsOffset,r.flatCoordsOffset)):(this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset),t.data=new Float32Array(r.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var r=this.getData().getItemModel(t),i=r.option instanceof Array?r.option:r.getShallow("coords");return i},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,r){if(this._flatCoordsOffset){for(var i=this._flatCoordsOffset[t*2],n=this._flatCoordsOffset[t*2+1],o=0;o<n;o++)r[o]=r[o]||[],r[o][0]=this._flatCoords[i+o*2],r[o][1]=this._flatCoords[i+o*2+1];return n}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)r[o]=r[o]||[],r[o][0]=s[o][0],r[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var r=0;if(this._flatCoords&&(r=this._flatCoords.length),he(t[0])){for(var i=t.length,n=new qm(i),o=new jm(i),s=0,l=0,u=0,h=0;h<i;){u++;var v=t[h++];n[l++]=s+r,n[l++]=v;for(var c=0;c<v;c++){var f=t[h++],p=t[h++];o[s++]=f,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(n.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,r){var i=new kt(["value"],this);return i.hasItemOption=!1,i.initData(t.data,[],function(n,o,s,l){if(n instanceof Array)return NaN;i.hasItemOption=!0;var u=n.value;if(u!=null)return u instanceof Array?u[l]:u}),i},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=n.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),h=[];return l!=null&&h.push(l),u!=null&&h.push(u),Zt("nameValue",{name:h.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?1e4:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?2e4:this.get("progressiveThreshold"))},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),r=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&r>0?r+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(wt);function Jr(a){return a instanceof Array||(a=[a,a]),a}var Qm={seriesType:"lines",reset:function(a){var e=Jr(a.get("symbol")),t=Jr(a.get("symbolSize")),r=a.getData();r.setVisual("fromSymbol",e&&e[0]),r.setVisual("toSymbol",e&&e[1]),r.setVisual("fromSymbolSize",t&&t[0]),r.setVisual("toSymbolSize",t&&t[1]);function i(n,o){var s=n.getItemModel(o),l=Jr(s.getShallow("symbol",!0)),u=Jr(s.getShallow("symbolSize",!0));l[0]&&n.setItemVisual(o,"fromSymbol",l[0]),l[1]&&n.setItemVisual(o,"toSymbol",l[1]),u[0]&&n.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&n.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:r.hasItemOption?i:null}}};function t0(a){a.registerChartView(Km),a.registerSeriesModel(Jm),a.registerLayout(hv),a.registerVisual(Qm)}var e0=256,r0=function(){function a(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=Fo.createCanvas();this.canvas=e}return a.prototype.update=function(e,t,r,i,n,o){var s=this._getBrush(),l=this._getGradient(n,"inRange"),u=this._getGradient(n,"outOfRange"),h=this.pointSize+this.blurSize,v=this.canvas,c=v.getContext("2d"),f=e.length;v.width=t,v.height=r;for(var p=0;p<f;++p){var d=e[p],g=d[0],y=d[1],m=d[2],S=i(m);c.globalAlpha=S,c.drawImage(s,g-h,y-h)}if(!v.width||!v.height)return v;for(var x=c.getImageData(0,0,v.width,v.height),b=x.data,_=0,w=b.length,T=this.minOpacity,L=this.maxOpacity,A=L-T;_<w;){var S=b[_+3]/256,C=Math.floor(S*(e0-1))*4;if(S>0){var I=o(S)?l:u;S>0&&(S=S*A+T),b[_++]=I[C],b[_++]=I[C+1],b[_++]=I[C+2],b[_++]=I[C+3]*S*256}else _+=4}return c.putImageData(x,0,0),v},a.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=Fo.createCanvas()),t=this.pointSize+this.blurSize,r=t*2;e.width=r,e.height=r;var i=e.getContext("2d");return i.clearRect(0,0,r,r),i.shadowOffsetX=r,i.shadowBlur=this.blurSize,i.shadowColor="#000",i.beginPath(),i.arc(-t,t,this.pointSize,0,Math.PI*2,!0),i.closePath(),i.fill(),e},a.prototype._getGradient=function(e,t){for(var r=this._gradientPixels,i=r[t]||(r[t]=new Uint8ClampedArray(256*4)),n=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,n),i[o++]=n[0],i[o++]=n[1],i[o++]=n[2],i[o++]=n[3];return i},a}();function a0(a,e,t){var r=a[1]-a[0];e=W(e,function(o){return{interval:[(o.interval[0]-a[0])/r,(o.interval[1]-a[0])/r]}});var i=e.length,n=0;return function(o){var s;for(s=n;s<i;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){n=s;break}}if(s===i)for(s=n-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){n=s;break}}return s>=0&&s<i&&t[s]}}function i0(a,e){var t=a[1]-a[0];return e=[(e[0]-a[0])/t,(e[1]-a[0])/t],function(r){return r>=e[0]&&r<=e[1]}}function nl(a){var e=a.dimensions;return e[0]==="lng"&&e[1]==="lat"}var n0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n;r.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(n=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,i,0,t.getData().count()):nl(o)&&this._renderOnGeo(o,t,n,i)},e.prototype.incrementalPrepareRender=function(t,r,i){this.group.removeAll()},e.prototype.incrementalRender=function(t,r,i,n){var o=r.coordinateSystem;o&&(nl(o)?this.render(r,i,n):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(r,n,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){Wa(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,r,i,n,o){var s=t.coordinateSystem,l=Ya(s,"cartesian2d"),u,h,v,c;if(l){var f=s.getAxis("x"),p=s.getAxis("y");u=f.getBandWidth()+.5,h=p.getBandWidth()+.5,v=f.scale.getExtent(),c=p.scale.getExtent()}for(var d=this.group,g=t.getData(),y=t.getModel(["emphasis","itemStyle"]).getItemStyle(),m=t.getModel(["blur","itemStyle"]).getItemStyle(),S=t.getModel(["select","itemStyle"]).getItemStyle(),x=t.get(["itemStyle","borderRadius"]),b=Nt(t),_=t.getModel("emphasis"),w=_.get("focus"),T=_.get("blurScope"),L=_.get("disabled"),A=l?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],C=i;C<n;C++){var I=void 0,M=g.getItemVisual(C,"style");if(l){var P=g.get(A[0],C),R=g.get(A[1],C);if(isNaN(g.get(A[2],C))||isNaN(P)||isNaN(R)||P<v[0]||P>v[1]||R<c[0]||R>c[1])continue;var V=s.dataToPoint([P,R]);I=new gt({shape:{x:V[0]-u/2,y:V[1]-h/2,width:u,height:h},style:M})}else{if(isNaN(g.get(A[1],C)))continue;I=new gt({z2:1,shape:s.dataToRect([g.get(A[0],C)]).contentShape,style:M})}if(g.hasItemOption){var k=g.getItemModel(C),N=k.getModel("emphasis");y=N.getModel("itemStyle").getItemStyle(),m=k.getModel(["blur","itemStyle"]).getItemStyle(),S=k.getModel(["select","itemStyle"]).getItemStyle(),x=k.get(["itemStyle","borderRadius"]),w=N.get("focus"),T=N.get("blurScope"),L=N.get("disabled"),b=Nt(k)}I.shape.r=x;var G=t.getRawValue(C),Z="-";G&&G[2]!=null&&(Z=G[2]+""),jt(I,b,{labelFetcher:t,labelDataIndex:C,defaultOpacity:M.opacity,defaultText:Z}),I.ensureState("emphasis").style=y,I.ensureState("blur").style=m,I.ensureState("select").style=S,St(I,w,T,L),I.incremental=o,o&&(I.states.emphasis.hoverLayer=!0),d.add(I),g.setItemGraphicEl(C,I),this._progressiveEls&&this._progressiveEls.push(I)}},e.prototype._renderOnGeo=function(t,r,i,n){var o=i.targetVisuals.inRange,s=i.targetVisuals.outOfRange,l=r.getData(),u=this._hmLayer||this._hmLayer||new r0;u.blurSize=r.get("blurSize"),u.pointSize=r.get("pointSize"),u.minOpacity=r.get("minOpacity"),u.maxOpacity=r.get("maxOpacity");var h=t.getViewRect().clone(),v=t.getRoamTransform();h.applyTransform(v);var c=Math.max(h.x,0),f=Math.max(h.y,0),p=Math.min(h.width+h.x,n.getWidth()),d=Math.min(h.height+h.y,n.getHeight()),g=p-c,y=d-f,m=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],S=l.mapArray(m,function(w,T,L){var A=t.dataToPoint([w,T]);return A[0]-=c,A[1]-=f,A.push(L),A}),x=i.getExtent(),b=i.type==="visualMap.continuous"?i0(x,i.option.range):a0(x,i.getPieceList(),i.option.selected);u.update(S,g,y,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},b);var _=new pe({style:{width:g,height:y,x:c,y:f,image:u.canvas},silent:!0});this.group.add(_)},e.type="heatmap",e}(_t),o0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return Qe(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=ju.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(wt);function s0(a){a.registerChartView(n0),a.registerSeriesModel(o0)}var l0=["itemStyle","borderWidth"],ol=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],Ei=new za,u0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),h=u.isHorizontal(),v=l.master.getRect(),c={ecSize:{width:i.getWidth(),height:i.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[v.x,v.x+v.width],[v.y,v.y+v.height]],isHorizontal:h,valueDim:ol[+h],categoryDim:ol[1-+h]};o.diff(s).add(function(p){if(o.hasValue(p)){var d=ll(o,p),g=sl(o,p,d,c),y=ul(o,c,g);o.setItemGraphicEl(p,y),n.add(y),vl(y,c,g)}}).update(function(p,d){var g=s.getItemGraphicEl(d);if(!o.hasValue(p)){n.remove(g);return}var y=ll(o,p),m=sl(o,p,y,c),S=gv(o,m);g&&S!==g.__pictorialShapeStr&&(n.remove(g),o.setItemGraphicEl(p,null),g=null),g?g0(g,c,m):g=ul(o,c,m,!0),o.setItemGraphicEl(p,g),g.__pictorialSymbolMeta=m,n.add(g),vl(g,c,m)}).remove(function(p){var d=s.getItemGraphicEl(p);d&&hl(s,p,d.__pictorialSymbolMeta.animationModel,d)}).execute();var f=t.get("clip",!0)?Ua(t.coordinateSystem,!1,t):null;return f?n.setClipPath(f):n.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,r){var i=this.group,n=this._data;t.get("animation")?n&&n.eachItemGraphicEl(function(o){hl(n,nt(o).dataIndex,t,o)}):i.removeAll()},e.type="pictorialBar",e}(_t);function sl(a,e,t,r){var i=a.getItemLayout(e),n=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,h=t.get("symbolPatternSize")||2,v=t.isAnimationEnabled(),c={dataIndex:e,layout:i,itemModel:t,symbolType:a.getItemVisual(e,"symbol")||"circle",style:a.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:n,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:h,rotation:u,animationModel:v?t:null,hoverScale:v&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};h0(t,n,i,r,c),v0(a,e,i,n,o,c.boundingLength,c.pxSign,h,r,c),c0(t,c.symbolScale,u,r,c);var f=c.symbolSize,p=zr(t.get("symbolOffset"),f);return f0(t,f,i,n,o,p,s,c.valueLineWidth,c.boundingLength,c.repeatCutLength,r,c),c}function h0(a,e,t,r,i){var n=r.valueDim,o=a.get("symbolBoundingData"),s=r.coordSys.getOtherAxis(r.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[n.wh]<=0),h;if(H(o)){var v=[Vi(s,o[0])-l,Vi(s,o[1])-l];v[1]<v[0]&&v.reverse(),h=v[u]}else o!=null?h=Vi(s,o)-l:e?h=r.coordSysExtent[n.index][u]-l:h=t[n.wh];i.boundingLength=h,e&&(i.repeatCutLength=t[n.wh]);var c=n.xy==="x",f=s.inverse;i.pxSign=c&&!f||!c&&f?h>=0?1:-1:h>0?1:-1}function Vi(a,e){return a.toGlobalCoord(a.dataToCoord(a.scale.parse(e)))}function v0(a,e,t,r,i,n,o,s,l,u){var h=l.valueDim,v=l.categoryDim,c=Math.abs(t[v.wh]),f=a.getItemVisual(e,"symbolSize"),p;H(f)?p=f.slice():f==null?p=["100%","100%"]:p=[f,f],p[v.index]=O(p[v.index],c),p[h.index]=O(p[h.index],r?c:Math.abs(n)),u.symbolSize=p;var d=u.symbolScale=[p[0]/s,p[1]/s];d[h.index]*=(l.isHorizontal?-1:1)*o}function c0(a,e,t,r,i){var n=a.get(l0)||0;n&&(Ei.attr({scaleX:e[0],scaleY:e[1],rotation:t}),Ei.updateTransform(),n/=Ei.getLineScale(),n*=e[r.valueDim.index]),i.valueLineWidth=n||0}function f0(a,e,t,r,i,n,o,s,l,u,h,v){var c=h.categoryDim,f=h.valueDim,p=v.pxSign,d=Math.max(e[f.index]+s,0),g=d;if(r){var y=Math.abs(l),m=Vt(a.get("symbolMargin"),"15%")+"",S=!1;m.lastIndexOf("!")===m.length-1&&(S=!0,m=m.slice(0,m.length-1));var x=O(m,e[f.index]),b=Math.max(d+x*2,0),_=S?0:x*2,w=Yc(r),T=w?r:cl((y+_)/b),L=y-T*d;x=L/2/(S?T:Math.max(T-1,1)),b=d+x*2,_=S?0:x*2,!w&&r!=="fixed"&&(T=u?cl((Math.abs(u)+_)/b):0),g=T*b-_,v.repeatTimes=T,v.symbolMargin=x}var A=p*(g/2),C=v.pathPosition=[];C[c.index]=t[c.wh]/2,C[f.index]=o==="start"?A:o==="end"?l-A:l/2,n&&(C[0]+=n[0],C[1]+=n[1]);var I=v.bundlePosition=[];I[c.index]=t[c.xy],I[f.index]=t[f.xy];var M=v.barRectShape=F({},t);M[f.wh]=p*Math.max(Math.abs(t[f.wh]),Math.abs(C[f.index]+A)),M[c.wh]=t[c.wh];var P=v.clipShape={};P[c.xy]=-t[c.xy],P[c.wh]=h.ecSize[c.wh],P[f.xy]=0,P[f.wh]=t[f.wh]}function vv(a){var e=a.symbolPatternSize,t=Pt(a.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function cv(a,e,t,r){var i=a.__pictorialBundle,n=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,h=0,v=n[e.valueDim.index]+o+t.symbolMargin*2;for(lo(a,function(d){d.__pictorialAnimationIndex=h,d.__pictorialRepeatTimes=u,h<u?Xe(d,null,p(h),t,r):Xe(d,null,{scaleX:0,scaleY:0},t,r,function(){i.remove(d)}),h++});h<u;h++){var c=vv(t);c.__pictorialAnimationIndex=h,c.__pictorialRepeatTimes=u,i.add(c);var f=p(h);Xe(c,{x:f.x,y:f.y,scaleX:0,scaleY:0},{scaleX:f.scaleX,scaleY:f.scaleY,rotation:f.rotation},t,r)}function p(d){var g=s.slice(),y=t.pxSign,m=d;return(t.symbolRepeatDirection==="start"?y>0:y<0)&&(m=u-1-d),g[l.index]=v*(m-u/2+.5)+s[l.index],{x:g[0],y:g[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function fv(a,e,t,r){var i=a.__pictorialBundle,n=a.__pictorialMainPath;n?Xe(n,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,r):(n=a.__pictorialMainPath=vv(t),i.add(n),Xe(n,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,r))}function pv(a,e,t){var r=F({},e.barRectShape),i=a.__pictorialBarRect;i?Xe(i,null,{shape:r},e,t):(i=a.__pictorialBarRect=new gt({z2:2,shape:r,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),i.disableMorphing=!0,a.add(i))}function dv(a,e,t,r){if(t.symbolClip){var i=a.__pictorialClipPath,n=F({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(i)mt(i,{shape:n},s,l);else{n[o.wh]=0,i=new gt({shape:n}),a.__pictorialBundle.setClipPath(i),a.__pictorialClipPath=i;var u={};u[o.wh]=t.clipShape[o.wh],Gr[r?"updateProps":"initProps"](i,{shape:u},s,l)}}}function ll(a,e){var t=a.getItemModel(e);return t.getAnimationDelayParams=p0,t.isAnimationEnabled=d0,t}function p0(a){return{index:a.__pictorialAnimationIndex,count:a.__pictorialRepeatTimes}}function d0(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function ul(a,e,t,r){var i=new $,n=new $;return i.add(n),i.__pictorialBundle=n,n.x=t.bundlePosition[0],n.y=t.bundlePosition[1],t.symbolRepeat?cv(i,e,t):fv(i,e,t),pv(i,t,r),dv(i,e,t,r),i.__pictorialShapeStr=gv(a,t),i.__pictorialSymbolMeta=t,i}function g0(a,e,t){var r=t.animationModel,i=t.dataIndex,n=a.__pictorialBundle;mt(n,{x:t.bundlePosition[0],y:t.bundlePosition[1]},r,i),t.symbolRepeat?cv(a,e,t,!0):fv(a,e,t,!0),pv(a,t,!0),dv(a,e,t,!0)}function hl(a,e,t,r){var i=r.__pictorialBarRect;i&&i.removeTextContent();var n=[];lo(r,function(o){n.push(o)}),r.__pictorialMainPath&&n.push(r.__pictorialMainPath),r.__pictorialClipPath&&(t=null),D(n,function(o){da(o,{scaleX:0,scaleY:0},t,e,function(){r.parent&&r.parent.remove(r)})}),a.setItemGraphicEl(e,null)}function gv(a,e){return[a.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function lo(a,e,t){D(a.__pictorialBundle.children(),function(r){r!==a.__pictorialBarRect&&e.call(t,r)})}function Xe(a,e,t,r,i,n){e&&a.attr(e),r.symbolClip&&!i?t&&a.attr(t):t&&Gr[i?"updateProps":"initProps"](a,t,r.animationModel,r.dataIndex,n)}function vl(a,e,t){var r=t.dataIndex,i=t.itemModel,n=i.getModel("emphasis"),o=n.getModel("itemStyle").getItemStyle(),s=i.getModel(["blur","itemStyle"]).getItemStyle(),l=i.getModel(["select","itemStyle"]).getItemStyle(),u=i.getShallow("cursor"),h=n.get("focus"),v=n.get("blurScope"),c=n.get("scale");lo(a,function(d){if(d instanceof pe){var g=d.style;d.useStyle(F({image:g.image,x:g.x,y:g.y,width:g.width,height:g.height},t.style))}else d.useStyle(t.style);var y=d.ensureState("emphasis");y.style=o,c&&(y.scaleX=d.scaleX*1.1,y.scaleY=d.scaleY*1.1),d.ensureState("blur").style=s,d.ensureState("select").style=l,u&&(d.cursor=u),d.z2=t.z2});var f=e.valueDim.posDesc[+(t.boundingLength>0)],p=a.__pictorialBarRect;p.ignoreClip=!0,jt(p,Nt(i),{labelFetcher:e.seriesModel,labelDataIndex:r,defaultText:on(e.seriesModel.getData(),r),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:f}),St(a,h,v,n.get("disabled"))}function cl(a){var e=Math.round(a);return Math.abs(a-e)<1e-4?e:Math.ceil(a)}var y0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,a.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=ar(Ho.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(Ho);function m0(a){a.registerChartView(u0),a.registerSeriesModel(y0),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,it($c,"pictorialBar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Xc("pictorialBar"))}var S0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=n.getLayout("layoutInfo"),h=u.rect,v=u.boundaryGap;s.x=0,s.y=h.y+v[0];function c(g){return g.name}var f=new Fa(this._layersSeries||[],l,c,c),p=[];f.add(B(d,this,"add")).update(B(d,this,"update")).remove(B(d,this,"remove")).execute();function d(g,y,m){var S=o._layers;if(g==="remove"){s.remove(S[y]);return}for(var x=[],b=[],_,w=l[y].indices,T=0;T<w.length;T++){var L=n.getItemLayout(w[T]),A=L.x,C=L.y0,I=L.y;x.push(A,C),b.push(A,C+I),_=n.getItemVisual(w[T],"style")}var M,P=n.getItemLayout(w[0]),R=t.getModel("label"),V=R.get("margin"),k=t.getModel("emphasis");if(g==="add"){var N=p[y]=new $;M=new Kc({shape:{points:x,stackedOnPoints:b,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),N.add(M),s.add(N),t.isAnimationEnabled()&&M.setClipPath(x0(M.getBoundingRect(),t,function(){M.removeClipPath()}))}else{var N=S[m];M=N.childAt(0),s.add(N),p[y]=N,mt(M,{shape:{points:x,stackedOnPoints:b}},t),rr(M)}jt(M,Nt(t),{labelDataIndex:w[T-1],defaultText:n.getName(w[T-1]),inheritColor:_.fill},{normal:{verticalAlign:"middle"}}),M.setTextConfig({position:null,local:!0});var G=M.getTextContent();G&&(G.x=P.x-V,G.y=P.y0+P.y/2),M.useStyle(_),n.setItemGraphicEl(y,M),Qt(M,t),St(M,k.get("focus"),k.get("blurScope"),k.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(_t);function x0(a,e,t){var r=new gt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return ve(r,{shape:{x:a.x-50,width:a.width+100,height:a.height+20}},e,t),r}var ki=2,b0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new On(B(this.getData,this),B(this.getRawData,this))},e.prototype.fixData=function(t){var r=t.length,i={},n=an(t,function(c){return i.hasOwnProperty(c[0]+"")||(i[c[0]+""]=-1),c[2]}),o=[];n.buckets.each(function(c,f){o.push({name:f,dataList:c})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,h=0;h<o[l].dataList.length;++h){var v=o[l].dataList[h][0]+"";i[v]=l}for(var v in i)i.hasOwnProperty(v)&&i[v]!==l&&(i[v]=l,t[r]=[v,0,u],r++)}return t},e.prototype.getInitialData=function(t,r){for(var i=this.getReferringComponents("singleAxis",er).models[0],n=i.get("type"),o=Gt(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=rt(),h=0,v=0;v<s.length;++v)l.push(s[v][ki]),u.get(s[v][ki])||(u.set(s[v][ki],h),h++);var c=En(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:nn(n)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,f=new kt(c,this);return f.initData(s),f},e.prototype.getLayerSeries=function(){for(var t=this.getData(),r=t.count(),i=[],n=0;n<r;++n)i[n]=n;var o=t.mapDimension("single"),s=an(i,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,h){u.sort(function(v,c){return t.get(o,v)-t.get(o,c)}),l.push({name:h,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,r,i){H(t)||(t=t?[t]:[]);for(var n=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,h=0;h<l;++h){for(var v=Number.MAX_VALUE,c=-1,f=o[h].indices.length,p=0;p<f;++p){var d=n.get(t[0],o[h].indices[p]),g=Math.abs(d-r);g<=v&&(u=d,v=g,c=o[h].indices[p])}s.push(c)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=n.getName(t),s=n.get(n.mapDimension("value"),t);return Zt("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(wt);function _0(a,e){a.eachSeriesByType("themeRiver",function(t){var r=t.getData(),i=t.coordinateSystem,n={},o=i.getRect();n.rect=o;var s=t.get("boundaryGap"),l=i.getAxis();if(n.boundaryGap=s,l.orient==="horizontal"){s[0]=O(s[0],o.height),s[1]=O(s[1],o.height);var u=o.height-s[0]-s[1];fl(r,t,u)}else{s[0]=O(s[0],o.width),s[1]=O(s[1],o.width);var h=o.width-s[0]-s[1];fl(r,t,h)}r.setLayout("layoutInfo",n)})}function fl(a,e,t){if(a.count())for(var r=e.coordinateSystem,i=e.getLayerSeries(),n=a.mapDimension("single"),o=a.mapDimension("value"),s=W(i,function(g){return W(g.indices,function(y){var m=r.dataToPoint(a.get(n,y));return m[1]=a.get(o,y),m})}),l=w0(s),u=l.y0,h=t/l.max,v=i.length,c=i[0].indices.length,f,p=0;p<c;++p){f=u[p]*h,a.setItemLayout(i[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:f,y:s[0][p][1]*h});for(var d=1;d<v;++d)f+=s[d-1][p][1]*h,a.setItemLayout(i[d].indices[p],{layerIndex:d,x:s[d][p][0],y0:f,y:s[d][p][1]*h})}}function w0(a){for(var e=a.length,t=a[0].length,r=[],i=[],n=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=a[l][o][1];s>n&&(n=s),r.push(s)}for(var u=0;u<t;++u)i[u]=(n-r[u])/2;n=0;for(var h=0;h<t;++h){var v=r[h]+i[h];v>n&&(n=v)}return{y0:i,max:n}}function A0(a){a.registerChartView(S0),a.registerSeriesModel(b0),a.registerLayout(_0),a.registerProcessor(Gn("themeRiver"))}var T0=2,D0=4,pl=function(a){E(e,a);function e(t,r,i,n){var o=a.call(this)||this;o.z2=T0,o.textConfig={inside:!0},nt(o).seriesIndex=r.seriesIndex;var s=new ut({z2:D0,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,r,i,n),o}return e.prototype.updateData=function(t,r,i,n,o){this.node=r,r.piece=this,i=i||this._seriesModel,n=n||this._ecModel;var s=this;nt(s).dataIndex=r.dataIndex;var l=r.getModel(),u=l.getModel("emphasis"),h=r.getLayout(),v=F({},h);v.label=null;var c=r.getVisual("style");c.lineJoin="bevel";var f=r.getVisual("decal");f&&(c.decal=Pn(f,o));var p=Wo(l.getModel("itemStyle"),v,!0);F(v,p),D(rn,function(m){var S=s.ensureState(m),x=l.getModel([m,"itemStyle"]);S.style=x.getItemStyle();var b=Wo(x,v);b&&(S.shape=b)}),t?(s.setShape(v),s.shape.r=h.r0,ve(s,{shape:{r:h.r}},i,r.dataIndex)):(mt(s,{shape:v},i),rr(s)),s.useStyle(c),this._updateLabel(i);var d=l.getShallow("cursor");d&&s.attr("cursor",d),this._seriesModel=i||this._seriesModel,this._ecModel=n||this._ecModel;var g=u.get("focus"),y=g==="relative"?pa(r.getAncestorsIndices(),r.getDescendantIndices()):g==="ancestor"?r.getAncestorsIndices():g==="descendant"?r.getDescendantIndices():g;St(this,y,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var r=this,i=this.node.getModel(),n=i.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),h=Math.sin(l),v=this,c=v.getTextContent(),f=this.node.dataIndex,p=n.get("minAngle")/180*Math.PI,d=n.get("show")&&!(p!=null&&Math.abs(s)<p);c.ignore=!d,D(jc,function(y){var m=y==="normal"?i.getModel("label"):i.getModel([y,"label"]),S=y==="normal",x=S?c:c.ensureState(y),b=t.getFormattedLabel(f,y);S&&(b=b||r.node.name),x.style=ct(m,{},null,y!=="normal",!0),b&&(x.style.text=b);var _=m.get("show");_!=null&&!S&&(x.ignore=!_);var w=g(m,"position"),T=S?v:v.states[y],L=T.style.fill;T.textConfig={outsideFill:m.get("color")==="inherit"?L:null,inside:w!=="outside"};var A,C=g(m,"distance")||0,I=g(m,"align"),M=g(m,"rotate"),P=Math.PI*.5,R=Math.PI*1.5,V=Zr(M==="tangential"?Math.PI/2-l:l),k=V>P&&!qc(V-P)&&V<R;w==="outside"?(A=o.r+C,I=k?"right":"left"):!I||I==="center"?(s===2*Math.PI&&o.r0===0?A=0:A=(o.r+o.r0)/2,I="center"):I==="left"?(A=o.r0+C,I=k?"right":"left"):I==="right"&&(A=o.r-C,I=k?"left":"right"),x.style.align=I,x.style.verticalAlign=g(m,"verticalAlign")||"middle",x.x=A*u+o.cx,x.y=A*h+o.cy;var N=0;M==="radial"?N=Zr(-l)+(k?Math.PI:0):M==="tangential"?N=Zr(Math.PI/2-l)+(k?Math.PI:0):he(M)&&(N=M*Math.PI/180),x.rotation=Zr(N)});function g(y,m){var S=y.get(m);return S??n.get(m)}c.dirtyStyle()},e}(wr),mn="sunburstRootToNode",dl="sunburstHighlight",L0="sunburstUnhighlight";function C0(a){a.registerAction({type:mn,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},r);function r(i,n){var o=Dr(e,[mn],i);if(o){var s=i.getViewRoot();s&&(e.direction=Qn(s,o.node)?"rollUp":"drillDown"),i.resetViewRoot(o.node)}}}),a.registerAction({type:dl,update:"none"},function(e,t,r){e=F({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},i);function i(n){var o=Dr(e,[dl],n);o&&(e.dataIndex=o.node.dataIndex)}r.dispatchAction(F(e,{type:"highlight"}))}),a.registerAction({type:L0,update:"updateView"},function(e,t,r){e=F({},e),r.dispatchAction(F(e,{type:"downplay"}))})}var I0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){var o=this;this.seriesModel=t,this.api=i,this.ecModel=r;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),h=this.group,v=t.get("renderLabelForZeroData"),c=[];u.eachNode(function(m){c.push(m)});var f=this._oldChildren||[];p(c,f),y(l,u),this._initEvents(),this._oldChildren=c;function p(m,S){if(m.length===0&&S.length===0)return;new Fa(S,m,x,x).add(b).update(b).remove(it(b,null)).execute();function x(_){return _.getId()}function b(_,w){var T=_==null?null:m[_],L=w==null?null:S[w];d(T,L)}}function d(m,S){if(!v&&m&&!m.getValue()&&(m=null),m!==l&&S!==l){if(S&&S.piece)m?(S.piece.updateData(!1,m,t,r,i),s.setItemGraphicEl(m.dataIndex,S.piece)):g(S);else if(m){var x=new pl(m,t,r,i);h.add(x),s.setItemGraphicEl(m.dataIndex,x)}}}function g(m){m&&m.piece&&(h.remove(m.piece),m.piece=null)}function y(m,S){S.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,m,t,r,i):(o.virtualPiece=new pl(m,t,r,i),h.add(o.virtualPiece)),S.piece.off("click"),o.virtualPiece.on("click",function(x){o._rootToNode(S.parentNode)})):o.virtualPiece&&(h.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(r){var i=!1,n=t.seriesModel.getViewRoot();n.eachNode(function(o){if(!i&&o.piece&&o.piece===r.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var h=l.get("target",!0)||"_blank";Xu(u,h)}}i=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:mn,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,r){var i=r.getData(),n=i.getItemLayout(0);if(n){var o=t[0]-n.cx,s=t[1]-n.cy,l=Math.sqrt(o*o+s*s);return l<=n.r&&l>=n.r0}},e.type="sunburst",e}(_t),M0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,r){var i={name:t.name,children:t.data};yv(i);var n=this._levelModels=W(t.levels||[],function(l){return new Jt(l,this,r)},this),o=Jn.createTree(i,this,s);function s(l){l.wrapMethod("getItemModel",function(u,h){var v=o.getNodeByDataIndex(h),c=n[v.depth];return c&&(u.parentModel=c),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(t);return r.treePathInfo=qa(i,this),r},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){Nh(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(wt);function yv(a){var e=0;D(a.children,function(r){yv(r);var i=r.value;H(i)&&(i=i[0]),e+=i});var t=a.value;H(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),H(a.value)?a.value[0]=t:a.value=t}var gl=Math.PI/180;function P0(a,e,t){e.eachSeriesByType(a,function(r){var i=r.get("center"),n=r.get("radius");H(n)||(n=[0,n]),H(i)||(i=[i,i]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=O(i[0],o),h=O(i[1],s),v=O(n[0],l/2),c=O(n[1],l/2),f=-r.get("startAngle")*gl,p=r.get("minAngle")*gl,d=r.getData().tree.root,g=r.getViewRoot(),y=g.depth,m=r.get("sort");m!=null&&mv(g,m);var S=0;D(g.children,function(V){!isNaN(V.getValue())&&S++});var x=g.getValue(),b=Math.PI/(x||S)*2,_=g.depth>0,w=g.height-(_?-1:1),T=(c-v)/(w||1),L=r.get("clockwise"),A=r.get("stillShowZeroSum"),C=L?1:-1,I=function(V,k){if(V){var N=k;if(V!==d){var G=V.getValue(),Z=x===0&&A?b:G*b;Z<p&&(Z=p),N=k+C*Z;var U=V.depth-y-(_?-1:1),K=v+T*U,J=v+T*(U+1),q=r.getLevelModel(V);if(q){var tt=q.get("r0",!0),Lt=q.get("r",!0),ge=q.get("radius",!0);ge!=null&&(tt=ge[0],Lt=ge[1]),tt!=null&&(K=O(tt,l/2)),Lt!=null&&(J=O(Lt,l/2))}V.setLayout({angle:Z,startAngle:k,endAngle:N,clockwise:L,cx:u,cy:h,r0:K,r:J})}if(V.children&&V.children.length){var j=0;D(V.children,function(Y){j+=I(Y,k+j)})}return N-k}};if(_){var M=v,P=v+T,R=Math.PI*2;d.setLayout({angle:R,startAngle:f,endAngle:f+R,clockwise:L,cx:u,cy:h,r0:M,r:P})}I(g,f)})}function mv(a,e){var t=a.children||[];a.children=R0(t,e),t.length&&D(a.children,function(r){mv(r,e)})}function R0(a,e){if(st(e)){var t=W(a,function(i,n){var o=i.getValue();return{params:{depth:i.depth,height:i.height,dataIndex:i.dataIndex,getValue:function(){return o}},index:n}});return t.sort(function(i,n){return e(i.params,n.params)}),W(t,function(i){return a[i.index]})}else{var r=e==="asc";return a.sort(function(i,n){var o=(i.getValue()-n.getValue())*(r?1:-1);return o===0?(i.dataIndex-n.dataIndex)*(r?-1:1):o})}}function E0(a){var e={};function t(r,i,n){for(var o=r;o&&o.depth>1;)o=o.parentNode;var s=i.getColorFromPalette(o.name||o.dataIndex+"",e);return r.depth>1&&Q(s)&&(s=Jc(s,(r.depth-1)/(n-1)*.5)),s}a.eachSeriesByType("sunburst",function(r){var i=r.getData(),n=i.tree;n.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,r,n.root.height));var u=i.ensureUniqueItemVisual(o.dataIndex,"style");F(u,l)})})}function V0(a){a.registerChartView(I0),a.registerSeriesModel(M0),a.registerLayout(it(P0,"sunburst")),a.registerProcessor(it(Gn,"sunburst")),a.registerVisual(E0),C0(a)}var yl={color:"fill",borderColor:"stroke"},k0={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},se=Dt(),N0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,r){return Qe(null,this)},e.prototype.getDataParams=function(t,r,i){var n=a.prototype.getDataParams.call(this,t,r);return i&&(n.info=se(i).info),n},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(wt);function z0(a,e){return e=e||[0,0],W(["x","y"],function(t,r){var i=this.getAxis(t),n=e[r],o=a[r]/2;return i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(n-o)-i.dataToCoord(n+o))},this)}function O0(a){var e=a.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:B(z0,a)}}}function G0(a,e){return e=e||[0,0],W([0,1],function(t){var r=e[t],i=a[t]/2,n=[],o=[];return n[t]=r-i,o[t]=r+i,n[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(n)[t]-this.dataToPoint(o)[t])},this)}function B0(a){var e=a.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:a.getZoom()},api:{coord:function(t){return a.dataToPoint(t)},size:B(G0,a)}}}function F0(a,e){var t=this.getAxis(),r=e instanceof Array?e[0]:e,i=(a instanceof Array?a[0]:a)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(r-i)-t.dataToCoord(r+i))}function H0(a){var e=a.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:B(F0,a)}}}function W0(a,e){return e=e||[0,0],W(["Radius","Angle"],function(t,r){var i="get"+t+"Axis",n=this[i](),o=e[r],s=a[r]/2,l=n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(o-s)-n.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function Z0(a){var e=a.getRadiusAxis(),t=a.getAngleAxis(),r=e.getExtent();return r[0]>r[1]&&r.reverse(),{coordSys:{type:"polar",cx:a.cx,cy:a.cy,r:r[1],r0:r[0]},api:{coord:function(i){var n=e.dataToRadius(i[0]),o=t.dataToAngle(i[1]),s=a.coordToPoint([n,o]);return s.push(n,o*Math.PI/180),s},size:B(W0,a)}}}function U0(a){var e=a.getRect(),t=a.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:a.getCellWidth(),cellHeight:a.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(r,i){return a.dataToPoint(r,i)}}}}function Sv(a,e,t,r){return a&&(a.legacy||a.legacy!==!1&&!t&&!r&&e!=="tspan"&&(e==="text"||z(a,"text")))}function xv(a,e,t){var r=a,i,n,o;if(e==="text")o=r;else{o={},z(r,"text")&&(o.text=r.text),z(r,"rich")&&(o.rich=r.rich),z(r,"textFill")&&(o.fill=r.textFill),z(r,"textStroke")&&(o.stroke=r.textStroke),z(r,"fontFamily")&&(o.fontFamily=r.fontFamily),z(r,"fontSize")&&(o.fontSize=r.fontSize),z(r,"fontStyle")&&(o.fontStyle=r.fontStyle),z(r,"fontWeight")&&(o.fontWeight=r.fontWeight),n={type:"text",style:o,silent:!0},i={};var s=z(r,"textPosition");t?i.position=s?r.textPosition:"inside":s&&(i.position=r.textPosition),z(r,"textPosition")&&(i.position=r.textPosition),z(r,"textOffset")&&(i.offset=r.textOffset),z(r,"textRotation")&&(i.rotation=r.textRotation),z(r,"textDistance")&&(i.distance=r.textDistance)}return ml(o,a),D(o.rich,function(l){ml(l,l)}),{textConfig:i,textContent:n}}function ml(a,e){e&&(e.font=e.textFont||e.font,z(e,"textStrokeWidth")&&(a.lineWidth=e.textStrokeWidth),z(e,"textAlign")&&(a.align=e.textAlign),z(e,"textVerticalAlign")&&(a.verticalAlign=e.textVerticalAlign),z(e,"textLineHeight")&&(a.lineHeight=e.textLineHeight),z(e,"textWidth")&&(a.width=e.textWidth),z(e,"textHeight")&&(a.height=e.textHeight),z(e,"textBackgroundColor")&&(a.backgroundColor=e.textBackgroundColor),z(e,"textPadding")&&(a.padding=e.textPadding),z(e,"textBorderColor")&&(a.borderColor=e.textBorderColor),z(e,"textBorderWidth")&&(a.borderWidth=e.textBorderWidth),z(e,"textBorderRadius")&&(a.borderRadius=e.textBorderRadius),z(e,"textBoxShadowColor")&&(a.shadowColor=e.textBoxShadowColor),z(e,"textBoxShadowBlur")&&(a.shadowBlur=e.textBoxShadowBlur),z(e,"textBoxShadowOffsetX")&&(a.shadowOffsetX=e.textBoxShadowOffsetX),z(e,"textBoxShadowOffsetY")&&(a.shadowOffsetY=e.textBoxShadowOffsetY))}function Sl(a,e,t){var r=a;r.textPosition=r.textPosition||t.position||"inside",t.offset!=null&&(r.textOffset=t.offset),t.rotation!=null&&(r.textRotation=t.rotation),t.distance!=null&&(r.textDistance=t.distance);var i=r.textPosition.indexOf("inside")>=0,n=a.fill||"#000";xl(r,e);var o=r.textFill==null;return i?o&&(r.textFill=t.insideFill||"#fff",!r.textStroke&&t.insideStroke&&(r.textStroke=t.insideStroke),!r.textStroke&&(r.textStroke=n),r.textStrokeWidth==null&&(r.textStrokeWidth=2)):(o&&(r.textFill=a.fill||t.outsideFill||"#000"),!r.textStroke&&t.outsideStroke&&(r.textStroke=t.outsideStroke)),r.text=e.text,r.rich=e.rich,D(e.rich,function(s){xl(s,s)}),r}function xl(a,e){e&&(z(e,"fill")&&(a.textFill=e.fill),z(e,"stroke")&&(a.textStroke=e.fill),z(e,"lineWidth")&&(a.textStrokeWidth=e.lineWidth),z(e,"font")&&(a.font=e.font),z(e,"fontStyle")&&(a.fontStyle=e.fontStyle),z(e,"fontWeight")&&(a.fontWeight=e.fontWeight),z(e,"fontSize")&&(a.fontSize=e.fontSize),z(e,"fontFamily")&&(a.fontFamily=e.fontFamily),z(e,"align")&&(a.textAlign=e.align),z(e,"verticalAlign")&&(a.textVerticalAlign=e.verticalAlign),z(e,"lineHeight")&&(a.textLineHeight=e.lineHeight),z(e,"width")&&(a.textWidth=e.width),z(e,"height")&&(a.textHeight=e.height),z(e,"backgroundColor")&&(a.textBackgroundColor=e.backgroundColor),z(e,"padding")&&(a.textPadding=e.padding),z(e,"borderColor")&&(a.textBorderColor=e.borderColor),z(e,"borderWidth")&&(a.textBorderWidth=e.borderWidth),z(e,"borderRadius")&&(a.textBorderRadius=e.borderRadius),z(e,"shadowColor")&&(a.textBoxShadowColor=e.shadowColor),z(e,"shadowBlur")&&(a.textBoxShadowBlur=e.shadowBlur),z(e,"shadowOffsetX")&&(a.textBoxShadowOffsetX=e.shadowOffsetX),z(e,"shadowOffsetY")&&(a.textBoxShadowOffsetY=e.shadowOffsetY),z(e,"textShadowColor")&&(a.textShadowColor=e.textShadowColor),z(e,"textShadowBlur")&&(a.textShadowBlur=e.textShadowBlur),z(e,"textShadowOffsetX")&&(a.textShadowOffsetX=e.textShadowOffsetX),z(e,"textShadowOffsetY")&&(a.textShadowOffsetY=e.textShadowOffsetY))}var bv={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},bl=pt(bv);Wu(Ar,function(a,e){return a[e]=1,a},{});Ar.join(", ");var La=["","style","shape","extra"],Je=Dt();function uo(a,e,t,r,i){var n=a+"Animation",o=rh(a,r,i)||{},s=Je(e).userDuring;return o.duration>0&&(o.during=s?B(q0,{el:e,userDuring:s}):null,o.setToFinal=!0,o.scope=a),F(o,t[n]),o}function ha(a,e,t,r){r=r||{};var i=r.dataIndex,n=r.isInit,o=r.clearStyle,s=t.isAnimationEnabled(),l=Je(a),u=e.style;l.userDuring=e.during;var h={},v={};if(J0(a,e,v),wl("shape",e,v),wl("extra",e,v),!n&&s&&(j0(a,e,h),_l("shape",a,e,h),_l("extra",a,e,h),Q0(a,e,u,h)),v.style=u,Y0(a,v,o),X0(a,e),s)if(n){var c={};D(La,function(p){var d=p?e[p]:e;d&&d.enterFrom&&(p&&(c[p]=c[p]||{}),F(p?c[p]:c,d.enterFrom))});var f=uo("enter",a,e,t,i);f.duration>0&&a.animateFrom(c,f)}else $0(a,e,i||0,t,h);_v(a,e),u?a.dirty():a.markRedraw()}function _v(a,e){for(var t=Je(a).leaveToProps,r=0;r<La.length;r++){var i=La[r],n=i?e[i]:e;n&&n.leaveTo&&(t||(t=Je(a).leaveToProps={}),i&&(t[i]=t[i]||{}),F(i?t[i]:t,n.leaveTo))}}function Ja(a,e,t,r){if(a){var i=a.parent,n=Je(a).leaveToProps;if(n){var o=uo("update",a,e,t,0);o.done=function(){i.remove(a)},a.animateTo(n,o)}else i.remove(a)}}function Ne(a){return a==="all"}function Y0(a,e,t){var r=e.style;if(!a.isGroup&&r){if(t){a.useStyle({});for(var i=a.animators,n=0;n<i.length;n++){var o=i[n];o.targetName==="style"&&o.changeTarget(a.style)}}a.setStyle(r)}e&&(e.style=null,e&&a.attr(e),e.style=r)}function $0(a,e,t,r,i){if(i){var n=uo("update",a,e,r,t);n.duration>0&&a.animateFrom(i,n)}}function X0(a,e){z(e,"silent")&&(a.silent=e.silent),z(e,"ignore")&&(a.ignore=e.ignore),a instanceof Ke&&z(e,"invisible")&&(a.invisible=e.invisible),a instanceof Ut&&z(e,"autoBatch")&&(a.autoBatch=e.autoBatch)}var ee={},K0={setTransform:function(a,e){return ee.el[a]=e,this},getTransform:function(a){return ee.el[a]},setShape:function(a,e){var t=ee.el,r=t.shape||(t.shape={});return r[a]=e,t.dirtyShape&&t.dirtyShape(),this},getShape:function(a){var e=ee.el.shape;if(e)return e[a]},setStyle:function(a,e){var t=ee.el,r=t.style;return r&&(r[a]=e,t.dirtyStyle&&t.dirtyStyle()),this},getStyle:function(a){var e=ee.el.style;if(e)return e[a]},setExtra:function(a,e){var t=ee.el.extra||(ee.el.extra={});return t[a]=e,this},getExtra:function(a){var e=ee.el.extra;if(e)return e[a]}};function q0(){var a=this,e=a.el;if(e){var t=Je(e).userDuring,r=a.userDuring;if(t!==r){a.el=a.userDuring=null;return}ee.el=e,r(K0)}}function _l(a,e,t,r){var i=t[a];if(i){var n=e[a],o;if(n){var s=t.transition,l=i.transition;if(l)if(!o&&(o=r[a]={}),Ne(l))F(o,n);else for(var u=ue(l),h=0;h<u.length;h++){var v=u[h],c=n[v];o[v]=c}else if(Ne(s)||Mt(s,a)>=0){!o&&(o=r[a]={});for(var f=pt(n),h=0;h<f.length;h++){var v=f[h],c=n[v];tS(i[v],c)&&(o[v]=c)}}}}}function wl(a,e,t){var r=e[a];if(r)for(var i=t[a]={},n=pt(r),o=0;o<n.length;o++){var s=n[o];i[s]=Qc(r[s])}}function j0(a,e,t){for(var r=e.transition,i=Ne(r)?Ar:ue(r||[]),n=0;n<i.length;n++){var o=i[n];if(!(o==="style"||o==="shape"||o==="extra")){var s=a[o];t[o]=s}}}function J0(a,e,t){for(var r=0;r<bl.length;r++){var i=bl[r],n=bv[i],o=e[i];o&&(t[n[0]]=o[0],t[n[1]]=o[1])}for(var r=0;r<Ar.length;r++){var s=Ar[r];e[s]!=null&&(t[s]=e[s])}}function Q0(a,e,t,r){if(t){var i=a.style,n;if(i){var o=t.transition,s=e.transition;if(o&&!Ne(o)){var l=ue(o);!n&&(n=r.style={});for(var u=0;u<l.length;u++){var h=l[u],v=i[h];n[h]=v}}else if(a.getAnimationStyleProps&&(Ne(s)||Ne(o)||Mt(s,"style")>=0)){var c=a.getAnimationStyleProps(),f=c?c.style:null;if(f){!n&&(n=r.style={});for(var p=pt(t),u=0;u<p.length;u++){var h=p[u];if(f[h]){var v=i[h];n[h]=v}}}}}}}function tS(a,e){return tf(a)?a!==e:a!=null&&isFinite(a)}var wv=Dt(),eS=["percent","easing","shape","style","extra"];function Av(a){a.stopAnimation("keyframe"),a.attr(wv(a))}function Ca(a,e,t){if(!(!t.isAnimationEnabled()||!e)){if(H(e)){D(e,function(s){Ca(a,s,t)});return}var r=e.keyframes,i=e.duration;if(t&&i==null){var n=rh("enter",t,0);i=n&&n.duration}if(!(!r||!i)){var o=wv(a);D(La,function(s){if(!(s&&!a[s])){var l;r.sort(function(u,h){return u.percent-h.percent}),D(r,function(u){var h=a.animators,v=s?u[s]:u;if(v){var c=pt(v);if(s||(c=Gt(c,function(d){return Mt(eS,d)<0})),!!c.length){l||(l=a.animate(s,e.loop,!0),l.scope="keyframe");for(var f=0;f<h.length;f++)h[f]!==l&&h[f].targetName===l.targetName&&h[f].stopTracks(c);s&&(o[s]=o[s]||{});var p=s?o[s]:o;D(c,function(d){p[d]=((s?a[s]:a)||{})[d]}),l.whenWithKeys(i*u.percent,v,c,u.easing)}}}),l&&l.delay(e.delay||0).duration(i).start(e.easing)}})}}}var le="emphasis",me="normal",ho="blur",vo="select",Ae=[me,le,ho,vo],Ni={normal:["itemStyle"],emphasis:[le,"itemStyle"],blur:[ho,"itemStyle"],select:[vo,"itemStyle"]},zi={normal:["label"],emphasis:[le,"label"],blur:[ho,"label"],select:[vo,"label"]},rS=["x","y"],aS="e\0\0",Ft={normal:{},emphasis:{},blur:{},select:{}},iS={cartesian2d:O0,geo:B0,single:H0,polar:Z0,calendar:U0};function Sn(a){return a instanceof Ut}function xn(a){return a instanceof Ke}function nS(a,e){e.copyTransform(a),xn(e)&&xn(a)&&(e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel,e.invisible=a.invisible,e.ignore=a.ignore,Sn(e)&&Sn(a)&&e.setShape(a.shape))}var oS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=Al(t,s,r,i);o||l.removeAll(),s.diff(o).add(function(v){Oi(i,null,v,u(v,n),t,l,s)}).remove(function(v){var c=o.getItemGraphicEl(v);c&&Ja(c,se(c).option,t)}).update(function(v,c){var f=o.getItemGraphicEl(c);Oi(i,f,v,u(v,n),t,l,s)}).execute();var h=t.get("clip",!0)?Ua(t.coordinateSystem,!1,t):null;h?l.setClipPath(h):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,r,i){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,r,i,n,o){var s=r.getData(),l=Al(r,s,i,n),u=this._progressiveEls=[];function h(f){f.isGroup||(f.incremental=!0,f.ensureState("emphasis").hoverLayer=!0)}for(var v=t.start;v<t.end;v++){var c=Oi(null,null,v,l(v,o),r,this.group,s);c&&(c.traverse(h),u.push(c))}},e.prototype.eachRendered=function(t){Wa(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,r,i,n){var o=r.element;if(o==null||i.name===o)return!0;for(;(i=i.__hostTarget||i.parent)&&i!==this.group;)if(i.name===o)return!0;return!1},e.type="custom",e}(_t);function co(a){var e=a.type,t;if(e==="path"){var r=a.shape,i=r.width!=null&&r.height!=null?{x:r.x||0,y:r.y||0,width:r.width,height:r.height}:null,n=Lv(r);t=af(n,null,i,r.layout||"center"),se(t).customPathData=n}else if(e==="image")t=new pe({}),se(t).customImagePath=a.style.image;else if(e==="text")t=new ut({});else if(e==="group")t=new $;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=ah(e);if(!o){var s="";eh(s)}t=new o}return se(t).customGraphicType=e,t.name=a.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function fo(a,e,t,r,i,n,o){Av(e);var s=i&&i.normal.cfg;s&&e.setTextConfig(s),r&&r.transition==null&&(r.transition=rS);var l=r&&r.style;if(l){if(e.type==="text"){var u=l;z(u,"textFill")&&(u.fill=u.textFill),z(u,"textStroke")&&(u.stroke=u.textStroke)}var h=void 0,v=Sn(e)?l.decal:null;a&&v&&(v.dirty=!0,h=Pn(v,a)),l.__decalPattern=h}if(xn(e)&&l){var h=l.__decalPattern;h&&(l.decal=h)}ha(e,r,n,{dataIndex:t,isInit:o,clearStyle:!0}),Ca(e,r.keyframeAnimation,n)}function Tv(a,e,t,r,i){var n=e.isGroup?null:e,o=i&&i[a].cfg;if(n){var s=n.ensureState(a);if(r===!1){var l=n.getState(a);l&&(l.style=null)}else s.style=r||null;o&&(s.textConfig=o),qe(n)}}function sS(a,e,t){if(!a.isGroup){var r=a,i=t.currentZ,n=t.currentZLevel;r.z=i,r.zlevel=n;var o=e.z2;o!=null&&(r.z2=o||0);for(var s=0;s<Ae.length;s++)lS(r,e,Ae[s])}}function lS(a,e,t){var r=t===me,i=r?e:Ia(e,t),n=i?i.z2:null,o;n!=null&&(o=r?a:a.ensureState(t),o.z2=n||0)}function Al(a,e,t,r){var i=a.get("renderItem"),n=a.coordinateSystem,o={};n&&(o=n.prepareCustoms?n.prepareCustoms(n):iS[n.type](n));for(var s=ft({getWidth:r.getWidth,getHeight:r.getHeight,getZr:r.getZr,getDevicePixelRatio:r.getDevicePixelRatio,value:x,style:_,ordinalRawValue:b,styleEmphasis:w,visual:A,barLayout:C,currentSeriesIndices:I,font:M},o.api||{}),l={context:{},seriesId:a.id,seriesName:a.name,seriesIndex:a.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:uS(a.getData())},u,h,v={},c={},f={},p={},d=0;d<Ae.length;d++){var g=Ae[d];f[g]=a.getModel(Ni[g]),p[g]=a.getModel(zi[g])}function y(P){return P===u?h||(h=e.getItemModel(P)):e.getItemModel(P)}function m(P,R){return e.hasItemOption?P===u?v[R]||(v[R]=y(P).getModel(Ni[R])):y(P).getModel(Ni[R]):f[R]}function S(P,R){return e.hasItemOption?P===u?c[R]||(c[R]=y(P).getModel(zi[R])):y(P).getModel(zi[R]):p[R]}return function(P,R){return u=P,h=null,v={},c={},i&&i(ft({dataIndexInside:P,dataIndex:e.getRawIndex(P),actionType:R?R.type:null},l),s)};function x(P,R){return R==null&&(R=u),e.getStore().get(e.getDimensionIndex(P||0),R)}function b(P,R){R==null&&(R=u),P=P||0;var V=e.getDimensionInfo(P);if(!V){var k=e.getDimensionIndex(P);return k>=0?e.getStore().get(k,R):void 0}var N=e.get(V.name,R),G=V&&V.ordinalMeta;return G?G.categories[N]:N}function _(P,R){R==null&&(R=u);var V=e.getItemVisual(R,"style"),k=V&&V.fill,N=V&&V.opacity,G=m(R,me).getItemStyle();k!=null&&(G.fill=k),N!=null&&(G.opacity=N);var Z={inheritColor:Q(k)?k:"#000"},U=S(R,me),K=ct(U,null,Z,!1,!0);K.text=U.getShallow("show")?Tt(a.getFormattedLabel(R,me),on(e,R)):null;var J=Zo(U,Z,!1);return L(P,G),G=Sl(G,K,J),P&&T(G,P),G.legacy=!0,G}function w(P,R){R==null&&(R=u);var V=m(R,le).getItemStyle(),k=S(R,le),N=ct(k,null,null,!0,!0);N.text=k.getShallow("show")?kr(a.getFormattedLabel(R,le),a.getFormattedLabel(R,me),on(e,R)):null;var G=Zo(k,null,!0);return L(P,V),V=Sl(V,N,G),P&&T(V,P),V.legacy=!0,V}function T(P,R){for(var V in R)z(R,V)&&(P[V]=R[V])}function L(P,R){P&&(P.textFill&&(R.textFill=P.textFill),P.textPosition&&(R.textPosition=P.textPosition))}function A(P,R){if(R==null&&(R=u),z(yl,P)){var V=e.getItemVisual(R,"style");return V?V[yl[P]]:null}if(z(k0,P))return e.getItemVisual(R,P)}function C(P){if(n.type==="cartesian2d"){var R=n.getBaseAxis();return ef(ft({axis:R},P))}}function I(){return t.getCurrentSeriesIndices()}function M(P){return rf(P,t)}}function uS(a){var e={};return D(a.dimensions,function(t){var r=a.getDimensionInfo(t);if(!r.isExtraCoord){var i=r.coordDim,n=e[i]=e[i]||[];n[r.coordDimIndex]=a.getDimensionIndex(t)}}),e}function Oi(a,e,t,r,i,n,o){if(!r){n.remove(e);return}var s=po(a,e,t,r,i,n);return s&&o.setItemGraphicEl(t,s),s&&St(s,r.focus,r.blurScope,r.emphasisDisabled),s}function po(a,e,t,r,i,n){var o=-1,s=e;e&&Dv(e,r,i)&&(o=Mt(n.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=co(r),s&&nS(s,u)),r.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),Ft.normal.cfg=Ft.normal.conOpt=Ft.emphasis.cfg=Ft.emphasis.conOpt=Ft.blur.cfg=Ft.blur.conOpt=Ft.select.cfg=Ft.select.conOpt=null,Ft.isLegacy=!1,vS(u,t,r,i,l,Ft),hS(u,t,r,i,l),fo(a,u,t,r,Ft,i,l),z(r,"info")&&(se(u).info=r.info);for(var h=0;h<Ae.length;h++){var v=Ae[h];if(v!==me){var c=Ia(r,v),f=go(r,c,v);Tv(v,u,c,f,Ft)}}return sS(u,r,i),r.type==="group"&&cS(a,u,t,r,i),o>=0?n.replaceAt(u,o):n.add(u),u}function Dv(a,e,t){var r=se(a),i=e.type,n=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||i!=null&&i!==r.customGraphicType||i==="path"&&gS(n)&&Lv(n)!==r.customPathData||i==="image"&&z(o,"image")&&o.image!==r.customImagePath}function hS(a,e,t,r,i){var n=t.clipPath;if(n===!1)a&&a.getClipPath()&&a.removeClipPath();else if(n){var o=a.getClipPath();o&&Dv(o,n,r)&&(o=null),o||(o=co(n),a.setClipPath(o)),fo(null,o,e,n,null,r,i)}}function vS(a,e,t,r,i,n){if(!a.isGroup){Tl(t,null,n),Tl(t,le,n);var o=n.normal.conOpt,s=n.emphasis.conOpt,l=n.blur.conOpt,u=n.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var h=a.getTextContent();if(o===!1)h&&a.removeTextContent();else{o=n.normal.conOpt=o||{type:"text"},h?h.clearStates():(h=co(o),a.setTextContent(h)),fo(null,h,e,o,null,r,i);for(var v=o&&o.style,c=0;c<Ae.length;c++){var f=Ae[c];if(f!==me){var p=n[f].conOpt;Tv(f,h,p,go(o,p,f),null)}}v?h.dirty():h.markRedraw()}}}}function Tl(a,e,t){var r=e?Ia(a,e):a,i=e?go(a,r,le):a.style,n=a.type,o=r?r.textConfig:null,s=a.textContent,l=s?e?Ia(s,e):s:null;if(i&&(t.isLegacy||Sv(i,n,!!o,!!l))){t.isLegacy=!0;var u=xv(i,n,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var h=l;!h.type&&(h.type="text")}var v=e?t[e]:t.normal;v.cfg=o,v.conOpt=l}function Ia(a,e){return e?a?a[e]:null:a}function go(a,e,t){var r=e&&e.style;return r==null&&t===le&&a&&(r=a.styleEmphasis),r}function cS(a,e,t,r,i){var n=r.children,o=n?n.length:0,s=r.$mergeChildren,l=s==="byName"||r.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){pS({api:a,oldChildren:e.children()||[],newChildren:n||[],dataIndex:t,seriesModel:i,group:e});return}u&&e.removeAll();for(var h=0;h<o;h++){var v=n[h],c=e.childAt(h);v?(v.ignore==null&&(v.ignore=!1),po(a,c,t,v,i,e)):c.ignore=!0}for(var f=e.childCount()-1;f>=h;f--){var p=e.childAt(f);fS(e,p,i)}}}function fS(a,e,t){e&&Ja(e,se(a).option,t)}function pS(a){new Fa(a.oldChildren,a.newChildren,Dl,Dl,a).add(Ll).update(Ll).remove(dS).execute()}function Dl(a,e){var t=a&&a.name;return t??aS+e}function Ll(a,e){var t=this.context,r=a!=null?t.newChildren[a]:null,i=e!=null?t.oldChildren[e]:null;po(t.api,i,t.dataIndex,r,t.seriesModel,t.group)}function dS(a){var e=this.context,t=e.oldChildren[a];t&&Ja(t,se(t).option,e.seriesModel)}function Lv(a){return a&&(a.pathData||a.d)}function gS(a){return a&&(z(a,"pathData")||z(a,"d"))}function yS(a){a.registerChartView(oS),a.registerSeriesModel(N0)}var mS=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,i,n,o){var s=i.axis;s.dim==="angle"&&(this.animationThreshold=Math.PI/18);var l=s.polar,u=l.getOtherAxis(s),h=u.getExtent(),v=s.dataToCoord(r),c=n.get("type");if(c&&c!=="none"){var f=ih(n),p=xS[c](s,l,v,h);p.style=f,t.graphicKey=p.type,t.pointer=p}var d=n.get(["label","margin"]),g=SS(r,i,n,l,d);nf(t,i,n,o,g)},e}(nh);function SS(a,e,t,r,i){var n=e.axis,o=n.dataToCoord(a),s=r.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l=r.getRadiusAxis().getExtent(),u,h,v;if(n.dim==="radius"){var c=tr();Oa(c,c,s),Oe(c,c,[r.cx,r.cy]),u=xe([o,-i],c);var f=e.getModel("axisLabel").get("rotate")||0,p=je.innerTextLayout(s,f*Math.PI/180,-1);h=p.textAlign,v=p.textVerticalAlign}else{var d=l[1];u=r.coordToPoint([d+i,o]);var g=r.cx,y=r.cy;h=Math.abs(u[0]-g)/d<.3?"center":u[0]>g?"left":"right",v=Math.abs(u[1]-y)/d<.3?"middle":u[1]>y?"top":"bottom"}return{position:u,align:h,verticalAlign:v}}var xS={line:function(a,e,t,r){return a.dim==="angle"?{type:"Line",shape:oh(e.coordToPoint([r[0],t]),e.coordToPoint([r[1],t]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:t}}},shadow:function(a,e,t,r){var i=Math.max(1,a.getBandWidth()),n=Math.PI/180;return a.dim==="angle"?{type:"Sector",shape:Uo(e.cx,e.cy,r[0],r[1],(-t-i/2)*n,(-t+i/2)*n)}:{type:"Sector",shape:Uo(e.cx,e.cy,t-i/2,t+i/2,0,Math.PI*2)}}},bS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.findAxisModel=function(t){var r,i=this.ecModel;return i.eachComponent(t,function(n){n.getCoordSysModel()===this&&(r=n)},this),r},e.type="polar",e.dependencies=["radiusAxis","angleAxis"],e.defaultOption={z:0,center:["50%","50%"],radius:"80%"},e}($t),yo=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",er).models[0]},e.type="polarAxis",e}($t);ie(yo,Fn);var _S=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="angleAxis",e}(yo),wS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="radiusAxis",e}(yo),mo=function(a){E(e,a);function e(t,r){return a.call(this,"radius",t,r)||this}return e.prototype.pointToData=function(t,r){return this.polar.pointToData(t,r)[this.dim==="radius"?0:1]},e}(de);mo.prototype.dataToRadius=de.prototype.dataToCoord;mo.prototype.radiusToData=de.prototype.coordToData;var AS=Dt(),So=function(a){E(e,a);function e(t,r){return a.call(this,"angle",t,r||[0,360])||this}return e.prototype.pointToData=function(t,r){return this.polar.pointToData(t,r)[this.dim==="radius"?0:1]},e.prototype.calculateCategoryInterval=function(){var t=this,r=t.getLabelModel(),i=t.scale,n=i.getExtent(),o=i.count();if(n[1]-n[0]<1)return 0;var s=n[0],l=t.dataToCoord(s+1)-t.dataToCoord(s),u=Math.abs(l),h=of(s==null?"":s+"",r.getFont(),"center","top"),v=Math.max(h.height,7),c=v/u;isNaN(c)&&(c=1/0);var f=Math.max(0,Math.floor(c)),p=AS(t.model),d=p.lastAutoInterval,g=p.lastTickCount;return d!=null&&g!=null&&Math.abs(d-f)<=1&&Math.abs(g-o)<=1&&d>f?f=d:(p.lastTickCount=o,p.lastAutoInterval=f),f},e}(de);So.prototype.dataToAngle=de.prototype.dataToCoord;So.prototype.angleToData=de.prototype.coordToData;var Cv=["radius","angle"],TS=function(){function a(e){this.dimensions=Cv,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new mo,this._angleAxis=new So,this.axisPointerEnabled=!0,this.name=e||"",this._radiusAxis.polar=this._angleAxis.polar=this}return a.prototype.containPoint=function(e){var t=this.pointToCoord(e);return this._radiusAxis.contain(t[0])&&this._angleAxis.contain(t[1])},a.prototype.containData=function(e){return this._radiusAxis.containData(e[0])&&this._angleAxis.containData(e[1])},a.prototype.getAxis=function(e){var t="_"+e+"Axis";return this[t]},a.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},a.prototype.getAxesByScale=function(e){var t=[],r=this._angleAxis,i=this._radiusAxis;return r.scale.type===e&&t.push(r),i.scale.type===e&&t.push(i),t},a.prototype.getAngleAxis=function(){return this._angleAxis},a.prototype.getRadiusAxis=function(){return this._radiusAxis},a.prototype.getOtherAxis=function(e){var t=this._angleAxis;return e===t?this._radiusAxis:t},a.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},a.prototype.getTooltipAxes=function(e){var t=e!=null&&e!=="auto"?this.getAxis(e):this.getBaseAxis();return{baseAxes:[t],otherAxes:[this.getOtherAxis(t)]}},a.prototype.dataToPoint=function(e,t){return this.coordToPoint([this._radiusAxis.dataToRadius(e[0],t),this._angleAxis.dataToAngle(e[1],t)])},a.prototype.pointToData=function(e,t){var r=this.pointToCoord(e);return[this._radiusAxis.radiusToData(r[0],t),this._angleAxis.angleToData(r[1],t)]},a.prototype.pointToCoord=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,i=this.getAngleAxis(),n=i.getExtent(),o=Math.min(n[0],n[1]),s=Math.max(n[0],n[1]);i.inverse?o=s-360:s=o+360;var l=Math.sqrt(t*t+r*r);t/=l,r/=l;for(var u=Math.atan2(-r,t)/Math.PI*180,h=u<o?1:-1;u<o||u>s;)u+=h*360;return[l,u]},a.prototype.coordToPoint=function(e){var t=e[0],r=e[1]/180*Math.PI,i=Math.cos(r)*t+this.cx,n=-Math.sin(r)*t+this.cy;return[i,n]},a.prototype.getArea=function(){var e=this.getAngleAxis(),t=this.getRadiusAxis(),r=t.getExtent().slice();r[0]>r[1]&&r.reverse();var i=e.getExtent(),n=Math.PI/180,o=1e-4;return{cx:this.cx,cy:this.cy,r0:r[0],r:r[1],startAngle:-i[0]*n,endAngle:-i[1]*n,clockwise:e.inverse,contain:function(s,l){var u=s-this.cx,h=l-this.cy,v=u*u+h*h,c=this.r,f=this.r0;return c!==f&&v-o<=c*c&&v+o>=f*f}}},a.prototype.convertToPixel=function(e,t,r){var i=Cl(t);return i===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=Cl(t);return i===this?this.pointToData(r):null},a}();function Cl(a){var e=a.seriesModel,t=a.polarModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function DS(a,e,t){var r=e.get("center"),i=t.getWidth(),n=t.getHeight();a.cx=O(r[0],i),a.cy=O(r[1],n);var o=a.getRadiusAxis(),s=Math.min(i,n)/2,l=e.get("radius");l==null?l=[0,"100%"]:H(l)||(l=[0,l]);var u=[O(l[0],s),O(l[1],s)];o.inverse?o.setExtent(u[1],u[0]):o.setExtent(u[0],u[1])}function LS(a,e){var t=this,r=t.getAngleAxis(),i=t.getRadiusAxis();if(r.scale.setExtent(1/0,-1/0),i.scale.setExtent(1/0,-1/0),a.eachSeries(function(s){if(s.coordinateSystem===t){var l=s.getData();D(Yo(l,"radius"),function(u){i.scale.unionExtentFromData(l,u)}),D(Yo(l,"angle"),function(u){r.scale.unionExtentFromData(l,u)})}}),ya(r.scale,r.model),ya(i.scale,i.model),r.type==="category"&&!r.onBand){var n=r.getExtent(),o=360/r.scale.count();r.inverse?n[1]+=o:n[1]-=o,r.setExtent(n[0],n[1])}}function CS(a){return a.mainType==="angleAxis"}function Il(a,e){var t;if(a.type=e.get("type"),a.scale=Bn(e),a.onBand=e.get("boundaryGap")&&a.type==="category",a.inverse=e.get("inverse"),CS(e)){a.inverse=a.inverse!==e.get("clockwise");var r=e.get("startAngle"),i=(t=e.get("endAngle"))!==null&&t!==void 0?t:r+(a.inverse?-360:360);a.setExtent(r,i)}e.axis=a,a.model=e}var IS={dimensions:Cv,create:function(a,e){var t=[];return a.eachComponent("polar",function(r,i){var n=new TS(i+"");n.update=LS;var o=n.getRadiusAxis(),s=n.getAngleAxis(),l=r.findAxisModel("radiusAxis"),u=r.findAxisModel("angleAxis");Il(o,l),Il(s,u),DS(n,r,e),t.push(n),r.coordinateSystem=n,n.model=r}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="polar"){var i=r.getReferringComponents("polar",er).models[0];r.coordinateSystem=i.coordinateSystem}}),t}},MS=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function Qr(a,e,t){e[1]>e[0]&&(e=e.slice().reverse());var r=a.coordToPoint([e[0],t]),i=a.coordToPoint([e[1],t]);return{x1:r[0],y1:r[1],x2:i[0],y2:i[1]}}function ta(a){var e=a.getRadiusAxis();return e.inverse?0:1}function Ml(a){var e=a[0],t=a[a.length-1];e&&t&&Math.abs(Math.abs(e.coord-t.coord)-360)<1e-4&&a.pop()}var PS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,r){if(this.group.removeAll(),!!t.get("show")){var i=t.axis,n=i.polar,o=n.getRadiusAxis().getExtent(),s=i.getTicksCoords(),l=i.getMinorTicksCoords(),u=W(i.getViewLabels(),function(h){h=ht(h);var v=i.scale,c=v.type==="ordinal"?v.getRawOrdinalNumber(h.tickValue):h.tickValue;return h.coord=i.dataToCoord(c),h});Ml(u),Ml(s),D(MS,function(h){t.get([h,"show"])&&(!i.scale.isBlank()||h==="axisLine")&&RS[h](this.group,t,n,s,l,o,u)},this)}},e.type="angleAxis",e}(Br),RS={axisLine:function(a,e,t,r,i,n){var o=e.getModel(["axisLine","lineStyle"]),s=t.getAngleAxis(),l=Math.PI/180,u=s.getExtent(),h=ta(t),v=h?0:1,c,f=Math.abs(u[1]-u[0])===360?"Circle":"Arc";n[v]===0?c=new Gr[f]({shape:{cx:t.cx,cy:t.cy,r:n[h],startAngle:-u[0]*l,endAngle:-u[1]*l,clockwise:s.inverse},style:o.getLineStyle(),z2:1,silent:!0}):c=new sf({shape:{cx:t.cx,cy:t.cy,r:n[h],r0:n[v]},style:o.getLineStyle(),z2:1,silent:!0}),c.style.fill=null,a.add(c)},axisTick:function(a,e,t,r,i,n){var o=e.getModel("axisTick"),s=(o.get("inside")?-1:1)*o.get("length"),l=n[ta(t)],u=W(r,function(h){return new Kt({shape:Qr(t,[l,l+s],h.coord)})});a.add(ne(u,{style:ft(o.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(a,e,t,r,i,n){if(i.length){for(var o=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(o.get("inside")?-1:1)*s.get("length"),u=n[ta(t)],h=[],v=0;v<i.length;v++)for(var c=0;c<i[v].length;c++)h.push(new Kt({shape:Qr(t,[u,u+l],i[v][c].coord)}));a.add(ne(h,{style:ft(s.getModel("lineStyle").getLineStyle(),ft(o.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(a,e,t,r,i,n,o){var s=e.getCategories(!0),l=e.getModel("axisLabel"),u=l.get("margin"),h=e.get("triggerEvent");D(o,function(v,c){var f=l,p=v.tickValue,d=n[ta(t)],g=t.coordToPoint([d+u,v.coord]),y=t.cx,m=t.cy,S=Math.abs(g[0]-y)/d<.3?"center":g[0]>y?"left":"right",x=Math.abs(g[1]-m)/d<.3?"middle":g[1]>m?"top":"bottom";if(s&&s[p]){var b=s[p];Ot(b)&&b.textStyle&&(f=new Jt(b.textStyle,l,l.ecModel))}var _=new ut({silent:je.isLabelSilent(e),style:ct(f,{x:g[0],y:g[1],fill:f.getTextColor()||e.get(["axisLine","lineStyle","color"]),text:v.formattedLabel,align:S,verticalAlign:x})});if(a.add(_),h){var w=je.makeAxisEventDataBase(e);w.targetType="axisLabel",w.value=v.rawLabel,nt(_).eventData=w}},this)},splitLine:function(a,e,t,r,i,n){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var h=[],v=0;v<r.length;v++){var c=u++%l.length;h[c]=h[c]||[],h[c].push(new Kt({shape:Qr(t,n,r[v].coord)}))}for(var v=0;v<h.length;v++)a.add(ne(h[v],{style:ft({stroke:l[v%l.length]},s.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(a,e,t,r,i,n){if(i.length){for(var o=e.getModel("minorSplitLine"),s=o.getModel("lineStyle"),l=[],u=0;u<i.length;u++)for(var h=0;h<i[u].length;h++)l.push(new Kt({shape:Qr(t,n,i[u][h].coord)}));a.add(ne(l,{style:s.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(a,e,t,r,i,n){if(r.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var h=[],v=Math.PI/180,c=-r[0].coord*v,f=Math.min(n[0],n[1]),p=Math.max(n[0],n[1]),d=e.get("clockwise"),g=1,y=r.length;g<=y;g++){var m=g===y?r[0].coord:r[g].coord,S=u++%l.length;h[S]=h[S]||[],h[S].push(new wr({shape:{cx:t.cx,cy:t.cy,r0:f,r:p,startAngle:c,endAngle:-m*v,clockwise:d},silent:!0})),c=-m*v}for(var g=0;g<h.length;g++)a.add(ne(h[g],{style:ft({fill:l[g%l.length]},s.getAreaStyle()),silent:!0}))}}},ES=["axisLine","axisTickLabel","axisName"],VS=["splitLine","splitArea","minorSplitLine"],kS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,r){if(this.group.removeAll(),!!t.get("show")){var i=this._axisGroup,n=this._axisGroup=new $;this.group.add(n);var o=t.axis,s=o.polar,l=s.getAngleAxis(),u=o.getTicksCoords(),h=o.getMinorTicksCoords(),v=l.getExtent()[0],c=o.getExtent(),f=zS(s,t,v),p=new je(t,f);D(ES,p.add,p),n.add(p.getGroup()),Hn(i,n,t),D(VS,function(d){t.get([d,"show"])&&!o.scale.isBlank()&&NS[d](this.group,t,s,v,c,u,h)},this)}},e.type="radiusAxis",e}(Br),NS={splitLine:function(a,e,t,r,i,n){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0,h=t.getAngleAxis(),v=Math.PI/180,c=h.getExtent(),f=Math.abs(c[1]-c[0])===360?"Circle":"Arc";l=l instanceof Array?l:[l];for(var p=[],d=0;d<n.length;d++){var g=u++%l.length;p[g]=p[g]||[],p[g].push(new Gr[f]({shape:{cx:t.cx,cy:t.cy,r:Math.max(n[d].coord,0),startAngle:-c[0]*v,endAngle:-c[1]*v,clockwise:h.inverse}}))}for(var d=0;d<p.length;d++)a.add(ne(p[d],{style:ft({stroke:l[d%l.length],fill:null},s.getLineStyle()),silent:!0}))},minorSplitLine:function(a,e,t,r,i,n,o){if(o.length){for(var s=e.getModel("minorSplitLine"),l=s.getModel("lineStyle"),u=[],h=0;h<o.length;h++)for(var v=0;v<o[h].length;v++)u.push(new za({shape:{cx:t.cx,cy:t.cy,r:o[h][v].coord}}));a.add(ne(u,{style:ft({fill:null},l.getLineStyle()),silent:!0}))}},splitArea:function(a,e,t,r,i,n){if(n.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var h=[],v=n[0].coord,c=1;c<n.length;c++){var f=u++%l.length;h[f]=h[f]||[],h[f].push(new wr({shape:{cx:t.cx,cy:t.cy,r0:v,r:n[c].coord,startAngle:0,endAngle:Math.PI*2},silent:!0})),v=n[c].coord}for(var c=0;c<h.length;c++)a.add(ne(h[c],{style:ft({fill:l[c%l.length]},s.getAreaStyle()),silent:!0}))}}};function zS(a,e,t){return{position:[a.cx,a.cy],rotation:t/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}function Iv(a){return a.get("stack")||"__ec_stack_"+a.seriesIndex}function Mv(a,e){return e.dim+a.model.componentIndex}function OS(a,e,t){var r={},i=GS(Gt(e.getSeriesByType(a),function(n){return!e.isSeriesFiltered(n)&&n.coordinateSystem&&n.coordinateSystem.type==="polar"}));e.eachSeriesByType(a,function(n){if(n.coordinateSystem.type==="polar"){var o=n.getData(),s=n.coordinateSystem,l=s.getBaseAxis(),u=Mv(s,l),h=Iv(n),v=i[u][h],c=v.offset,f=v.width,p=s.getOtherAxis(l),d=n.coordinateSystem.cx,g=n.coordinateSystem.cy,y=n.get("barMinHeight")||0,m=n.get("barMinAngle")||0;r[h]=r[h]||[];for(var S=o.mapDimension(p.dim),x=o.mapDimension(l.dim),b=sh(o,S),_=l.dim!=="radius"||!n.get("roundCap",!0),w=p.model,T=w.get("startValue"),L=p.dataToCoord(T||0),A=0,C=o.count();A<C;A++){var I=o.get(S,A),M=o.get(x,A),P=I>=0?"p":"n",R=L;b&&(r[h][M]||(r[h][M]={p:L,n:L}),R=r[h][M][P]);var V=void 0,k=void 0,N=void 0,G=void 0;if(p.dim==="radius"){var Z=p.dataToCoord(I)-L,U=l.dataToCoord(M);Math.abs(Z)<y&&(Z=(Z<0?-1:1)*y),V=R,k=R+Z,N=U-c,G=N-f,b&&(r[h][M][P]=k)}else{var K=p.dataToCoord(I,_)-L,J=l.dataToCoord(M);Math.abs(K)<m&&(K=(K<0?-1:1)*m),V=J+c,k=V+f,N=R,G=R+K,b&&(r[h][M][P]=G)}o.setItemLayout(A,{cx:d,cy:g,r0:V,r:k,startAngle:-N*Math.PI/180,endAngle:-G*Math.PI/180,clockwise:N>=G})}}})}function GS(a){var e={};D(a,function(r,i){var n=r.getData(),o=r.coordinateSystem,s=o.getBaseAxis(),l=Mv(o,s),u=s.getExtent(),h=s.type==="category"?s.getBandWidth():Math.abs(u[1]-u[0])/n.count(),v=e[l]||{bandWidth:h,remainedWidth:h,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},c=v.stacks;e[l]=v;var f=Iv(r);c[f]||v.autoWidthCount++,c[f]=c[f]||{width:0,maxWidth:0};var p=O(r.get("barWidth"),h),d=O(r.get("barMaxWidth"),h),g=r.get("barGap"),y=r.get("barCategoryGap");p&&!c[f].width&&(p=Math.min(v.remainedWidth,p),c[f].width=p,v.remainedWidth-=p),d&&(c[f].maxWidth=d),g!=null&&(v.gap=g),y!=null&&(v.categoryGap=y)});var t={};return D(e,function(r,i){t[i]={};var n=r.stacks,o=r.bandWidth,s=O(r.categoryGap,o),l=O(r.gap,1),u=r.remainedWidth,h=r.autoWidthCount,v=(u-s)/(h+(h-1)*l);v=Math.max(v,0),D(n,function(d,g){var y=d.maxWidth;y&&y<v&&(y=Math.min(y,u),d.width&&(y=Math.min(y,d.width)),u-=y,d.width=y,h--)}),v=(u-s)/(h+(h-1)*l),v=Math.max(v,0);var c=0,f;D(n,function(d,g){d.width||(d.width=v),f=d,c+=d.width*(1+l)}),f&&(c-=f.width*l);var p=-c/2;D(n,function(d,g){t[i][g]=t[i][g]||{offset:p,width:d.width},p+=d.width*(1+l)})}),t}var BS={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},FS={splitNumber:5},HS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="polar",e}(te);function WS(a){X(Zn),Br.registerAxisPointerClass("PolarAxisPointer",mS),a.registerCoordinateSystem("polar",IS),a.registerComponentModel(bS),a.registerComponentView(HS),ma(a,"angle",_S,BS),ma(a,"radius",wS,FS),a.registerComponentView(PS),a.registerComponentView(kS),a.registerLayout(it(OS,"bar"))}function bn(a,e){e=e||{};var t=a.coordinateSystem,r=a.axis,i={},n=r.position,o=r.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};i.position=[o==="vertical"?u.vertical[n]:l[0],o==="horizontal"?u.horizontal[n]:l[3]];var h={horizontal:0,vertical:1};i.rotation=Math.PI/2*h[o];var v={top:-1,bottom:1,right:1,left:-1};i.labelDirection=i.tickDirection=i.nameDirection=v[n],a.get(["axisTick","inside"])&&(i.tickDirection=-i.tickDirection),Vt(e.labelInside,a.get(["axisLabel","inside"]))&&(i.labelDirection=-i.labelDirection);var c=e.rotate;return c==null&&(c=a.get(["axisLabel","rotate"])),i.labelRotation=n==="top"?-c:c,i.z2=1,i}var ZS=["axisLine","axisTickLabel","axisName"],US=["splitArea","splitLine"],YS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,r,i,n){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new $;var l=bn(t),u=new je(t,l);D(ZS,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),D(US,function(h){t.get([h,"show"])&&$S[h](this,this.group,this._axisGroup,t)},this),Hn(s,this._axisGroup,t),a.prototype.render.call(this,t,r,i,n)},e.prototype.remove=function(){lf(this)},e.type="singleAxis",e}(Br),$S={splitLine:function(a,e,t,r){var i=r.axis;if(!i.scale.isBlank()){var n=r.getModel("splitLine"),o=n.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=r.coordinateSystem.getRect(),h=i.isHorizontal(),v=[],c=0,f=i.getTicksCoords({tickModel:n}),p=[],d=[],g=0;g<f.length;++g){var y=i.toGlobalCoord(f[g].coord);h?(p[0]=y,p[1]=u.y,d[0]=y,d[1]=u.y+u.height):(p[0]=u.x,p[1]=y,d[0]=u.x+u.width,d[1]=y);var m=new Kt({shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},silent:!0});hf(m.shape,l);var S=c++%s.length;v[S]=v[S]||[],v[S].push(m)}for(var x=o.getLineStyle(["color"]),g=0;g<v.length;++g)e.add(ne(v[g],{style:ft({stroke:s[g%s.length]},x),silent:!0}))}},splitArea:function(a,e,t,r){uf(a,t,r,r)}},va=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}($t);ie(va,Fn.prototype);var XS=function(a){E(e,a);function e(t,r,i,n,o){var s=a.call(this,t,r,i)||this;return s.type=n||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,r){return this.coordinateSystem.pointToData(t)[0]},e}(de),Pv=["single"],KS=function(){function a(e,t,r){this.type="single",this.dimension="single",this.dimensions=Pv,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var i=this.dimension,n=new XS(i,Bn(e),[0,0],e.get("type"),e.get("position")),o=n.type==="category";n.onBand=o&&e.get("boundaryGap"),n.inverse=e.get("inverse"),n.orient=e.get("orient"),e.axis=n,n.model=e,n.coordinateSystem=this,this._axis=n},a.prototype.update=function(e,t){e.eachSeries(function(r){if(r.coordinateSystem===this){var i=r.getData();D(i.mapDimensionsAll(this.dimension),function(n){this._axis.scale.unionExtentFromData(i,n)},this),ya(this._axis.scale,this._axis.model)}},this)},a.prototype.resize=function(e,t){this._rect=Yt({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},a.prototype.getRect=function(){return this._rect},a.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,r=t.isHorizontal(),i=r?[0,e.width]:[0,e.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),this._updateAxisTransform(t,r?e.x:e.y)},a.prototype._updateAxisTransform=function(e,t){var r=e.getExtent(),i=r[0]+r[1],n=e.isHorizontal();e.toGlobalCoord=n?function(o){return o+t}:function(o){return i-o+t},e.toLocalCoord=n?function(o){return o-t}:function(o){return i-o+t}},a.prototype.getAxis=function(){return this._axis},a.prototype.getBaseAxis=function(){return this._axis},a.prototype.getAxes=function(){return[this._axis]},a.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},a.prototype.containPoint=function(e){var t=this.getRect(),r=this.getAxis(),i=r.orient;return i==="horizontal"?r.contain(r.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:r.contain(r.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},a.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},a.prototype.dataToPoint=function(e){var t=this.getAxis(),r=this.getRect(),i=[],n=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),i[n]=t.toGlobalCoord(t.dataToCoord(+e)),i[1-n]=n===0?r.y+r.height/2:r.x+r.width/2,i},a.prototype.convertToPixel=function(e,t,r){var i=Pl(t);return i===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=Pl(t);return i===this?this.pointToData(r):null},a}();function Pl(a){var e=a.seriesModel,t=a.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function qS(a,e){var t=[];return a.eachComponent("singleAxis",function(r,i){var n=new KS(r,a,e);n.name="single_"+i,n.resize(r,e),r.coordinateSystem=n,t.push(n)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="singleAxis"){var i=r.getReferringComponents("singleAxis",er).models[0];r.coordinateSystem=i&&i.coordinateSystem}}),t}var jS={create:qS,dimensions:Pv},Rl=["x","y"],JS=["width","height"],QS=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,i,n,o){var s=i.axis,l=s.coordinateSystem,u=Gi(l,1-Ma(s)),h=l.dataToPoint(r)[0],v=n.get("type");if(v&&v!=="none"){var c=ih(n),f=t1[v](s,h,u);f.style=c,t.graphicKey=f.type,t.pointer=f}var p=bn(i);vf(r,t,p,i,n,o)},e.prototype.getHandleTransform=function(t,r,i){var n=bn(r,{labelInside:!1});n.labelMargin=i.get(["handle","margin"]);var o=cf(r.axis,t,n);return{x:o[0],y:o[1],rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,r,i,n){var o=i.axis,s=o.coordinateSystem,l=Ma(o),u=Gi(s,l),h=[t.x,t.y];h[l]+=r[l],h[l]=Math.min(u[1],h[l]),h[l]=Math.max(u[0],h[l]);var v=Gi(s,1-l),c=(v[1]+v[0])/2,f=[c,c];return f[l]=h[l],{x:h[0],y:h[1],rotation:t.rotation,cursorPoint:f,tooltipOption:{verticalAlign:"middle"}}},e}(nh),t1={line:function(a,e,t){var r=oh([e,t[0]],[e,t[1]],Ma(a));return{type:"Line",subPixelOptimize:!0,shape:r}},shadow:function(a,e,t){var r=a.getBandWidth(),i=t[1]-t[0];return{type:"Rect",shape:ff([e-r/2,t[0]],[r,i],Ma(a))}}};function Ma(a){return a.isHorizontal()?0:1}function Gi(a,e){var t=a.getRect();return[t[Rl[e]],t[Rl[e]]+t[JS[e]]]}var e1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(te);function r1(a){X(Zn),Br.registerAxisPointerClass("SingleAxisPointer",QS),a.registerComponentView(e1),a.registerComponentView(YS),a.registerComponentModel(va),ma(a,"single",va,va.defaultOption),a.registerCoordinateSystem("single",jS)}var a1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,i){var n=lh(t);a.prototype.init.apply(this,arguments),El(t,n)},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),El(this.option,t)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}($t);function El(a,e){var t=a.cellSize,r;H(t)?r=t:r=a.cellSize=[t,t],r.length===1&&(r[1]=r[0]);var i=W([0,1],function(n){return pf(e,n)&&(r[n]="auto"),r[n]!=null&&r[n]!=="auto"});uh(a,e,{type:"box",ignoreSize:i})}var i1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=this.group;n.removeAll();var o=t.coordinateSystem,s=o.getRangeInfo(),l=o.getOrient(),u=r.getLocaleModel();this._renderDayRect(t,s,n),this._renderLines(t,s,l,n),this._renderYearText(t,s,l,n),this._renderMonthText(t,u,l,n),this._renderWeekText(t,u,s,l,n)},e.prototype._renderDayRect=function(t,r,i){for(var n=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),s=n.getCellWidth(),l=n.getCellHeight(),u=r.start.time;u<=r.end.time;u=n.getNextNDay(u,1).time){var h=n.dataToRect([u],!1).tl,v=new gt({shape:{x:h[0],y:h[1],width:s,height:l},cursor:"default",style:o});i.add(v)}},e.prototype._renderLines=function(t,r,i,n){var o=this,s=t.coordinateSystem,l=t.getModel(["splitLine","lineStyle"]).getLineStyle(),u=t.get(["splitLine","show"]),h=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var v=r.start,c=0;v.time<=r.end.time;c++){p(v.formatedDate),c===0&&(v=s.getDateInfo(r.start.y+"-"+r.start.m));var f=v.date;f.setMonth(f.getMonth()+1),v=s.getDateInfo(f)}p(s.getNextNDay(r.end.time,1).formatedDate);function p(d){o._firstDayOfMonth.push(s.getDateInfo(d)),o._firstDayPoints.push(s.dataToRect([d],!1).tl);var g=o._getLinePointsOfOneWeek(t,d,i);o._tlpoints.push(g[0]),o._blpoints.push(g[g.length-1]),u&&o._drawSplitline(g,l,n)}u&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,h,i),l,n),u&&this._drawSplitline(o._getEdgesPoints(o._blpoints,h,i),l,n)},e.prototype._getEdgesPoints=function(t,r,i){var n=[t[0].slice(),t[t.length-1].slice()],o=i==="horizontal"?0:1;return n[0][o]=n[0][o]-r/2,n[1][o]=n[1][o]+r/2,n},e.prototype._drawSplitline=function(t,r,i){var n=new Be({z2:20,shape:{points:t},style:r});i.add(n)},e.prototype._getLinePointsOfOneWeek=function(t,r,i){for(var n=t.coordinateSystem,o=n.getDateInfo(r),s=[],l=0;l<7;l++){var u=n.getNextNDay(o.time,l),h=n.dataToRect([u.time],!1);s[2*u.day]=h.tl,s[2*u.day+1]=h[i==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(t,r){return Q(t)&&t?df(t,r):st(t)?t(r):r.nameMap},e.prototype._yearTextPositionControl=function(t,r,i,n,o){var s=r[0],l=r[1],u=["center","bottom"];n==="bottom"?(l+=o,u=["center","top"]):n==="left"?s-=o:n==="right"?(s+=o,u=["center","top"]):l-=o;var h=0;return(n==="left"||n==="right")&&(h=Math.PI/2),{rotation:h,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(t,r,i,n){var o=t.getModel("yearLabel");if(o.get("show")){var s=o.get("margin"),l=o.get("position");l||(l=i!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],h=(u[0][0]+u[1][0])/2,v=(u[0][1]+u[1][1])/2,c=i==="horizontal"?0:1,f={top:[h,u[c][1]],bottom:[h,u[1-c][1]],left:[u[1-c][0],v],right:[u[c][0],v]},p=r.start.y;+r.end.y>+r.start.y&&(p=p+"-"+r.end.y);var d=o.get("formatter"),g={start:r.start.y,end:r.end.y,nameMap:p},y=this._formatterLabel(d,g),m=new ut({z2:30,style:ct(o,{text:y}),silent:o.get("silent")});m.attr(this._yearTextPositionControl(m,f[l],i,l,s)),n.add(m)}},e.prototype._monthTextPositionControl=function(t,r,i,n,o){var s="left",l="top",u=t[0],h=t[1];return i==="horizontal"?(h=h+o,r&&(s="center"),n==="start"&&(l="bottom")):(u=u+o,r&&(l="middle"),n==="start"&&(s="right")),{x:u,y:h,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(t,r,i,n){var o=t.getModel("monthLabel");if(o.get("show")){var s=o.get("nameMap"),l=o.get("margin"),u=o.get("position"),h=o.get("align"),v=[this._tlpoints,this._blpoints];(!s||Q(s))&&(s&&(r=$o(s)||r),s=r.get(["time","monthAbbr"])||[]);var c=u==="start"?0:1,f=i==="horizontal"?0:1;l=u==="start"?-l:l;for(var p=h==="center",d=o.get("silent"),g=0;g<v[c].length-1;g++){var y=v[c][g].slice(),m=this._firstDayOfMonth[g];if(p){var S=this._firstDayPoints[g];y[f]=(S[f]+v[0][g+1][f])/2}var x=o.get("formatter"),b=s[+m.m-1],_={yyyy:m.y,yy:(m.y+"").slice(2),MM:m.m,M:+m.m,nameMap:b},w=this._formatterLabel(x,_),T=new ut({z2:30,style:F(ct(o,{text:w}),this._monthTextPositionControl(y,p,i,u,l)),silent:d});n.add(T)}}},e.prototype._weekTextPositionControl=function(t,r,i,n,o){var s="center",l="middle",u=t[0],h=t[1],v=i==="start";return r==="horizontal"?(u=u+n+(v?1:-1)*o[0]/2,s=v?"right":"left"):(h=h+n+(v?1:-1)*o[1]/2,l=v?"bottom":"top"),{x:u,y:h,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(t,r,i,n,o){var s=t.getModel("dayLabel");if(s.get("show")){var l=t.coordinateSystem,u=s.get("position"),h=s.get("nameMap"),v=s.get("margin"),c=l.getFirstDayOfWeek();if(!h||Q(h)){h&&(r=$o(h)||r);var f=r.get(["time","dayOfWeekShort"]);h=f||W(r.get(["time","dayOfWeekAbbr"]),function(_){return _[0]})}var p=l.getNextNDay(i.end.time,7-i.lweek).time,d=[l.getCellWidth(),l.getCellHeight()];v=O(v,Math.min(d[1],d[0])),u==="start"&&(p=l.getNextNDay(i.start.time,-(7+i.fweek)).time,v=-v);for(var g=s.get("silent"),y=0;y<7;y++){var m=l.getNextNDay(p,y),S=l.dataToRect([m.time],!1).center,x=y;x=Math.abs((y+c)%7);var b=new ut({z2:30,style:F(ct(s,{text:h[x]}),this._weekTextPositionControl(S,n,u,v,d)),silent:g});o.add(b)}}},e.type="calendar",e}(te),Bi=864e5,n1=function(){function a(e,t,r){this.type="calendar",this.dimensions=a.dimensions,this.getDimensionsInfo=a.getDimensionsInfo,this._model=e}return a.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},a.prototype.getRangeInfo=function(){return this._rangeInfo},a.prototype.getModel=function(){return this._model},a.prototype.getRect=function(){return this._rect},a.prototype.getCellWidth=function(){return this._sw},a.prototype.getCellHeight=function(){return this._sh},a.prototype.getOrient=function(){return this._orient},a.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},a.prototype.getDateInfo=function(e){e=gf(e);var t=e.getFullYear(),r=e.getMonth()+1,i=r<10?"0"+r:""+r,n=e.getDate(),o=n<10?"0"+n:""+n,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:t+"",m:i,d:o,day:s,time:e.getTime(),formatedDate:t+"-"+i+"-"+o,date:e}},a.prototype.getNextNDay=function(e,t){return t=t||0,t===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t),this.getDateInfo(e))},a.prototype.update=function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var r=this._rangeInfo.weeks||1,i=["width","height"],n=this._model.getCellSize().slice(),o=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[r,7]:[7,r];D([0,1],function(v){h(n,v)&&(o[i[v]]=n[v]*s[v])});var l={width:t.getWidth(),height:t.getHeight()},u=this._rect=Yt(o,l);D([0,1],function(v){h(n,v)||(n[v]=u[i[v]]/s[v])});function h(v,c){return v[c]!=null&&v[c]!=="auto"}this._sw=n[0],this._sh=n[1]},a.prototype.dataToPoint=function(e,t){H(e)&&(e=e[0]),t==null&&(t=!0);var r=this.getDateInfo(e),i=this._rangeInfo,n=r.formatedDate;if(t&&!(r.time>=i.start.time&&r.time<i.end.time+Bi))return[NaN,NaN];var o=r.day,s=this._getRangeInfo([i.start.time,n]).nthWeek;return this._orient==="vertical"?[this._rect.x+o*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+o*this._sh+this._sh/2]},a.prototype.pointToData=function(e){var t=this.pointToDate(e);return t&&t.time},a.prototype.dataToRect=function(e,t){var r=this.dataToPoint(e,t);return{contentShape:{x:r[0]-(this._sw-this._lineWidth)/2,y:r[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:r,tl:[r[0]-this._sw/2,r[1]-this._sh/2],tr:[r[0]+this._sw/2,r[1]-this._sh/2],br:[r[0]+this._sw/2,r[1]+this._sh/2],bl:[r[0]-this._sw/2,r[1]+this._sh/2]}},a.prototype.pointToDate=function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,r=Math.floor((e[1]-this._rect.y)/this._sh)+1,i=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(r,t-1,i):this._getDateByWeeksAndDay(t,r-1,i)},a.prototype.convertToPixel=function(e,t,r){var i=Vl(t);return i===this?i.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=Vl(t);return i===this?i.pointToData(r):null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.prototype._initRangeOption=function(){var e=this._model.get("range"),t;if(H(e)&&e.length===1&&(e=e[0]),H(e))t=e;else{var r=e.toString();if(/^\d{4}$/.test(r)&&(t=[r+"-01-01",r+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(r)){var i=this.getDateInfo(r),n=i.date;n.setMonth(n.getMonth()+1);var o=this.getNextNDay(n,-1);t=[i.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(r)&&(t=[r,r])}if(!t)return e;var s=this._getRangeInfo(t);return s.start.time>s.end.time&&t.reverse(),t},a.prototype._getRangeInfo=function(e){var t=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],r;t[0].time>t[1].time&&(r=!0,t.reverse());var i=Math.floor(t[1].time/Bi)-Math.floor(t[0].time/Bi)+1,n=new Date(t[0].time),o=n.getDate(),s=t[1].date.getDate();n.setDate(o+i-1);var l=n.getDate();if(l!==s)for(var u=n.getTime()-t[1].time>0?1:-1;(l=n.getDate())!==s&&(n.getTime()-t[1].time)*u>0;)i-=u,n.setDate(l-u);var h=Math.floor((i+t[0].day+6)/7),v=r?-h+1:h-1;return r&&t.reverse(),{range:[t[0].formatedDate,t[1].formatedDate],start:t[0],end:t[1],allDay:i,weeks:h,nthWeek:v,fweek:t[0].day,lweek:t[1].day}},a.prototype._getDateByWeeksAndDay=function(e,t,r){var i=this._getRangeInfo(r);if(e>i.weeks||e===0&&t<i.fweek||e===i.weeks&&t>i.lweek)return null;var n=(e-1)*7-i.fweek+t,o=new Date(i.start.time);return o.setDate(+i.start.d+n),this.getDateInfo(o)},a.create=function(e,t){var r=[];return e.eachComponent("calendar",function(i){var n=new a(i);r.push(n),i.coordinateSystem=n}),e.eachSeries(function(i){i.get("coordinateSystem")==="calendar"&&(i.coordinateSystem=r[i.get("calendarIndex")||0])}),r},a.dimensions=["time","value"],a}();function Vl(a){var e=a.calendarModel,t=a.seriesModel,r=e?e.coordinateSystem:t?t.coordinateSystem:null;return r}function o1(a){a.registerComponentModel(a1),a.registerComponentView(i1),a.registerCoordinateSystem("calendar",n1)}function s1(a,e){var t=a.existing;if(e.id=a.keyInfo.id,!e.type&&t&&(e.type=t.type),e.parentId==null){var r=e.parentOption;r?e.parentId=r.id:t&&(e.parentId=t.parentId)}e.parentOption=null}function kl(a,e){var t;return D(e,function(r){a[r]!=null&&a[r]!=="auto"&&(t=!0)}),t}function l1(a,e,t){var r=F({},t),i=a[e],n=t.$action||"merge";n==="merge"?i?(zt(i,r,!0),uh(i,r,{ignoreSize:!0}),mf(t,i),ea(t,i),ea(t,i,"shape"),ea(t,i,"style"),ea(t,i,"extra"),t.clipPath=i.clipPath):a[e]=r:n==="replace"?a[e]=r:n==="remove"&&i&&(a[e]=null)}var Rv=["transition","enterFrom","leaveTo"],u1=Rv.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function ea(a,e,t){if(t&&(!a[t]&&e[t]&&(a[t]={}),a=a[t],e=e[t]),!(!a||!e))for(var r=t?Rv:u1,i=0;i<r.length;i++){var n=r[i];a[n]==null&&e[n]!=null&&(a[n]=e[n])}}function h1(a,e){if(a&&(a.hv=e.hv=[kl(e,["left","right"]),kl(e,["top","bottom"])],a.type==="group")){var t=a,r=e;t.width==null&&(t.width=r.width=0),t.height==null&&(t.height=r.height=0)}}var v1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventAutoZ=!0,t}return e.prototype.mergeOption=function(t,r){var i=this.option.elements;this.option.elements=null,a.prototype.mergeOption.call(this,t,r),this.option.elements=i},e.prototype.optionUpdated=function(t,r){var i=this.option,n=(r?i:t).elements,o=i.elements=r?[]:i.elements,s=[];this._flatten(n,s,null);var l=yf(o,s,"normalMerge"),u=this._elOptionsToUpdate=[];D(l,function(h,v){var c=h.newOption;c&&(u.push(c),s1(h,c),l1(o,v,c),h1(o[v],c))},this),i.elements=Gt(o,function(h){return h&&delete h.$action,h!=null})},e.prototype._flatten=function(t,r,i){D(t,function(n){if(n){i&&(n.parentOption=i),r.push(n);var o=n.children;o&&o.length&&this._flatten(o,r,n),delete n.children}},this)},e.prototype.useElOptionsToUpdate=function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t},e.type="graphic",e.defaultOption={elements:[]},e}($t),Nl={path:null,compoundPath:null,group:$,image:pe,text:ut},Ht=Dt(),c1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._elMap=rt()},e.prototype.render=function(t,r,i){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,i)},e.prototype._updateElements=function(t){var r=t.useElOptionsToUpdate();if(r){var i=this._elMap,n=this.group,o=t.get("z"),s=t.get("zlevel");D(r,function(l){var u=oe(l.id,null),h=u!=null?i.get(u):null,v=oe(l.parentId,null),c=v!=null?i.get(v):n,f=l.type,p=l.style;f==="text"&&p&&l.hv&&l.hv[1]&&(p.textVerticalAlign=p.textBaseline=p.verticalAlign=p.align=null);var d=l.textContent,g=l.textConfig;if(p&&Sv(p,f,!!g,!!d)){var y=xv(p,f,!0);!g&&y.textConfig&&(g=l.textConfig=y.textConfig),!d&&y.textContent&&(d=y.textContent)}var m=f1(l),S=l.$action||"merge",x=S==="merge",b=S==="replace";if(x){var _=!h,w=h;_?w=zl(u,c,l.type,i):(w&&(Ht(w).isNew=!1),Av(w)),w&&(ha(w,m,t,{isInit:_}),Ol(w,l,o,s))}else if(b){ca(h,l,i,t);var T=zl(u,c,l.type,i);T&&(ha(T,m,t,{isInit:!0}),Ol(T,l,o,s))}else S==="remove"&&(_v(h,l),ca(h,l,i,t));var L=i.get(u);if(L&&d)if(x){var A=L.getTextContent();A?A.attr(d):L.setTextContent(new ut(d))}else b&&L.setTextContent(new ut(d));if(L){var C=l.clipPath;if(C){var I=C.type,M=void 0,_=!1;if(x){var P=L.getClipPath();_=!P||Ht(P).type!==I,M=_?_n(I):P}else b&&(_=!0,M=_n(I));L.setClipPath(M),ha(M,C,t,{isInit:_}),Ca(M,C.keyframeAnimation,t)}var R=Ht(L);L.setTextConfig(g),R.option=l,p1(L,t,l),Bu({el:L,componentModel:t,itemName:L.name,itemTooltipOption:l.tooltip}),Ca(L,l.keyframeAnimation,t)}})}},e.prototype._relocate=function(t,r){for(var i=t.option.elements,n=this.group,o=this._elMap,s=r.getWidth(),l=r.getHeight(),u=["x","y"],h=0;h<i.length;h++){var v=i[h],c=oe(v.id,null),f=c!=null?o.get(c):null;if(!(!f||!f.isGroup)){var p=f.parent,d=p===n,g=Ht(f),y=Ht(p);g.width=O(g.option.width,d?s:y.width)||0,g.height=O(g.option.height,d?l:y.height)||0}}for(var h=i.length-1;h>=0;h--){var v=i[h],c=oe(v.id,null),f=c!=null?o.get(c):null;if(f){var p=f.parent,y=Ht(p),m=p===n?{width:s,height:l}:{width:y.width,height:y.height},S={},x=Vn(f,v,m,null,{hv:v.hv,boundingMode:v.bounding},S);if(!Ht(f).isNew&&x){for(var b=v.transition,_={},w=0;w<u.length;w++){var T=u[w],L=S[T];b&&(Ne(b)||Mt(b,T)>=0)?_[T]=L:f[T]=L}mt(f,_,t,0)}else f.attr(S)}}},e.prototype._clear=function(){var t=this,r=this._elMap;r.each(function(i){ca(i,Ht(i).option,r,t._lastGraphicModel)}),this._elMap=rt()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(te);function _n(a){var e=z(Nl,a)?Nl[a]:ah(a),t=new e({});return Ht(t).type=a,t}function zl(a,e,t,r){var i=_n(t);return e.add(i),r.set(a,i),Ht(i).id=a,Ht(i).isNew=!0,i}function ca(a,e,t,r){var i=a&&a.parent;i&&(a.type==="group"&&a.traverse(function(n){ca(n,e,t,r)}),Ja(a,e,r),t.removeKey(Ht(a).id))}function Ol(a,e,t,r){a.isGroup||D([["cursor",Ke.prototype.cursor],["zlevel",r||0],["z",t||0],["z2",0]],function(i){var n=i[0];z(e,n)?a[n]=Tt(e[n],i[1]):a[n]==null&&(a[n]=i[1])}),D(pt(e),function(i){if(i.indexOf("on")===0){var n=e[i];a[i]=st(n)?n:null}}),z(e,"draggable")&&(a.draggable=e.draggable),e.name!=null&&(a.name=e.name),e.id!=null&&(a.id=e.id)}function f1(a){return a=F({},a),D(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(Sf),function(e){delete a[e]}),a}function p1(a,e,t){var r=nt(a).eventData;!a.silent&&!a.ignore&&!r&&(r=nt(a).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:a.name}),r&&(r.info=t.info)}function d1(a){a.registerComponentModel(v1),a.registerComponentView(c1),a.registerPreprocessor(function(e){var t=e.graphic;H(t)?!t[0]||!t[0].elements?e.graphic=[{elements:t}]:e.graphic=[e.graphic[0]]:t&&!t.elements&&(e.graphic=[{elements:[t]}])})}var g1=["rect","polygon","keep","clear"];function y1(a,e){var t=ue(a?a.brush:[]);if(t.length){var r=[];D(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(r=r.concat(u))});var i=a&&a.toolbox;H(i)&&(i=i[0]),i||(i={feature:{}},a.toolbox=[i]);var n=i.feature||(i.feature={}),o=n.brush||(n.brush={}),s=o.type||(o.type=[]);s.push.apply(s,r),m1(s),e&&!s.length&&s.push.apply(s,g1)}}function m1(a){var e={};D(a,function(t){e[t]=1}),a.length=0,D(e,function(t,r){a.push(r)})}var Gl=D;function Bl(a){if(a){for(var e in a)if(a.hasOwnProperty(e))return!0}}function wn(a,e,t){var r={};return Gl(e,function(n){var o=r[n]=i();Gl(a[n],function(s,l){if(yt.isValidType(l)){var u={type:l,visual:s};t&&t(u,n),o[l]=new yt(u),l==="opacity"&&(u=ht(u),u.type="colorAlpha",o.__hidden.__alphaForOpacity=new yt(u))}})}),r;function i(){var n=function(){};n.prototype.__hidden=n.prototype;var o=new n;return o}}function Ev(a,e,t){var r;D(t,function(i){e.hasOwnProperty(i)&&Bl(e[i])&&(r=!0)}),r&&D(t,function(i){e.hasOwnProperty(i)&&Bl(e[i])?a[i]=ht(e[i]):delete a[i]})}function S1(a,e,t,r,i,n){var o={};D(a,function(v){var c=yt.prepareVisualTypes(e[v]);o[v]=c});var s;function l(v){return hh(t,s,v)}function u(v,c){vh(t,s,v,c)}t.each(h);function h(v,c){s=v;var f=t.getRawDataItem(s);if(!(f&&f.visualMap===!1))for(var p=r.call(i,v),d=e[p],g=o[p],y=0,m=g.length;y<m;y++){var S=g[y];d[S]&&d[S].applyVisual(v,l,u)}}}function x1(a,e,t,r){var i={};return D(a,function(n){var o=yt.prepareVisualTypes(e[n]);i[n]=o}),{progress:function(o,s){var l;r!=null&&(l=s.getDimensionIndex(r));function u(b){return hh(s,v,b)}function h(b,_){vh(s,v,b,_)}for(var v,c=s.getStore();(v=o.next())!=null;){var f=s.getRawDataItem(v);if(!(f&&f.visualMap===!1))for(var p=r!=null?c.get(l,v):v,d=t(p),g=e[d],y=i[d],m=0,S=y.length;m<S;m++){var x=y[m];g[x]&&g[x].applyVisual(p,u,h)}}}}}function b1(a){var e=a.brushType,t={point:function(r){return Fl[e].point(r,t,a)},rect:function(r){return Fl[e].rect(r,t,a)}};return t}var Fl={lineX:Hl(0),lineY:Hl(1),rect:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])},rect:function(a,e,t){return a&&t.boundingRect.intersect(a)}},polygon:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])&&Ee(t.range,a[0],a[1])},rect:function(a,e,t){var r=t.range;if(!a||r.length<=1)return!1;var i=a.x,n=a.y,o=a.width,s=a.height,l=r[0];if(Ee(r,i,n)||Ee(r,i+o,n)||Ee(r,i,n+s)||Ee(r,i+o,n+s)||bt.create(a).contain(l[0],l[1])||Ur(i,n,i+o,n,r)||Ur(i,n,i,n+s,r)||Ur(i+o,n,i+o,n+s,r)||Ur(i,n+s,i+o,n+s,r))return!0}}};function Hl(a){var e=["x","y"],t=["width","height"];return{point:function(r,i,n){if(r){var o=n.range,s=r[a];return ur(s,o)}},rect:function(r,i,n){if(r){var o=n.range,s=[r[e[a]],r[e[a]]+r[t[a]]];return s[1]<s[0]&&s.reverse(),ur(s[0],o)||ur(s[1],o)||ur(o[0],s)||ur(o[1],s)}}}}function ur(a,e){return e[0]<=a&&a<=e[1]}var Wl=["inBrush","outOfBrush"],Fi="__ecBrushSelect",An="__ecInBrushSelectEvent";function Vv(a){a.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new xf(e.option,a);t.setInputRanges(e.areas,a)})}function _1(a,e,t){var r=[],i,n;a.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),Vv(a),a.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:ht(o.areas),selected:[]};r.push(l);var u=o.option,h=u.brushLink,v=[],c=[],f=[],p=!1;s||(i=u.throttleType,n=u.throttleDelay);var d=W(o.areas,function(b){var _=D1[b.brushType],w=ft({boundingRect:_?_(b):void 0},b);return w.selectors=b1(w),w}),g=wn(o.option,Wl,function(b){b.mappingMethod="fixed"});H(h)&&D(h,function(b){v[b]=1});function y(b){return h==="all"||!!v[b]}function m(b){return!!b.length}a.eachSeries(function(b,_){var w=f[_]=[];b.subType==="parallel"?S(b,_):x(b,_,w)});function S(b,_){var w=b.coordinateSystem;p=p||w.hasAxisBrushed(),y(_)&&w.eachActiveState(b.getData(),function(T,L){T==="active"&&(c[L]=1)})}function x(b,_,w){if(!(!b.brushSelector||T1(o,_))&&(D(d,function(L){o.brushTargetManager.controlSeries(L,b,a)&&w.push(L),p=p||m(w)}),y(_)&&m(w))){var T=b.getData();T.each(function(L){Zl(b,w,T,L)&&(c[L]=1)})}}a.eachSeries(function(b,_){var w={seriesId:b.id,seriesIndex:_,seriesName:b.name,dataIndex:[]};l.selected.push(w);var T=f[_],L=b.getData(),A=y(_)?function(C){return c[C]?(w.dataIndex.push(L.getRawIndex(C)),"inBrush"):"outOfBrush"}:function(C){return Zl(b,T,L,C)?(w.dataIndex.push(L.getRawIndex(C)),"inBrush"):"outOfBrush"};(y(_)?p:m(T))&&S1(Wl,g,L,A)})}),w1(e,i,n,r,t)}function w1(a,e,t,r,i){if(i){var n=a.getZr();if(!n[An]){n[Fi]||(n[Fi]=A1);var o=Za(n,Fi,t,e);o(a,r)}}}function A1(a,e){if(!a.isDisposed()){var t=a.getZr();t[An]=!0,a.dispatchAction({type:"brushSelect",batch:e}),t[An]=!1}}function Zl(a,e,t,r){for(var i=0,n=e.length;i<n;i++){var o=e[i];if(a.brushSelector(r,t,o.selectors,o))return!0}}function T1(a,e){var t=a.option.seriesIndex;return t!=null&&t!=="all"&&(H(t)?Mt(t,e)<0:e!==t)}var D1={rect:function(a){return Ul(a.range)},polygon:function(a){for(var e,t=a.range,r=0,i=t.length;r<i;r++){e=e||[[1/0,-1/0],[1/0,-1/0]];var n=t[r];n[0]<e[0][0]&&(e[0][0]=n[0]),n[0]>e[0][1]&&(e[0][1]=n[0]),n[1]<e[1][0]&&(e[1][0]=n[1]),n[1]>e[1][1]&&(e[1][1]=n[1])}return e&&Ul(e)}};function Ul(a){return new bt(a[0][0],a[1][0],a[0][1]-a[0][0],a[1][1]-a[1][0])}var L1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r,this.model,(this._brushController=new th(r.getZr())).on("brush",B(this._onBrush,this)).mount()},e.prototype.render=function(t,r,i,n){this.model=t,this._updateController(t,r,i,n)},e.prototype.updateTransform=function(t,r,i,n){Vv(r),this._updateController(t,r,i,n)},e.prototype.updateVisual=function(t,r,i,n){this.updateTransform(t,r,i,n)},e.prototype.updateView=function(t,r,i,n){this._updateController(t,r,i,n)},e.prototype._updateController=function(t,r,i,n){(!n||n.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(i)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var r=this.model.id,i=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:r,areas:ht(i),$from:r}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:r,areas:ht(i),$from:r})},e.type="brush",e}(te),C1="#ddd",I1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,r){var i=this.option;!r&&Ev(i,t,["inBrush","outOfBrush"]);var n=i.inBrush=i.inBrush||{};i.outOfBrush=i.outOfBrush||{color:C1},n.hasOwnProperty("liftZ")||(n.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=W(t,function(r){return Yl(this.option,r)},this))},e.prototype.setBrushOption=function(t){this.brushOption=Yl(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}($t);function Yl(a,e){return zt({brushType:a.brushType,brushMode:a.brushMode,transformable:a.transformable,brushStyle:new Jt(a.brushStyle).getItemStyle(),removeOnClick:a.removeOnClick,z:a.z},e,!0)}var M1=["rect","polygon","lineX","lineY","keep","clear"],P1=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,i){var n,o,s;r.eachComponent({mainType:"brush"},function(l){n=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=n,this._brushMode=o,D(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===n)?"emphasis":"normal")})},e.prototype.updateView=function(t,r,i){this.render(t,r,i)},e.prototype.getIcons=function(){var t=this.model,r=t.get("icon",!0),i={};return D(t.get("type",!0),function(n){r[n]&&(i[n]=r[n])}),i},e.prototype.onclick=function(t,r,i){var n=this._brushType,o=this._brushMode;i==="clear"?(r.dispatchAction({type:"axisAreaSelect",intervals:[]}),r.dispatchAction({type:"brush",command:"clear",areas:[]})):r.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:i==="keep"?n:n===i?!1:i,brushMode:i==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var r={show:!0,type:M1.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return r},e}(bf);function R1(a){a.registerComponentView(L1),a.registerComponentModel(I1),a.registerPreprocessor(y1),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,_1),a.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(r){r.setAreas(e.areas)})}),a.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},Ge),a.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},Ge),_f("brush",P1)}var $l=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode="box",t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),this._initData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){t==null&&(t=this.option.currentIndex);var r=this._data.count();this.option.loop?t=(t%r+r)%r:(t>=r&&(t=r-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t=this.option,r=t.data||[],i=t.axisType,n=this._names=[],o;i==="category"?(o=[],D(r,function(u,h){var v=oe(wf(u),""),c;Ot(u)?(c=ht(u),c.value=h):c=h,o.push(c),n.push(v)})):o=r;var s={category:"ordinal",time:"time",value:"number"}[i]||"number",l=this._data=new kt([{name:"value",type:s}],this);l.initData(o,n)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}($t),kv=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline.slider",e.defaultOption=ar($l.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}($l);ie(kv,Un.prototype);var E1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline",e}(te),V1=function(a){E(e,a);function e(t,r,i,n){var o=a.call(this,t,r,i)||this;return o.type=n||"value",o}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(de),Hi=Math.PI,Xl=Dt(),k1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.api=r},e.prototype.render=function(t,r,i){if(this.model=t,this.api=i,this.ecModel=r,this.group.removeAll(),t.get("show",!0)){var n=this._layout(t,i),o=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(n,t);t.formatTooltip=function(u){var h=l.scale.getLabel({value:u});return Zt("nameValue",{noName:!0,value:h})},D(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](n,o,l,t)},this),this._renderAxisLabel(n,s,l,t),this._position(n,t)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(t,r){var i=t.get(["label","position"]),n=t.get("orient"),o=z1(t,r),s;i==null||i==="auto"?s=n==="horizontal"?o.y+o.height/2<r.getHeight()/2?"-":"+":o.x+o.width/2<r.getWidth()/2?"+":"-":Q(i)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[n][i]:s=i;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},h={horizontal:0,vertical:Hi/2},v=n==="vertical"?o.height:o.width,c=t.getModel("controlStyle"),f=c.get("show",!0),p=f?c.get("itemSize"):0,d=f?c.get("itemGap"):0,g=p+d,y=t.get(["label","rotate"])||0;y=y*Hi/180;var m,S,x,b=c.get("position",!0),_=f&&c.get("showPlayBtn",!0),w=f&&c.get("showPrevBtn",!0),T=f&&c.get("showNextBtn",!0),L=0,A=v;b==="left"||b==="bottom"?(_&&(m=[0,0],L+=g),w&&(S=[L,0],L+=g),T&&(x=[A-p,0],A-=g)):(_&&(m=[A-p,0],A-=g),w&&(S=[0,0],L+=g),T&&(x=[A-p,0],A-=g));var C=[L,A];return t.get("inverse")&&C.reverse(),{viewRect:o,mainLength:v,orient:n,rotation:h[n],labelRotation:y,labelPosOpt:s,labelAlign:t.get(["label","align"])||l[n],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||u[n],playPosition:m,prevBtnPosition:S,nextBtnPosition:x,axisExtent:C,controlSize:p,controlGap:d}},e.prototype._position=function(t,r){var i=this._mainGroup,n=this._labelGroup,o=t.viewRect;if(t.orient==="vertical"){var s=tr(),l=o.x,u=o.y+o.height;Oe(s,s,[-l,-u]),Oa(s,s,-Hi/2),Oe(s,s,[l,u]),o=o.clone(),o.applyTransform(s)}var h=m(o),v=m(i.getBoundingRect()),c=m(n.getBoundingRect()),f=[i.x,i.y],p=[n.x,n.y];p[0]=f[0]=h[0][0];var d=t.labelPosOpt;if(d==null||Q(d)){var g=d==="+"?0:1;S(f,v,h,1,g),S(p,c,h,1,1-g)}else{var g=d>=0?0:1;S(f,v,h,1,g),p[1]=f[1]+d}i.setPosition(f),n.setPosition(p),i.rotation=n.rotation=t.rotation,y(i),y(n);function y(x){x.originX=h[0][0]-x.x,x.originY=h[1][0]-x.y}function m(x){return[[x.x,x.x+x.width],[x.y,x.y+x.height]]}function S(x,b,_,w,T){x[w]+=_[w][T]-b[w][T]}},e.prototype._createAxis=function(t,r){var i=r.getData(),n=r.get("axisType"),o=N1(r,n);o.getTicks=function(){return i.mapArray(["value"],function(u){return{value:u}})};var s=i.getDataExtent("value");o.setExtent(s[0],s[1]),o.calcNiceTicks();var l=new V1("value",o,t.axisExtent,n);return l.model=r,l},e.prototype._createGroup=function(t){var r=this[t]=new $;return this.group.add(r),r},e.prototype._renderAxisLine=function(t,r,i,n){var o=i.getExtent();if(n.get(["lineStyle","show"])){var s=new Kt({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:F({lineCap:"round"},n.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});r.add(s);var l=this._progressLine=new Kt({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:ft({lineCap:"round",lineWidth:s.style.lineWidth},n.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});r.add(l)}},e.prototype._renderAxisTick=function(t,r,i,n){var o=this,s=n.getData(),l=i.scale.getTicks();this._tickSymbols=[],D(l,function(u){var h=i.dataToCoord(u.value),v=s.getItemModel(u.value),c=v.getModel("itemStyle"),f=v.getModel(["emphasis","itemStyle"]),p=v.getModel(["progress","itemStyle"]),d={x:h,y:0,onclick:B(o._changeTimeline,o,u.value)},g=Kl(v,c,r,d);g.ensureState("emphasis").style=f.getItemStyle(),g.ensureState("progress").style=p.getItemStyle(),sa(g);var y=nt(g);v.get("tooltip")?(y.dataIndex=u.value,y.dataModel=n):y.dataIndex=y.dataModel=null,o._tickSymbols.push(g)})},e.prototype._renderAxisLabel=function(t,r,i,n){var o=this,s=i.getLabelModel();if(s.get("show")){var l=n.getData(),u=i.getViewLabels();this._tickLabels=[],D(u,function(h){var v=h.tickValue,c=l.getItemModel(v),f=c.getModel("label"),p=c.getModel(["emphasis","label"]),d=c.getModel(["progress","label"]),g=i.dataToCoord(h.tickValue),y=new ut({x:g,y:0,rotation:t.labelRotation-t.rotation,onclick:B(o._changeTimeline,o,v),silent:!1,style:ct(f,{text:h.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});y.ensureState("emphasis").style=ct(p),y.ensureState("progress").style=ct(d),r.add(y),sa(y),Xl(y).dataIndex=v,o._tickLabels.push(y)})}},e.prototype._renderControl=function(t,r,i,n){var o=t.controlSize,s=t.rotation,l=n.getModel("controlStyle").getItemStyle(),u=n.getModel(["emphasis","controlStyle"]).getItemStyle(),h=n.getPlayState(),v=n.get("inverse",!0);c(t.nextBtnPosition,"next",B(this._changeTimeline,this,v?"-":"+")),c(t.prevBtnPosition,"prev",B(this._changeTimeline,this,v?"+":"-")),c(t.playPosition,h?"stop":"play",B(this._handlePlayClick,this,!h),!0);function c(f,p,d,g){if(f){var y=sn(Tt(n.get(["controlStyle",p+"BtnSize"]),o),o),m=[0,-y/2,y,y],S=O1(n,p+"Icon",m,{x:f[0],y:f[1],originX:o/2,originY:0,rotation:g?-s:0,rectHover:!0,style:l,onclick:d});S.ensureState("emphasis").style=u,r.add(S),sa(S)}}},e.prototype._renderCurrentPointer=function(t,r,i,n){var o=n.getData(),s=n.getCurrentIndex(),l=o.getItemModel(s).getModel("checkpointStyle"),u=this,h={onCreate:function(v){v.draggable=!0,v.drift=B(u._handlePointerDrag,u),v.ondragend=B(u._handlePointerDragend,u),ql(v,u._progressLine,s,i,n,!0)},onUpdate:function(v){ql(v,u._progressLine,s,i,n)}};this._currentPointer=Kl(l,l,this._mainGroup,{},this._currentPointer,h)},e.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},e.prototype._handlePointerDrag=function(t,r,i){this._clearTimer(),this._pointerChangeTimeline([i.offsetX,i.offsetY])},e.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},e.prototype._pointerChangeTimeline=function(t,r){var i=this._toAxisCoord(t)[0],n=this._axis,o=qt(n.getExtent().slice());i>o[1]&&(i=o[1]),i<o[0]&&(i=o[0]),this._currentPointer.x=i,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=i,s.dirty());var l=this._findNearestTick(i),u=this.model;(r||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var r=t.model;t._changeTimeline(r.getCurrentIndex()+(r.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(t){var r=this._mainGroup.getLocalTransform();return xe(t,r,!0)},e.prototype._findNearestTick=function(t){var r=this.model.getData(),i=1/0,n,o=this._axis;return r.each(["value"],function(s,l){var u=o.dataToCoord(s),h=Math.abs(u-t);h<i&&(i=h,n=l)}),n},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(t){var r=this.model.getCurrentIndex();t==="+"?t=r+1:t==="-"&&(t=r-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},e.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),r=this._tickSymbols,i=this._tickLabels;if(r)for(var n=0;n<r.length;n++)r&&r[n]&&r[n].toggleState("progress",n<t);if(i)for(var n=0;n<i.length;n++)i&&i[n]&&i[n].toggleState("progress",Xl(i[n]).dataIndex<=t)},e.type="timeline.slider",e}(E1);function N1(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new Df({ordinalMeta:a.getCategories(),extent:[1/0,-1/0]});case"time":return new Tf({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new Af}}function z1(a,e){return Yt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},a.get("padding"))}function O1(a,e,t,r){var i=r.style,n=Lf(a.get(["controlStyle",e]),r||{},new bt(t[0],t[1],t[2],t[3]));return i&&n.setStyle(i),n}function Kl(a,e,t,r,i,n){var o=e.get("color");if(i)i.setColor(o),t.add(i),n&&n.onUpdate(i);else{var s=a.get("symbol");i=Pt(s,-1,-1,2,2,o),i.setStyle("strokeNoScale",!0),t.add(i),n&&n.onCreate(i)}var l=e.getItemStyle(["color"]);i.setStyle(l),r=zt({rectHover:!0,z2:100},r,!0);var u=Ha(a.get("symbolSize"));r.scaleX=u[0]/2,r.scaleY=u[1]/2;var h=zr(a.get("symbolOffset"),u);h&&(r.x=(r.x||0)+h[0],r.y=(r.y||0)+h[1]);var v=a.get("symbolRotate");return r.rotation=(v||0)*Math.PI/180||0,i.attr(r),i.updateTransform(),i}function ql(a,e,t,r,i,n){if(!a.dragging){var o=i.getModel("checkpointStyle"),s=r.dataToCoord(i.getData().get("value",t));if(n||!o.get("animation",!0))a.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:o.get("animationDuration",!0),easing:o.get("animationEasing",!0)};a.stopAnimation(null,!0),a.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function G1(a){a.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,t,r){var i=t.getComponent("timeline");return i&&e.currentIndex!=null&&(i.setCurrentIndex(e.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.getPlayState()&&(i.setPlayState(!1),r.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),t.resetOption("timeline",{replaceMerge:i.get("replaceMerge",!0)}),ft({currentIndex:i.option.currentIndex},e)}),a.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,t){var r=t.getComponent("timeline");r&&e.playState!=null&&r.setPlayState(e.playState)})}function B1(a){var e=a&&a.timeline;H(e)||(e=e?[e]:[]),D(e,function(t){t&&F1(t)})}function F1(a){var e=a.type,t={number:"value",time:"time"};if(t[e]&&(a.axisType=t[e],delete a.type),jl(a),Ve(a,"controlPosition")){var r=a.controlStyle||(a.controlStyle={});Ve(r,"position")||(r.position=a.controlPosition),r.position==="none"&&!Ve(r,"show")&&(r.show=!1,delete r.position),delete a.controlPosition}D(a.data||[],function(i){Ot(i)&&!H(i)&&(!Ve(i,"value")&&Ve(i,"name")&&(i.value=i.name),jl(i))})}function jl(a){var e=a.itemStyle||(a.itemStyle={}),t=e.emphasis||(e.emphasis={}),r=a.label||a.label||{},i=r.normal||(r.normal={}),n={normal:1,emphasis:1};D(r,function(o,s){!n[s]&&!Ve(i,s)&&(i[s]=o)}),t.label&&!Ve(r,"emphasis")&&(r.emphasis=t.label,delete t.label)}function Ve(a,e){return a.hasOwnProperty(e)}function H1(a){a.registerComponentModel(kv),a.registerComponentView(k1),a.registerSubTypeDefaulter("timeline",function(){return"slider"}),G1(a),a.registerPreprocessor(B1)}function xo(a,e){if(!a)return!1;for(var t=H(a)?a:[a],r=0;r<t.length;r++)if(t[r]&&t[r][e])return!0;return!1}function ra(a){Ba(a,"label",["show"])}var aa=Dt(),fe=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.createdBySelf=!1,t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),this._mergeOption(t,i,!1,!0)},e.prototype.isAnimationEnabled=function(){if(Cf.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,r){this._mergeOption(t,r,!1,!1)},e.prototype._mergeOption=function(t,r,i,n){var o=this.mainType;i||r.eachSeries(function(s){var l=s.get(this.mainType,!0),u=aa(s)[o];if(!l||!l.data){aa(s)[o]=null;return}u?u._mergeOption(l,r,!0):(n&&ra(l),D(l.data,function(h){h instanceof Array?(ra(h[0]),ra(h[1])):ra(h)}),u=this.createMarkerModelFromSeries(l,this,r),F(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),aa(s)[o]=u},this)},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=this.getRawValue(t),s=n.getName(t);return Zt("section",{header:this.name,blocks:[Zt("nameValue",{name:s,value:o,noName:!s,noValue:o==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.prototype.getDataParams=function(t,r){var i=Un.prototype.getDataParams.call(this,t,r),n=this.__hostSeries;return n&&(i.seriesId=n.id,i.seriesName=n.name,i.seriesType=n.subType),i},e.getMarkerModelFromSeries=function(t,r){return aa(t)[r]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}($t);ie(fe,Un.prototype);var W1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(fe);function Tn(a){return!(isNaN(parseFloat(a.x))&&isNaN(parseFloat(a.y)))}function Z1(a){return!isNaN(parseFloat(a.x))&&!isNaN(parseFloat(a.y))}function ia(a,e,t,r,i,n){var o=[],s=sh(e,r),l=s?e.getCalculationInfo("stackResultDimension"):r,u=bo(e,l,a),h=e.indicesOfNearest(l,u)[0];o[i]=e.get(t,h),o[n]=e.get(l,h);var v=e.get(r,h),c=If(e.get(r,h));return c=Math.min(c,20),c>=0&&(o[n]=+o[n].toFixed(c)),[o,v]}var Wi={min:it(ia,"min"),max:it(ia,"max"),average:it(ia,"average"),median:it(ia,"median")};function Mr(a,e){if(e){var t=a.getData(),r=a.coordinateSystem,i=r&&r.dimensions;if(!Z1(e)&&!H(e.coord)&&H(i)){var n=Nv(e,t,r,a);if(e=ht(e),e.type&&Wi[e.type]&&n.baseAxis&&n.valueAxis){var o=Mt(i,n.baseAxis.dim),s=Mt(i,n.valueAxis.dim),l=Wi[e.type](t,n.baseDataDim,n.valueDataDim,o,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!H(i))e.coord=[];else for(var u=e.coord,h=0;h<2;h++)Wi[u[h]]&&(u[h]=bo(t,t.mapDimension(i[h]),u[h]));return e}}function Nv(a,e,t,r){var i={};return a.valueIndex!=null||a.valueDim!=null?(i.valueDataDim=a.valueIndex!=null?e.getDimension(a.valueIndex):a.valueDim,i.valueAxis=t.getAxis(U1(r,i.valueDataDim)),i.baseAxis=t.getOtherAxis(i.valueAxis),i.baseDataDim=e.mapDimension(i.baseAxis.dim)):(i.baseAxis=r.getBaseAxis(),i.valueAxis=t.getOtherAxis(i.baseAxis),i.baseDataDim=e.mapDimension(i.baseAxis.dim),i.valueDataDim=e.mapDimension(i.valueAxis.dim)),i}function U1(a,e){var t=a.getData().getDimensionInfo(e);return t&&t.coordDim}function Pr(a,e){return a&&a.containData&&e.coord&&!Tn(e)?a.containData(e.coord):!0}function Y1(a,e,t){return a&&a.containZone&&e.coord&&t.coord&&!Tn(e)&&!Tn(t)?a.containZone(e.coord,t.coord):!0}function zv(a,e){return a?function(t,r,i,n){var o=n<2?t.coord&&t.coord[n]:t.value;return Sa(o,e[n])}:function(t,r,i,n){return Sa(t.value,e[n])}}function bo(a,e,t){if(t==="average"){var r=0,i=0;return a.each(e,function(n,o){isNaN(n)||(r+=n,i++)}),r/i}else return t==="median"?a.getMedian(e):a.getDataExtent(e)[t==="max"?1:0]}var Zi=Dt(),_o=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this.markerGroupMap=rt()},e.prototype.render=function(t,r,i){var n=this,o=this.markerGroupMap;o.each(function(s){Zi(s).keep=!1}),r.eachSeries(function(s){var l=fe.getMarkerModelFromSeries(s,n.type);l&&n.renderSeries(s,l,r,i)}),o.each(function(s){!Zi(s).keep&&n.group.remove(s.group)})},e.prototype.markKeep=function(t){Zi(t).keep=!0},e.prototype.toggleBlurSeries=function(t,r){var i=this;D(t,function(n){var o=fe.getMarkerModelFromSeries(n,i.type);if(o){var s=o.getData();s.eachItemGraphicEl(function(l){l&&(r?Mf(l):Pf(l))})}})},e.type="marker",e}(te);function Jl(a,e,t){var r=e.coordinateSystem;a.each(function(i){var n=a.getItemModel(i),o,s=O(n.get("x"),t.getWidth()),l=O(n.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(a.getValues(a.dimensions,i));else if(r){var u=a.get(r.dimensions[0],i),h=a.get(r.dimensions[1],i);o=r.dataToPoint([u,h])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),a.setItemLayout(i,o)})}var $1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=fe.getMarkerModelFromSeries(n,"markPoint");o&&(Jl(o.getData(),n,i),this.markerGroupMap.get(n.id).updateLayout())},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,h=u.get(s)||u.set(s,new Na),v=X1(o,t,r);r.setData(v),Jl(r.getData(),t,n),v.each(function(c){var f=v.getItemModel(c),p=f.getShallow("symbol"),d=f.getShallow("symbolSize"),g=f.getShallow("symbolRotate"),y=f.getShallow("symbolOffset"),m=f.getShallow("symbolKeepAspect");if(st(p)||st(d)||st(g)||st(y)){var S=r.getRawValue(c),x=r.getDataParams(c);st(p)&&(p=p(S,x)),st(d)&&(d=d(S,x)),st(g)&&(g=g(S,x)),st(y)&&(y=y(S,x))}var b=f.getModel("itemStyle").getItemStyle(),_=$a(l,"color");b.fill||(b.fill=_),v.setItemVisual(c,{symbol:p,symbolSize:d,symbolRotate:g,symbolOffset:y,symbolKeepAspect:m,style:b})}),h.updateData(v),this.group.add(h.group),v.eachItemGraphicEl(function(c){c.traverse(function(f){nt(f).dataModel=r})}),this.markKeep(h),h.group.silent=r.get("silent")||t.get("silent")},e.type="markPoint",e}(_o);function X1(a,e,t){var r;a?r=W(a&&a.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return F(F({},l),{name:s,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var i=new kt(r,t),n=W(t.get("data"),it(Mr,e));a&&(n=Gt(n,it(Pr,a)));var o=zv(!!a,r);return i.initData(n,null,o),i}function K1(a){a.registerComponentModel(W1),a.registerComponentView($1),a.registerPreprocessor(function(e){xo(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var q1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(fe),na=Dt(),j1=function(a,e,t,r){var i=a.getData(),n;if(H(r))n=r;else{var o=r.type;if(o==="min"||o==="max"||o==="average"||o==="median"||r.xAxis!=null||r.yAxis!=null){var s=void 0,l=void 0;if(r.yAxis!=null||r.xAxis!=null)s=e.getAxis(r.yAxis!=null?"y":"x"),l=Vt(r.yAxis,r.xAxis);else{var u=Nv(r,i,e,a);s=u.valueAxis;var h=Rf(i,u.valueDataDim);l=bo(i,h,o)}var v=s.dim==="x"?0:1,c=1-v,f=ht(r),p={coord:[]};f.type=null,f.coord=[],f.coord[c]=-1/0,p.coord[c]=1/0;var d=t.get("precision");d>=0&&he(l)&&(l=+l.toFixed(Math.min(d,20))),f.coord[v]=p.coord[v]=l,n=[f,p,{type:o,valueIndex:r.valueIndex,value:l}]}else n=[]}var g=[Mr(a,n[0]),Mr(a,n[1]),F({},n[2])];return g[2].type=g[2].type||null,zt(g[2],g[0]),zt(g[2],g[1]),g};function Pa(a){return!isNaN(a)&&!isFinite(a)}function Ql(a,e,t,r){var i=1-a,n=r.dimensions[a];return Pa(e[i])&&Pa(t[i])&&e[a]===t[a]&&r.getAxis(n).containData(e[a])}function J1(a,e){if(a.type==="cartesian2d"){var t=e[0].coord,r=e[1].coord;if(t&&r&&(Ql(1,t,r,a)||Ql(0,t,r,a)))return!0}return Pr(a,e[0])&&Pr(a,e[1])}function Ui(a,e,t,r,i){var n=r.coordinateSystem,o=a.getItemModel(e),s,l=O(o.get("x"),i.getWidth()),u=O(o.get("y"),i.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition)s=r.getMarkerPosition(a.getValues(a.dimensions,e));else{var h=n.dimensions,v=a.get(h[0],e),c=a.get(h[1],e);s=n.dataToPoint([v,c])}if(Ya(n,"cartesian2d")){var f=n.getAxis("x"),p=n.getAxis("y"),h=n.dimensions;Pa(a.get(h[0],e))?s[0]=f.toGlobalCoord(f.getExtent()[t?0:1]):Pa(a.get(h[1],e))&&(s[1]=p.toGlobalCoord(p.getExtent()[t?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}a.setItemLayout(e,s)}var Q1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=fe.getMarkerModelFromSeries(n,"markLine");if(o){var s=o.getData(),l=na(o).from,u=na(o).to;l.each(function(h){Ui(l,h,!0,n,i),Ui(u,h,!1,n,i)}),s.each(function(h){s.setItemLayout(h,[l.getItemLayout(h),u.getItemLayout(h)])}),this.markerGroupMap.get(n.id).updateLayout()}},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,h=u.get(s)||u.set(s,new no);this.group.add(h.group);var v=tx(o,t,r),c=v.from,f=v.to,p=v.line;na(r).from=c,na(r).to=f,r.setData(p);var d=r.get("symbol"),g=r.get("symbolSize"),y=r.get("symbolRotate"),m=r.get("symbolOffset");H(d)||(d=[d,d]),H(g)||(g=[g,g]),H(y)||(y=[y,y]),H(m)||(m=[m,m]),v.from.each(function(x){S(c,x,!0),S(f,x,!1)}),p.each(function(x){var b=p.getItemModel(x).getModel("lineStyle").getLineStyle();p.setItemLayout(x,[c.getItemLayout(x),f.getItemLayout(x)]),b.stroke==null&&(b.stroke=c.getItemVisual(x,"style").fill),p.setItemVisual(x,{fromSymbolKeepAspect:c.getItemVisual(x,"symbolKeepAspect"),fromSymbolOffset:c.getItemVisual(x,"symbolOffset"),fromSymbolRotate:c.getItemVisual(x,"symbolRotate"),fromSymbolSize:c.getItemVisual(x,"symbolSize"),fromSymbol:c.getItemVisual(x,"symbol"),toSymbolKeepAspect:f.getItemVisual(x,"symbolKeepAspect"),toSymbolOffset:f.getItemVisual(x,"symbolOffset"),toSymbolRotate:f.getItemVisual(x,"symbolRotate"),toSymbolSize:f.getItemVisual(x,"symbolSize"),toSymbol:f.getItemVisual(x,"symbol"),style:b})}),h.updateData(p),v.line.eachItemGraphicEl(function(x){nt(x).dataModel=r,x.traverse(function(b){nt(b).dataModel=r})});function S(x,b,_){var w=x.getItemModel(b);Ui(x,b,_,t,n);var T=w.getModel("itemStyle").getItemStyle();T.fill==null&&(T.fill=$a(l,"color")),x.setItemVisual(b,{symbolKeepAspect:w.get("symbolKeepAspect"),symbolOffset:Tt(w.get("symbolOffset",!0),m[_?0:1]),symbolRotate:Tt(w.get("symbolRotate",!0),y[_?0:1]),symbolSize:Tt(w.get("symbolSize"),g[_?0:1]),symbol:Tt(w.get("symbol",!0),d[_?0:1]),style:T})}this.markKeep(h),h.group.silent=r.get("silent")||t.get("silent")},e.type="markLine",e}(_o);function tx(a,e,t){var r;a?r=W(a&&a.dimensions,function(u){var h=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return F(F({},h),{name:u,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var i=new kt(r,t),n=new kt(r,t),o=new kt([],t),s=W(t.get("data"),it(j1,e,a,t));a&&(s=Gt(s,it(J1,a)));var l=zv(!!a,r);return i.initData(W(s,function(u){return u[0]}),null,l),n.initData(W(s,function(u){return u[1]}),null,l),o.initData(W(s,function(u){return u[2]})),o.hasItemOption=!0,{from:i,to:n,line:o}}function ex(a){a.registerComponentModel(q1),a.registerComponentView(Q1),a.registerPreprocessor(function(e){xo(e.series,"markLine")&&(e.markLine=e.markLine||{})})}var rx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(fe),oa=Dt(),ax=function(a,e,t,r){var i=r[0],n=r[1];if(!(!i||!n)){var o=Mr(a,i),s=Mr(a,n),l=o.coord,u=s.coord;l[0]=Vt(l[0],-1/0),l[1]=Vt(l[1],-1/0),u[0]=Vt(u[0],1/0),u[1]=Vt(u[1],1/0);var h=Rn([{},o,s]);return h.coord=[o.coord,s.coord],h.x0=o.x,h.y0=o.y,h.x1=s.x,h.y1=s.y,h}};function Ra(a){return!isNaN(a)&&!isFinite(a)}function tu(a,e,t,r){var i=1-a;return Ra(e[i])&&Ra(t[i])}function ix(a,e){var t=e.coord[0],r=e.coord[1],i={coord:t,x:e.x0,y:e.y0},n={coord:r,x:e.x1,y:e.y1};return Ya(a,"cartesian2d")?t&&r&&(tu(1,t,r)||tu(0,t,r))?!0:Y1(a,i,n):Pr(a,i)||Pr(a,n)}function eu(a,e,t,r,i){var n=r.coordinateSystem,o=a.getItemModel(e),s,l=O(o.get(t[0]),i.getWidth()),u=O(o.get(t[1]),i.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition){var h=a.getValues(["x0","y0"],e),v=a.getValues(["x1","y1"],e),c=n.clampData(h),f=n.clampData(v),p=[];t[0]==="x0"?p[0]=c[0]>f[0]?v[0]:h[0]:p[0]=c[0]>f[0]?h[0]:v[0],t[1]==="y0"?p[1]=c[1]>f[1]?v[1]:h[1]:p[1]=c[1]>f[1]?h[1]:v[1],s=r.getMarkerPosition(p,t,!0)}else{var d=a.get(t[0],e),g=a.get(t[1],e),y=[d,g];n.clampData&&n.clampData(y,y),s=n.dataToPoint(y,!0)}if(Ya(n,"cartesian2d")){var m=n.getAxis("x"),S=n.getAxis("y"),d=a.get(t[0],e),g=a.get(t[1],e);Ra(d)?s[0]=m.toGlobalCoord(m.getExtent()[t[0]==="x0"?0:1]):Ra(g)&&(s[1]=S.toGlobalCoord(S.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var ru=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],nx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=fe.getMarkerModelFromSeries(n,"markArea");if(o){var s=o.getData();s.each(function(l){var u=W(ru,function(v){return eu(s,l,v,n,i)});s.setItemLayout(l,u);var h=s.getItemGraphicEl(l);h.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,h=u.get(s)||u.set(s,{group:new $});this.group.add(h.group),this.markKeep(h);var v=ox(o,t,r);r.setData(v),v.each(function(c){var f=W(ru,function(T){return eu(v,c,T,t,n)}),p=o.getAxis("x").scale,d=o.getAxis("y").scale,g=p.getExtent(),y=d.getExtent(),m=[p.parse(v.get("x0",c)),p.parse(v.get("x1",c))],S=[d.parse(v.get("y0",c)),d.parse(v.get("y1",c))];qt(m),qt(S);var x=!(g[0]>m[1]||g[1]<m[0]||y[0]>S[1]||y[1]<S[0]),b=!x;v.setItemLayout(c,{points:f,allClipped:b});var _=v.getItemModel(c).getModel("itemStyle").getItemStyle(),w=$a(l,"color");_.fill||(_.fill=w,Q(_.fill)&&(_.fill=ga(_.fill,.4))),_.stroke||(_.stroke=w),v.setItemVisual(c,"style",_)}),v.diff(oa(h).data).add(function(c){var f=v.getItemLayout(c);if(!f.allClipped){var p=new _e({shape:{points:f.points}});v.setItemGraphicEl(c,p),h.group.add(p)}}).update(function(c,f){var p=oa(h).data.getItemGraphicEl(f),d=v.getItemLayout(c);d.allClipped?p&&h.group.remove(p):(p?mt(p,{shape:{points:d.points}},r,c):p=new _e({shape:{points:d.points}}),v.setItemGraphicEl(c,p),h.group.add(p))}).remove(function(c){var f=oa(h).data.getItemGraphicEl(c);h.group.remove(f)}).execute(),v.eachItemGraphicEl(function(c,f){var p=v.getItemModel(f),d=v.getItemVisual(f,"style");c.useStyle(v.getItemVisual(f,"style")),jt(c,Nt(p),{labelFetcher:r,labelDataIndex:f,defaultText:v.getName(f)||"",inheritColor:Q(d.fill)?ga(d.fill,1):"#000"}),Qt(c,p),St(c,null,null,p.get(["emphasis","disabled"])),nt(c).dataModel=r}),oa(h).data=v,h.group.silent=r.get("silent")||t.get("silent")},e.type="markArea",e}(_o);function ox(a,e,t){var r,i,n=["x0","y0","x1","y1"];if(a){var o=W(a&&a.dimensions,function(u){var h=e.getData(),v=h.getDimensionInfo(h.mapDimension(u))||{};return F(F({},v),{name:u,ordinalMeta:null})});i=W(n,function(u,h){return{name:u,type:o[h%2].type}}),r=new kt(i,t)}else i=[{name:"value",type:"float"}],r=new kt(i,t);var s=W(t.get("data"),it(ax,e,a,t));a&&(s=Gt(s,it(ix,a)));var l=a?function(u,h,v,c){var f=u.coord[Math.floor(c/2)][c%2];return Sa(f,i[c])}:function(u,h,v,c){return Sa(u.value,i[c])};return r.initData(s,null,l),r.hasItemOption=!0,r}function sx(a){a.registerComponentModel(rx),a.registerComponentView(nx),a.registerPreprocessor(function(e){xo(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var lx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.inside",e.defaultOption=ar(xa.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),e}(xa),wo=Dt();function ux(a,e,t){wo(a).coordSysRecordMap.each(function(r){var i=r.dataZoomInfoMap.get(e.uid);i&&(i.getRange=t)})}function hx(a,e){for(var t=wo(a).coordSysRecordMap,r=t.keys(),i=0;i<r.length;i++){var n=r[i],o=t.get(n),s=o.dataZoomInfoMap;if(s){var l=e.uid,u=s.get(l);u&&(s.removeKey(l),s.keys().length||Ov(t,o))}}}function Ov(a,e){if(e){a.removeKey(e.model.uid);var t=e.controller;t&&t.dispose()}}function vx(a,e){var t={model:e,containsPoint:it(fx,e),dispatchAction:it(cx,a),dataZoomInfoMap:null,controller:null},r=t.controller=new Fr(a.getZr());return D(["pan","zoom","scrollMove"],function(i){r.on(i,function(n){var o=[];t.dataZoomInfoMap.each(function(s){if(n.isAvailableBehavior(s.model.option)){var l=(s.getRange||{})[i],u=l&&l(s.dzReferCoordSysInfo,t.model.mainType,t.controller,n);!s.model.get("disabled",!0)&&u&&o.push({dataZoomId:s.model.id,start:u[0],end:u[1]})}}),o.length&&t.dispatchAction(o)})}),t}function cx(a,e){a.isDisposed()||a.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function fx(a,e,t,r){return a.coordinateSystem.containPoint([t,r])}function px(a){var e,t="type_",r={type_true:2,type_move:1,type_false:0,type_undefined:-1},i=!0;return a.each(function(n){var o=n.model,s=o.get("disabled",!0)?!1:o.get("zoomLock",!0)?"move":!0;r[t+s]>r[t+e]&&(e=s),i=i&&o.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!i}}}function dx(a){a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,function(e,t){var r=wo(t),i=r.coordSysRecordMap||(r.coordSysRecordMap=rt());i.each(function(n){n.dataZoomInfoMap=null}),e.eachComponent({mainType:"dataZoom",subType:"inside"},function(n){var o=ch(n);D(o.infoList,function(s){var l=s.model.uid,u=i.get(l)||i.set(l,vx(t,s.model)),h=u.dataZoomInfoMap||(u.dataZoomInfoMap=rt());h.set(n.uid,{dzReferCoordSysInfo:s,model:n,getRange:null})})}),i.each(function(n){var o=n.controller,s,l=n.dataZoomInfoMap;if(l){var u=l.keys()[0];u!=null&&(s=l.get(u))}if(!s){Ov(i,n);return}var h=px(l);o.enable(h.controlType,h.opt),o.setPointerChecker(n.containsPoint),Za(n,"dispatchAction",s.model.get("throttle",!0),"fixRate")})})}var gx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataZoom.inside",t}return e.prototype.render=function(t,r,i){if(a.prototype.render.apply(this,arguments),t.noTarget()){this._clear();return}this.range=t.getPercentRange(),ux(i,t,{pan:B(Yi.pan,this),zoom:B(Yi.zoom,this),scrollMove:B(Yi.scrollMove,this)})},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){hx(this.api,this.dataZoomModel),this.range=null},e.type="dataZoom.inside",e}(fh),Yi={zoom:function(a,e,t,r){var i=this.range,n=i.slice(),o=a.axisModels[0];if(o){var s=$i[e](null,[r.originX,r.originY],o,t,a),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(n[1]-n[0])+n[0],u=Math.max(1/r.scale,0);n[0]=(n[0]-l)*u+l,n[1]=(n[1]-l)*u+l;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();if(Or(0,n,[0,100],0,h.minSpan,h.maxSpan),this.range=n,i[0]!==n[0]||i[1]!==n[1])return n}},pan:au(function(a,e,t,r,i,n){var o=$i[r]([n.oldX,n.oldY],[n.newX,n.newY],e,i,t);return o.signal*(a[1]-a[0])*o.pixel/o.pixelLength}),scrollMove:au(function(a,e,t,r,i,n){var o=$i[r]([0,0],[n.scrollDelta,n.scrollDelta],e,i,t);return o.signal*(a[1]-a[0])*n.scrollDelta})};function au(a){return function(e,t,r,i){var n=this.range,o=n.slice(),s=e.axisModels[0];if(s){var l=a(o,s,e,t,r,i);if(Or(l,o,[0,100],"all"),this.range=o,n[0]!==o[0]||n[1]!==o[1])return o}}}var $i={grid:function(a,e,t,r,i){var n=t.axis,o={},s=i.model.coordinateSystem.getRect();return a=a||[0,0],n.dim==="x"?(o.pixel=e[0]-a[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=n.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=s.height,o.pixelStart=s.y,o.signal=n.inverse?-1:1),o},polar:function(a,e,t,r,i){var n=t.axis,o={},s=i.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return a=a?s.pointToCoord(a):[0,0],e=s.pointToCoord(e),t.mainType==="radiusAxis"?(o.pixel=e[0]-a[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=n.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=u[1]-u[0],o.pixelStart=u[0],o.signal=n.inverse?-1:1),o},singleAxis:function(a,e,t,r,i){var n=t.axis,o=i.model.coordinateSystem.getRect(),s={};return a=a||[0,0],n.orient==="horizontal"?(s.pixel=e[0]-a[0],s.pixelLength=o.width,s.pixelStart=o.x,s.signal=n.inverse?1:-1):(s.pixel=e[1]-a[1],s.pixelLength=o.height,s.pixelStart=o.y,s.signal=n.inverse?-1:1),s}};function Gv(a){ph(a),a.registerComponentModel(lx),a.registerComponentView(gx),dx(a)}var yx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.slider",e.layoutMode="box",e.defaultOption=ar(xa.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleLabel:{show:!0},handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),e}(xa),hr=gt,iu=7,mx=1,Xi=30,Sx=7,vr="horizontal",nu="vertical",xx=5,bx=["line","bar","candlestick","scatter"],_x={easing:"cubicOut",duration:100,delay:0},wx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._displayables={},t}return e.prototype.init=function(t,r){this.api=r,this._onBrush=B(this._onBrush,this),this._onBrushEnd=B(this._onBrushEnd,this)},e.prototype.render=function(t,r,i,n){if(a.prototype.render.apply(this,arguments),Za(this,"_dispatchZoomAction",t.get("throttle"),"fixRate"),this._orient=t.getOrient(),t.get("show")===!1){this.group.removeAll();return}if(t.noTarget()){this._clear(),this.group.removeAll();return}(!n||n.type!=="dataZoom"||n.from!==this.uid)&&this._buildView(),this._updateView()},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){Qu(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},e.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var r=this._displayables.sliderGroup=new $;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(r),this._positionGroup()},e.prototype._resetLocation=function(){var t=this.dataZoomModel,r=this.api,i=t.get("brushSelect"),n=i?Sx:0,o=this._findCoordRect(),s={width:r.getWidth(),height:r.getHeight()},l=this._orient===vr?{right:s.width-o.x-o.width,top:s.height-Xi-iu-n,width:o.width,height:Xi}:{right:iu,top:o.y,width:Xi,height:o.height},u=lh(t.option);D(["right","top","width","height"],function(v){u[v]==="ph"&&(u[v]=l[v])});var h=Yt(u,s);this._location={x:h.x,y:h.y},this._size=[h.width,h.height],this._orient===nu&&this._size.reverse()},e.prototype._positionGroup=function(){var t=this.group,r=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),o=n&&n.get("inverse"),s=this._displayables.sliderGroup,l=(this._dataShadowInfo||{}).otherAxisInverse;s.attr(i===vr&&!o?{scaleY:l?1:-1,scaleX:1}:i===vr&&o?{scaleY:l?1:-1,scaleX:-1}:i===nu&&!o?{scaleY:l?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:l?-1:1,scaleX:-1,rotation:Math.PI/2});var u=t.getBoundingRect([s]);t.x=r.x-u.x,t.y=r.y-u.y,t.markRedraw()},e.prototype._getViewExtent=function(){return[0,this._size[0]]},e.prototype._renderBackground=function(){var t=this.dataZoomModel,r=this._size,i=this._displayables.sliderGroup,n=t.get("brushSelect");i.add(new hr({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new hr({shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:"transparent"},z2:0,onclick:B(this._onClickPanel,this)}),s=this.api.getZr();n?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",s.on("mousemove",this._onBrush),s.on("mouseup",this._onBrushEnd)):(s.off("mousemove",this._onBrush),s.off("mouseup",this._onBrushEnd)),i.add(o)},e.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],!t)return;var r=this._size,i=this._shadowSize||[],n=t.series,o=n.getRawData(),s=n.getShadowDim&&n.getShadowDim(),l=s&&o.getDimensionInfo(s)?n.getShadowDim():t.otherDim;if(l==null)return;var u=this._shadowPolygonPts,h=this._shadowPolylinePts;if(o!==this._shadowData||l!==this._shadowDim||r[0]!==i[0]||r[1]!==i[1]){var v=o.getDataExtent(l),c=(v[1]-v[0])*.3;v=[v[0]-c,v[1]+c];var f=[0,r[1]],p=[0,r[0]],d=[[r[0],0],[0,0]],g=[],y=p[1]/(o.count()-1),m=0,S=Math.round(o.count()/r[0]),x;o.each([l],function(L,A){if(S>0&&A%S){m+=y;return}var C=L==null||isNaN(L)||L==="",I=C?0:at(L,v,f,!0);C&&!x&&A?(d.push([d[d.length-1][0],0]),g.push([g[g.length-1][0],0])):!C&&x&&(d.push([m,0]),g.push([m,0])),d.push([m,I]),g.push([m,I]),m+=y,x=C}),u=this._shadowPolygonPts=d,h=this._shadowPolylinePts=g}this._shadowData=o,this._shadowDim=l,this._shadowSize=[r[0],r[1]];var b=this.dataZoomModel;function _(L){var A=b.getModel(L?"selectedDataBackground":"dataBackground"),C=new $,I=new _e({shape:{points:u},segmentIgnoreThreshold:1,style:A.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),M=new Be({shape:{points:h},segmentIgnoreThreshold:1,style:A.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19});return C.add(I),C.add(M),C}for(var w=0;w<3;w++){var T=_(w===1);this._displayables.sliderGroup.add(T),this._displayables.dataShadowSegs.push(T)}},e.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,r=t.get("showDataShadow");if(r!==!1){var i,n=this.ecModel;return t.eachTargetAxis(function(o,s){var l=t.getAxisProxy(o,s).getTargetSeriesModels();D(l,function(u){if(!i&&!(r!==!0&&Mt(bx,u.get("type"))<0)){var h=n.getComponent(Ef(o),s).axis,v=Ax(o),c,f=u.coordinateSystem;v!=null&&f.getOtherAxis&&(c=f.getOtherAxis(h).inverse),v=u.getData().mapDimension(v),i={thisAxis:h,series:u,thisDim:o,otherDim:v,otherAxisInverse:c}}},this)},this),i}},e.prototype._renderHandle=function(){var t=this.group,r=this._displayables,i=r.handles=[null,null],n=r.handleLabels=[null,null],o=this._displayables.sliderGroup,s=this._size,l=this.dataZoomModel,u=this.api,h=l.get("borderRadius")||0,v=l.get("brushSelect"),c=r.filler=new hr({silent:v,style:{fill:l.get("fillerColor")},textConfig:{position:"inside"}});o.add(c),o.add(new hr({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:s[0],height:s[1],r:h},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:mx,fill:"rgba(0,0,0,0)"}})),D([0,1],function(S){var x=l.get("handleIcon");!Vf[x]&&x.indexOf("path://")<0&&x.indexOf("image://")<0&&(x="path://"+x);var b=Pt(x,-1,0,2,2,null,!0);b.attr({cursor:ou(this._orient),draggable:!0,drift:B(this._onDragMove,this,S),ondragend:B(this._onDragEnd,this),onmouseover:B(this._showDataInfo,this,!0),onmouseout:B(this._showDataInfo,this,!1),z2:5});var _=b.getBoundingRect(),w=l.get("handleSize");this._handleHeight=O(w,this._size[1]),this._handleWidth=_.width/_.height*this._handleHeight,b.setStyle(l.getModel("handleStyle").getItemStyle()),b.style.strokeNoScale=!0,b.rectHover=!0,b.ensureState("emphasis").style=l.getModel(["emphasis","handleStyle"]).getItemStyle(),sa(b);var T=l.get("handleColor");T!=null&&(b.style.fill=T),o.add(i[S]=b);var L=l.getModel("textStyle"),A=l.get("handleLabel")||{},C=A.show||!1;t.add(n[S]=new ut({silent:!0,invisible:!C,style:ct(L,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:L.getTextColor(),font:L.getFont()}),z2:10}))},this);var f=c;if(v){var p=O(l.get("moveHandleSize"),s[1]),d=r.moveHandle=new gt({style:l.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:s[1]-.5,height:p}}),g=p*.8,y=r.moveHandleIcon=Pt(l.get("moveHandleIcon"),-g/2,-g/2,g,g,"#fff",!0);y.silent=!0,y.y=s[1]+p/2-.5,d.ensureState("emphasis").style=l.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var m=Math.min(s[1]/2,Math.max(p,10));f=r.moveZone=new gt({invisible:!0,shape:{y:s[1]-m,height:p+m}}),f.on("mouseover",function(){u.enterEmphasis(d)}).on("mouseout",function(){u.leaveEmphasis(d)}),o.add(d),o.add(y),o.add(f)}f.attr({draggable:!0,cursor:ou(this._orient),drift:B(this._onDragMove,this,"all"),ondragstart:B(this._showDataInfo,this,!0),ondragend:B(this._onDragEnd,this),onmouseover:B(this._showDataInfo,this,!0),onmouseout:B(this._showDataInfo,this,!1)})},e.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),r=this._getViewExtent();this._handleEnds=[at(t[0],[0,100],r,!0),at(t[1],[0,100],r,!0)]},e.prototype._updateInterval=function(t,r){var i=this.dataZoomModel,n=this._handleEnds,o=this._getViewExtent(),s=i.findRepresentativeAxisProxy().getMinMaxSpan(),l=[0,100];Or(r,n,o,i.get("zoomLock")?"all":t,s.minSpan!=null?at(s.minSpan,l,o,!0):null,s.maxSpan!=null?at(s.maxSpan,l,o,!0):null);var u=this._range,h=this._range=qt([at(n[0],o,l,!0),at(n[1],o,l,!0)]);return!u||u[0]!==h[0]||u[1]!==h[1]},e.prototype._updateView=function(t){var r=this._displayables,i=this._handleEnds,n=qt(i.slice()),o=this._size;D([0,1],function(f){var p=r.handles[f],d=this._handleHeight;p.attr({scaleX:d/2,scaleY:d/2,x:i[f]+(f?-1:1),y:o[1]/2-d/2})},this),r.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:o[1]});var s={x:n[0],width:n[1]-n[0]};r.moveHandle&&(r.moveHandle.setShape(s),r.moveZone.setShape(s),r.moveZone.getBoundingRect(),r.moveHandleIcon&&r.moveHandleIcon.attr("x",s.x+s.width/2));for(var l=r.dataShadowSegs,u=[0,n[0],n[1],o[0]],h=0;h<l.length;h++){var v=l[h],c=v.getClipPath();c||(c=new gt,v.setClipPath(c)),c.setShape({x:u[h],y:0,width:u[h+1]-u[h],height:o[1]})}this._updateDataInfo(t)},e.prototype._updateDataInfo=function(t){var r=this.dataZoomModel,i=this._displayables,n=i.handleLabels,o=this._orient,s=["",""];if(r.get("showDetail")){var l=r.findRepresentativeAxisProxy();if(l){var u=l.getAxisModel().axis,h=this._range,v=t?l.calculateDataWindow({start:h[0],end:h[1]}).valueWindow:l.getDataValueWindow();s=[this._formatLabel(v[0],u),this._formatLabel(v[1],u)]}}var c=qt(this._handleEnds.slice());f.call(this,0),f.call(this,1);function f(p){var d=la(i.handles[p].parent,this.group),g=dh(p===0?"right":"left",d),y=this._handleWidth/2+xx,m=xe([c[p]+(p===0?-y:y),this._size[1]/2],d);n[p].setStyle({x:m[0],y:m[1],verticalAlign:o===vr?"middle":g,align:o===vr?g:"center",text:s[p]})}},e.prototype._formatLabel=function(t,r){var i=this.dataZoomModel,n=i.get("labelFormatter"),o=i.get("labelPrecision");(o==null||o==="auto")&&(o=r.getPixelPrecision());var s=t==null||isNaN(t)?"":r.type==="category"||r.type==="time"?r.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(o,20));return st(n)?n(t,s):Q(n)?n.replace("{value}",s):s},e.prototype._showDataInfo=function(t){var r=this.dataZoomModel.get("handleLabel")||{},i=r.show||!1,n=this.dataZoomModel.getModel(["emphasis","handleLabel"]),o=n.get("show")||!1,s=t||this._dragging?o:i,l=this._displayables,u=l.handleLabels;u[0].attr("invisible",!s),u[1].attr("invisible",!s),l.moveHandle&&this.api[s?"enterEmphasis":"leaveEmphasis"](l.moveHandle,1)},e.prototype._onDragMove=function(t,r,i,n){this._dragging=!0,ze(n.event);var o=this._displayables.sliderGroup.getLocalTransform(),s=xe([r,i],o,!0),l=this._updateInterval(t,s[0]),u=this.dataZoomModel.get("realtime");this._updateView(!u),l&&u&&this._dispatchZoomAction(!0)},e.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction(!1)},e.prototype._onClickPanel=function(t){var r=this._size,i=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(i[0]<0||i[0]>r[0]||i[1]<0||i[1]>r[1])){var n=this._handleEnds,o=(n[0]+n[1])/2,s=this._updateInterval("all",i[0]-o);this._updateView(),s&&this._dispatchZoomAction(!1)}},e.prototype._onBrushStart=function(t){var r=t.offsetX,i=t.offsetY;this._brushStart=new Ju(r,i),this._brushing=!0,this._brushStartTime=+new Date},e.prototype._onBrushEnd=function(t){if(this._brushing){var r=this._displayables.brushRect;if(this._brushing=!1,!!r){r.attr("ignore",!0);var i=r.shape,n=+new Date;if(!(n-this._brushStartTime<200&&Math.abs(i.width)<5)){var o=this._getViewExtent(),s=[0,100];this._range=qt([at(i.x,o,s,!0),at(i.x+i.width,o,s,!0)]),this._handleEnds=[i.x,i.x+i.width],this._updateView(),this._dispatchZoomAction(!1)}}}},e.prototype._onBrush=function(t){this._brushing&&(ze(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},e.prototype._updateBrushRect=function(t,r){var i=this._displayables,n=this.dataZoomModel,o=i.brushRect;o||(o=i.brushRect=new hr({silent:!0,style:n.getModel("brushStyle").getItemStyle()}),i.sliderGroup.add(o)),o.attr("ignore",!1);var s=this._brushStart,l=this._displayables.sliderGroup,u=l.transformCoordToLocal(t,r),h=l.transformCoordToLocal(s.x,s.y),v=this._size;u[0]=Math.max(Math.min(v[0],u[0]),0),o.setShape({x:h[0],y:0,width:u[0]-h[0],height:v[1]})},e.prototype._dispatchZoomAction=function(t){var r=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?_x:null,start:r[0],end:r[1]})},e.prototype._findCoordRect=function(){var t,r=ch(this.dataZoomModel).infoList;if(!t&&r.length){var i=r[0].model.coordinateSystem;t=i.getRect&&i.getRect()}if(!t){var n=this.api.getWidth(),o=this.api.getHeight();t={x:n*.2,y:o*.2,width:n*.6,height:o*.6}}return t},e.type="dataZoom.slider",e}(fh);function Ax(a){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[a]}function ou(a){return a==="vertical"?"ns-resize":"ew-resize"}function Bv(a){a.registerComponentModel(yx),a.registerComponentView(wx),ph(a)}function Tx(a){X(Gv),X(Bv)}var Fv={get:function(a,e,t){var r=ht((Dx[a]||{})[e]);return t&&H(r)?r[r.length-1]:r}},Dx={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},su=yt.mapVisual,Lx=yt.eachVisual,Cx=H,lu=D,Ix=qt,Mx=at,Ea=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.stateList=["inRange","outOfRange"],t.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],t.layoutMode={type:"box",ignoreSize:!0},t.dataBound=[-1/0,1/0],t.targetVisuals={},t.controllerVisuals={},t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i)},e.prototype.optionUpdated=function(t,r){var i=this.option;!r&&Ev(i,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var r=this.stateList;t=B(t,this),this.controllerVisuals=wn(this.option.controller,r,t),this.targetVisuals=wn(this.option.target,r,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,r=[];return t==null||t==="all"?this.ecModel.eachSeries(function(i,n){r.push(n)}):r=ue(t),r},e.prototype.eachTargetSeries=function(t,r){D(this.getTargetSeriesIndices(),function(i){var n=this.ecModel.getSeriesByIndex(i);n&&t.call(r,n)},this)},e.prototype.isTargetSeries=function(t){var r=!1;return this.eachTargetSeries(function(i){i===t&&(r=!0)}),r},e.prototype.formatValueText=function(t,r,i){var n=this.option,o=n.precision,s=this.dataBound,l=n.formatter,u;i=i||["<",">"],H(t)&&(t=t.slice(),u=!0);var h=r?t:u?[v(t[0]),v(t[1])]:v(t);if(Q(l))return l.replace("{value}",u?h[0]:h).replace("{value2}",u?h[1]:h);if(st(l))return u?l(t[0],t[1]):l(t);if(u)return t[0]===s[0]?i[0]+" "+h[1]:t[1]===s[1]?i[1]+" "+h[0]:h[0]+" - "+h[1];return h;function v(c){return c===s[0]?"min":c===s[1]?"max":(+c).toFixed(Math.min(o,20))}},e.prototype.resetExtent=function(){var t=this.option,r=Ix([t.min,t.max]);this._dataExtent=r},e.prototype.getDataDimensionIndex=function(t){var r=this.option.dimension;if(r!=null)return t.getDimensionIndex(r);for(var i=t.dimensions,n=i.length-1;n>=0;n--){var o=i[n],s=t.getDimensionInfo(o);if(!s.isCalculationCoord)return s.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,r=this.option,i={inRange:r.inRange,outOfRange:r.outOfRange},n=r.target||(r.target={}),o=r.controller||(r.controller={});zt(n,i),zt(o,i);var s=this.isCategory();l.call(this,n),l.call(this,o),u.call(this,n,"inRange","outOfRange"),h.call(this,o);function l(v){Cx(r.color)&&!v.inRange&&(v.inRange={color:r.color.slice().reverse()}),v.inRange=v.inRange||{color:t.get("gradientColor")}}function u(v,c,f){var p=v[c],d=v[f];p&&!d&&(d=v[f]={},lu(p,function(g,y){if(yt.isValidType(y)){var m=Fv.get(y,"inactive",s);m!=null&&(d[y]=m,y==="color"&&!d.hasOwnProperty("opacity")&&!d.hasOwnProperty("colorAlpha")&&(d.opacity=[0,0]))}}))}function h(v){var c=(v.inRange||{}).symbol||(v.outOfRange||{}).symbol,f=(v.inRange||{}).symbolSize||(v.outOfRange||{}).symbolSize,p=this.get("inactiveColor"),d=this.getItemSymbol(),g=d||"roundRect";lu(this.stateList,function(y){var m=this.itemSize,S=v[y];S||(S=v[y]={color:s?p:[p]}),S.symbol==null&&(S.symbol=c&&ht(c)||(s?g:[g])),S.symbolSize==null&&(S.symbolSize=f&&ht(f)||(s?m[0]:[m[0],m[0]])),S.symbol=su(S.symbol,function(_){return _==="none"?g:_});var x=S.symbolSize;if(x!=null){var b=-1/0;Lx(x,function(_){_>b&&(b=_)}),S.symbolSize=su(x,function(_){return Mx(_,[0,b],[0,m[0]],!0)})}},this)}},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}($t),uu=[20,140],Px=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual(function(i){i.mappingMethod="linear",i.dataExtent=this.getExtent()}),this._resetRange()},e.prototype.resetItemSize=function(){a.prototype.resetItemSize.apply(this,arguments);var t=this.itemSize;(t[0]==null||isNaN(t[0]))&&(t[0]=uu[0]),(t[1]==null||isNaN(t[1]))&&(t[1]=uu[1])},e.prototype._resetRange=function(){var t=this.getExtent(),r=this.option.range;!r||r.auto?(t.auto=1,this.option.range=t):H(r)&&(r[0]>r[1]&&r.reverse(),r[0]=Math.max(r[0],t[0]),r[1]=Math.min(r[1],t[1]))},e.prototype.completeVisualOption=function(){a.prototype.completeVisualOption.apply(this,arguments),D(this.stateList,function(t){var r=this.option.controller[t].symbolSize;r&&r[0]!==r[1]&&(r[0]=r[1]/3)},this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),r=qt((this.get("range")||[]).slice());return r[0]>t[1]&&(r[0]=t[1]),r[1]>t[1]&&(r[1]=t[1]),r[0]<t[0]&&(r[0]=t[0]),r[1]<t[0]&&(r[1]=t[0]),r},e.prototype.getValueState=function(t){var r=this.option.range,i=this.getExtent();return(r[0]<=i[0]||r[0]<=t)&&(r[1]>=i[1]||t<=r[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[];return this.eachTargetSeries(function(i){var n=[],o=i.getData();o.each(this.getDataDimensionIndex(o),function(s,l){t[0]<=s&&s<=t[1]&&n.push(l)},this),r.push({seriesId:i.id,dataIndex:n})},this),r},e.prototype.getVisualMeta=function(t){var r=hu(this,"outOfRange",this.getExtent()),i=hu(this,"inRange",this.option.range.slice()),n=[];function o(f,p){n.push({value:f,color:t(f,p)})}for(var s=0,l=0,u=i.length,h=r.length;l<h&&(!i.length||r[l]<=i[0]);l++)r[l]<i[s]&&o(r[l],"outOfRange");for(var v=1;s<u;s++,v=0)v&&n.length&&o(i[s],"outOfRange"),o(i[s],"inRange");for(var v=1;l<h;l++)(!i.length||i[i.length-1]<r[l])&&(v&&(n.length&&o(n[n.length-1].value,"outOfRange"),v=0),o(r[l],"outOfRange"));var c=n.length;return{stops:n,outerColors:[c?n[0].color:"transparent",c?n[c-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=ar(Ea.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(Ea);function hu(a,e,t){if(t[0]===t[1])return t.slice();for(var r=200,i=(t[1]-t[0])/r,n=t[0],o=[],s=0;s<=r&&n<t[1];s++)o.push(n),n+=i;return o.push(t[1]),o}var Hv=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.autoPositionValues={left:1,right:1,top:1,bottom:1},t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r},e.prototype.render=function(t,r,i,n){if(this.visualMapModel=t,t.get("show")===!1){this.group.removeAll();return}this.doRender(t,r,i,n)},e.prototype.renderBackground=function(t){var r=this.visualMapModel,i=kf(r.get("padding")||0),n=t.getBoundingRect();t.add(new gt({z2:-1,silent:!0,shape:{x:n.x-i[3],y:n.y-i[0],width:n.width+i[3]+i[1],height:n.height+i[0]+i[2]},style:{fill:r.get("backgroundColor"),stroke:r.get("borderColor"),lineWidth:r.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,r,i){i=i||{};var n=i.forceState,o=this.visualMapModel,s={};if(r==="color"){var l=o.get("contentColor");s.color=l}function u(f){return s[f]}function h(f,p){s[f]=p}var v=o.controllerVisuals[n||o.getValueState(t)],c=yt.prepareVisualTypes(v);return D(c,function(f){var p=v[f];i.convertOpacityToAlpha&&f==="opacity"&&(f="colorAlpha",p=v.__alphaForOpacity),yt.dependsOn(f,r)&&p&&p.applyVisual(t,u,h)}),s[r]},e.prototype.positionGroup=function(t){var r=this.visualMapModel,i=this.api;Vn(t,r.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})},e.prototype.doRender=function(t,r,i,n){},e.type="visualMap",e}(te),vu=[["left","right","width"],["top","bottom","height"]];function Wv(a,e,t){var r=a.option,i=r.align;if(i!=null&&i!=="auto")return i;for(var n={width:e.getWidth(),height:e.getHeight()},o=r.orient==="horizontal"?1:0,s=vu[o],l=[0,null,10],u={},h=0;h<3;h++)u[vu[1-o][h]]=l[h],u[s[h]]=h===2?t[0]:r[s[h]];var v=[["x","width",3],["y","height",0]][o],c=Yt(u,n,r.padding);return s[(c.margin[v[2]]||0)+c[v[0]]+c[v[1]]*.5<n[v[1]]*.5?0:1]}function fa(a,e){return D(a||[],function(t){t.dataIndex!=null&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")}),a}var re=at,Rx=D,cu=Math.min,Ki=Math.max,Ex=12,Vx=6,kx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._shapes={},t._dataInterval=[],t._handleEnds=[],t._hoverLinkDataIndices=[],t}return e.prototype.init=function(t,r){a.prototype.init.call(this,t,r),this._hoverLinkFromSeriesMouseOver=B(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=B(this._hideIndicator,this)},e.prototype.doRender=function(t,r,i,n){(!n||n.type!=="selectDataRange"||n.from!==this.uid)&&this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,r=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(r);var i=t.get("text");this._renderEndsText(r,i,0),this._renderEndsText(r,i,1),this._updateView(!0),this.renderBackground(r),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(r)},e.prototype._renderEndsText=function(t,r,i){if(r){var n=r[1-i];n=n!=null?n+"":"";var o=this.visualMapModel,s=o.get("textGap"),l=o.itemSize,u=this._shapes.mainGroup,h=this._applyTransform([l[0]/2,i===0?-s:l[1]+s],u),v=this._applyTransform(i===0?"bottom":"top",u),c=this._orient,f=this.visualMapModel.textStyleModel;this.group.add(new ut({style:ct(f,{x:h[0],y:h[1],verticalAlign:c==="horizontal"?"middle":v,align:c==="horizontal"?v:"center",text:n})}))}},e.prototype._renderBar=function(t){var r=this.visualMapModel,i=this._shapes,n=r.itemSize,o=this._orient,s=this._useHandle,l=Wv(r,this.api,n),u=i.mainGroup=this._createBarGroup(l),h=new $;u.add(h),h.add(i.outOfRange=fu()),h.add(i.inRange=fu(null,s?du(this._orient):null,B(this._dragHandle,this,"all",!1),B(this._dragHandle,this,"all",!0))),h.setClipPath(new gt({shape:{x:0,y:0,width:n[0],height:n[1],r:3}}));var v=r.textStyleModel.getTextRect("国"),c=Ki(v.width,v.height);s&&(i.handleThumbs=[],i.handleLabels=[],i.handleLabelPoints=[],this._createHandle(r,u,0,n,c,o),this._createHandle(r,u,1,n,c,o)),this._createIndicator(r,u,n,c,o),t.add(u)},e.prototype._createHandle=function(t,r,i,n,o,s){var l=B(this._dragHandle,this,i,!1),u=B(this._dragHandle,this,i,!0),h=sn(t.get("handleSize"),n[0]),v=Pt(t.get("handleIcon"),-h/2,-h/2,h,h,null,!0),c=du(this._orient);v.attr({cursor:c,draggable:!0,drift:l,ondragend:u,onmousemove:function(y){ze(y.event)}}),v.x=n[0]/2,v.useStyle(t.getModel("handleStyle").getItemStyle()),v.setStyle({strokeNoScale:!0,strokeFirst:!0}),v.style.lineWidth*=2,v.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),cr(v,!0),r.add(v);var f=this.visualMapModel.textStyleModel,p=new ut({cursor:c,draggable:!0,drift:l,onmousemove:function(y){ze(y.event)},ondragend:u,style:ct(f,{x:0,y:0,text:""})});p.ensureState("blur").style={opacity:.1},p.stateTransition={duration:200},this.group.add(p);var d=[h,0],g=this._shapes;g.handleThumbs[i]=v,g.handleLabelPoints[i]=d,g.handleLabels[i]=p},e.prototype._createIndicator=function(t,r,i,n,o){var s=sn(t.get("indicatorSize"),i[0]),l=Pt(t.get("indicatorIcon"),-s/2,-s/2,s,s,null,!0);l.attr({cursor:"move",invisible:!0,silent:!0,x:i[0]/2});var u=t.getModel("indicatorStyle").getItemStyle();if(l instanceof pe){var h=l.style;l.useStyle(F({image:h.image,x:h.x,y:h.y,width:h.width,height:h.height},u))}else l.useStyle(u);r.add(l);var v=this.visualMapModel.textStyleModel,c=new ut({silent:!0,invisible:!0,style:ct(v,{x:0,y:0,text:""})});this.group.add(c);var f=[(o==="horizontal"?n/2:Vx)+i[0]/2,0],p=this._shapes;p.indicator=l,p.indicatorLabel=c,p.indicatorLabelPoint=f,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,r,i,n){if(this._useHandle){if(this._dragging=!r,!r){var o=this._applyTransform([i,n],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}r===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),r?!this._hovering&&this._clearHoverLinkToSeries():pu(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,r=this._dataInterval=t.getSelected(),i=t.getExtent(),n=[0,t.itemSize[1]];this._handleEnds=[re(r[0],i,n,!0),re(r[1],i,n,!0)]},e.prototype._updateInterval=function(t,r){r=r||0;var i=this.visualMapModel,n=this._handleEnds,o=[0,i.itemSize[1]];Or(r,n,o,t,0);var s=i.getExtent();this._dataInterval=[re(n[0],o,s,!0),re(n[1],o,s,!0)]},e.prototype._updateView=function(t){var r=this.visualMapModel,i=r.getExtent(),n=this._shapes,o=[0,r.itemSize[1]],s=t?o:this._handleEnds,l=this._createBarVisual(this._dataInterval,i,s,"inRange"),u=this._createBarVisual(i,i,o,"outOfRange");n.inRange.setStyle({fill:l.barColor}).setShape("points",l.barPoints),n.outOfRange.setStyle({fill:u.barColor}).setShape("points",u.barPoints),this._updateHandle(s,l)},e.prototype._createBarVisual=function(t,r,i,n){var o={forceState:n,convertOpacityToAlpha:!0},s=this._makeColorGradient(t,o),l=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],u=this._createBarPoints(i,l);return{barColor:new In(0,0,0,1,s),barPoints:u,handlesColor:[s[0].color,s[s.length-1].color]}},e.prototype._makeColorGradient=function(t,r){var i=100,n=[],o=(t[1]-t[0])/i;n.push({color:this.getControllerVisual(t[0],"color",r),offset:0});for(var s=1;s<i;s++){var l=t[0]+o*s;if(l>t[1])break;n.push({color:this.getControllerVisual(l,"color",r),offset:s/i})}return n.push({color:this.getControllerVisual(t[1],"color",r),offset:1}),n},e.prototype._createBarPoints=function(t,r){var i=this.visualMapModel.itemSize;return[[i[0]-r[0],t[0]],[i[0],t[0]],[i[0],t[1]],[i[0]-r[1],t[1]]]},e.prototype._createBarGroup=function(t){var r=this._orient,i=this.visualMapModel.get("inverse");return new $(r==="horizontal"&&!i?{scaleX:t==="bottom"?1:-1,rotation:Math.PI/2}:r==="horizontal"&&i?{scaleX:t==="bottom"?-1:1,rotation:-Math.PI/2}:r==="vertical"&&!i?{scaleX:t==="left"?1:-1,scaleY:-1}:{scaleX:t==="left"?1:-1})},e.prototype._updateHandle=function(t,r){if(this._useHandle){var i=this._shapes,n=this.visualMapModel,o=i.handleThumbs,s=i.handleLabels,l=n.itemSize,u=n.getExtent(),h=this._applyTransform("left",i.mainGroup);Rx([0,1],function(v){var c=o[v];c.setStyle("fill",r.handlesColor[v]),c.y=t[v];var f=re(t[v],[0,l[1]],u,!0),p=this.getControllerVisual(f,"symbolSize");c.scaleX=c.scaleY=p/l[0],c.x=l[0]-p/2;var d=xe(i.handleLabelPoints[v],la(c,this.group));if(this._orient==="horizontal"){var g=h==="left"||h==="top"?(l[0]-p)/2:(l[0]-p)/-2;d[1]+=g}s[v].setStyle({x:d[0],y:d[1],text:n.formatValueText(this._dataInterval[v]),verticalAlign:"middle",align:this._orient==="vertical"?this._applyTransform("left",i.mainGroup):"center"})},this)}},e.prototype._showIndicator=function(t,r,i,n){var o=this.visualMapModel,s=o.getExtent(),l=o.itemSize,u=[0,l[1]],h=this._shapes,v=h.indicator;if(v){v.attr("invisible",!1);var c={convertOpacityToAlpha:!0},f=this.getControllerVisual(t,"color",c),p=this.getControllerVisual(t,"symbolSize"),d=re(t,s,u,!0),g=l[0]-p/2,y={x:v.x,y:v.y};v.y=d,v.x=g;var m=xe(h.indicatorLabelPoint,la(v,this.group)),S=h.indicatorLabel;S.attr("invisible",!1);var x=this._applyTransform("left",h.mainGroup),b=this._orient,_=b==="horizontal";S.setStyle({text:(i||"")+o.formatValueText(r),verticalAlign:_?x:"middle",align:_?"center":x});var w={x:g,y:d,style:{fill:f}},T={style:{x:m[0],y:m[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var L={duration:100,easing:"cubicInOut",additive:!0};v.x=y.x,v.y=y.y,v.animateTo(w,L),S.animateTo(T,L)}else v.attr(w),S.attr(T);this._firstShowIndicator=!1;var A=this._shapes.handleLabels;if(A)for(var C=0;C<A.length;C++)this.api.enterBlur(A[C])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",function(r){if(t._hovering=!0,!t._dragging){var i=t.visualMapModel.itemSize,n=t._applyTransform([r.offsetX,r.offsetY],t._shapes.mainGroup,!0,!0);n[1]=cu(Ki(0,n[1]),i[1]),t._doHoverLinkToSeries(n[1],0<=n[0]&&n[0]<=i[0])}}).on("mouseout",function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()})},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,r){var i=this.visualMapModel,n=i.itemSize;if(i.option.hoverLink){var o=[0,n[1]],s=i.getExtent();t=cu(Ki(o[0],t),o[1]);var l=Nx(i,s,o),u=[t-l,t+l],h=re(t,o,s,!0),v=[re(u[0],o,s,!0),re(u[1],o,s,!0)];u[0]<o[0]&&(v[0]=-1/0),u[1]>o[1]&&(v[1]=1/0),r&&(v[0]===-1/0?this._showIndicator(h,v[1],"< ",l):v[1]===1/0?this._showIndicator(h,v[0],"> ",l):this._showIndicator(h,h,"≈ ",l));var c=this._hoverLinkDataIndices,f=[];(r||pu(i))&&(f=this._hoverLinkDataIndices=i.findTargetDataIndices(v));var p=Nf(c,f);this._dispatchHighDown("downplay",fa(p[0],i)),this._dispatchHighDown("highlight",fa(p[1],i))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){var r;if(Zu(t.target,function(l){var u=nt(l);if(u.dataIndex!=null)return r=u,!0},!0),!!r){var i=this.ecModel.getSeriesByIndex(r.seriesIndex),n=this.visualMapModel;if(n.isTargetSeries(i)){var o=i.getData(r.dataType),s=o.getStore().get(n.getDataDimensionIndex(o),r.dataIndex);isNaN(s)||this._showIndicator(s,s)}}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var r=this._shapes.handleLabels;if(r)for(var i=0;i<r.length;i++)this.api.leaveBlur(r[i])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",fa(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,r,i,n){var o=la(r,n?null:this.group);return H(t)?xe(t,o,i):dh(t,o,i)},e.prototype._dispatchHighDown=function(t,r){r&&r.length&&this.api.dispatchAction({type:t,batch:r})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(Hv);function fu(a,e,t,r){return new _e({shape:{points:a},draggable:!!t,cursor:e,drift:t,onmousemove:function(i){ze(i.event)},ondragend:r})}function Nx(a,e,t){var r=Ex/2,i=a.get("hoverLinkDataSize");return i&&(r=re(i,e,t,!0)/2),r}function pu(a){var e=a.get("hoverLinkOnHandle");return!!(e??a.get("realtime"))}function du(a){return a==="vertical"?"ns-resize":"ew-resize"}var zx={type:"selectDataRange",event:"dataRangeSelected",update:"update"},Ox=function(a,e){e.eachComponent({mainType:"visualMap",query:a},function(t){t.setSelected(a.selected)})},Gx=[{createOnAllSeries:!0,reset:function(a,e){var t=[];return e.eachComponent("visualMap",function(r){var i=a.pipelineContext;!r.isTargetSeries(a)||i&&i.large||t.push(x1(r.stateList,r.targetVisuals,B(r.getValueState,r),r.getDataDimensionIndex(a.getData())))}),t}},{createOnAllSeries:!0,reset:function(a,e){var t=a.getData(),r=[];e.eachComponent("visualMap",function(i){if(i.isTargetSeries(a)){var n=i.getVisualMeta(B(Bx,null,a,i))||{stops:[],outerColors:[]},o=i.getDataDimensionIndex(t);o>=0&&(n.dimension=o,r.push(n))}}),a.getData().setVisual("visualMeta",r)}}];function Bx(a,e,t,r){for(var i=e.targetVisuals[r],n=yt.prepareVisualTypes(i),o={color:$a(a.getData(),"color")},s=0,l=n.length;s<l;s++){var u=n[s],h=i[u==="opacity"?"__alphaForOpacity":u];h&&h.applyVisual(t,v,c)}return o.color;function v(f){return o[f]}function c(f,p){o[f]=p}}var gu=D;function Fx(a){var e=a&&a.visualMap;H(e)||(e=e?[e]:[]),gu(e,function(t){if(t){Ue(t,"splitList")&&!Ue(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var r=t.pieces;r&&H(r)&&gu(r,function(i){Ot(i)&&(Ue(i,"start")&&!Ue(i,"min")&&(i.min=i.start),Ue(i,"end")&&!Ue(i,"max")&&(i.max=i.end))})}})}function Ue(a,e){return a&&a.hasOwnProperty&&a.hasOwnProperty(e)}var yu=!1;function Zv(a){yu||(yu=!0,a.registerSubTypeDefaulter("visualMap",function(e){return!e.categories&&(!(e.pieces?e.pieces.length>0:e.splitNumber>0)||e.calculable)?"continuous":"piecewise"}),a.registerAction(zx,Ox),D(Gx,function(e){a.registerVisual(a.PRIORITY.VISUAL.COMPONENT,e)}),a.registerPreprocessor(Fx))}function Uv(a){a.registerComponentModel(Px),a.registerComponentView(kx),Zv(a)}var Hx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._pieceList=[],t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var i=this._mode=this._determineMode();this._pieceList=[],Wx[this._mode].call(this,this._pieceList),this._resetSelected(t,r);var n=this.option.categories;this.resetVisual(function(o,s){i==="categories"?(o.mappingMethod="category",o.categories=ht(n)):(o.dataExtent=this.getExtent(),o.mappingMethod="piecewise",o.pieceList=W(this._pieceList,function(l){return l=ht(l),s!=="inRange"&&(l.visual=null),l}))})},e.prototype.completeVisualOption=function(){var t=this.option,r={},i=yt.listVisualTypes(),n=this.isCategory();D(t.pieces,function(s){D(i,function(l){s.hasOwnProperty(l)&&(r[l]=1)})}),D(r,function(s,l){var u=!1;D(this.stateList,function(h){u=u||o(t,h,l)||o(t.target,h,l)},this),!u&&D(this.stateList,function(h){(t[h]||(t[h]={}))[l]=Fv.get(l,h==="inRange"?"active":"inactive",n)})},this);function o(s,l,u){return s&&s[l]&&s[l].hasOwnProperty(u)}a.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,r){var i=this.option,n=this._pieceList,o=(r?i:t).selected||{};if(i.selected=o,D(n,function(l,u){var h=this.getSelectedMapKey(l);o.hasOwnProperty(h)||(o[h]=!0)},this),i.selectedMode==="single"){var s=!1;D(n,function(l,u){var h=this.getSelectedMapKey(l);o[h]&&(s?o[h]=!1:s=!0)},this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return this._mode==="categories"?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=ht(t)},e.prototype.getValueState=function(t){var r=yt.findPieceIndex(t,this._pieceList);return r!=null&&this.option.selected[this.getSelectedMapKey(this._pieceList[r])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[],i=this._pieceList;return this.eachTargetSeries(function(n){var o=[],s=n.getData();s.each(this.getDataDimensionIndex(s),function(l,u){var h=yt.findPieceIndex(l,i);h===t&&o.push(u)},this),r.push({seriesId:n.id,dataIndex:o})},this),r},e.prototype.getRepresentValue=function(t){var r;if(this.isCategory())r=t.value;else if(t.value!=null)r=t.value;else{var i=t.interval||[];r=i[0]===-1/0&&i[1]===1/0?0:(i[0]+i[1])/2}return r},e.prototype.getVisualMeta=function(t){if(this.isCategory())return;var r=[],i=["",""],n=this;function o(h,v){var c=n.getRepresentValue({interval:h});v||(v=n.getValueState(c));var f=t(c,v);h[0]===-1/0?i[0]=f:h[1]===1/0?i[1]=f:r.push({value:h[0],color:f},{value:h[1],color:f})}var s=this._pieceList.slice();if(!s.length)s.push({interval:[-1/0,1/0]});else{var l=s[0].interval[0];l!==-1/0&&s.unshift({interval:[-1/0,l]}),l=s[s.length-1].interval[1],l!==1/0&&s.push({interval:[l,1/0]})}var u=-1/0;return D(s,function(h){var v=h.interval;v&&(v[0]>u&&o([u,v[0]],"outOfRange"),o(v.slice()),u=v[1])},this),{stops:r,outerColors:i}},e.type="visualMap.piecewise",e.defaultOption=ar(Ea.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(Ea),Wx={splitNumber:function(a){var e=this.option,t=Math.min(e.precision,20),r=this.getExtent(),i=e.splitNumber;i=Math.max(parseInt(i,10),1),e.splitNumber=i;for(var n=(r[1]-r[0])/i;+n.toFixed(t)!==n&&t<5;)t++;e.precision=t,n=+n.toFixed(t),e.minOpen&&a.push({interval:[-1/0,r[0]],close:[0,0]});for(var o=0,s=r[0];o<i;s+=n,o++){var l=o===i-1?r[1]:s+n;a.push({interval:[s,l],close:[1,1]})}e.maxOpen&&a.push({interval:[r[1],1/0],close:[0,0]}),Xo(a),D(a,function(u,h){u.index=h,u.text=this.formatValueText(u.interval)},this)},categories:function(a){var e=this.option;D(e.categories,function(t){a.push({text:this.formatValueText(t,!0),value:t})},this),mu(e,a)},pieces:function(a){var e=this.option;D(e.pieces,function(t,r){Ot(t)||(t={value:t});var i={text:"",index:r};if(t.label!=null&&(i.text=t.label),t.hasOwnProperty("value")){var n=i.value=t.value;i.interval=[n,n],i.close=[1,1]}else{for(var o=i.interval=[],s=i.close=[0,0],l=[1,0,1],u=[-1/0,1/0],h=[],v=0;v<2;v++){for(var c=[["gte","gt","min"],["lte","lt","max"]][v],f=0;f<3&&o[v]==null;f++)o[v]=t[c[f]],s[v]=l[f],h[v]=f===2;o[v]==null&&(o[v]=u[v])}h[0]&&o[1]===1/0&&(s[0]=0),h[1]&&o[0]===-1/0&&(s[1]=0),o[0]===o[1]&&s[0]&&s[1]&&(i.value=o[0])}i.visual=yt.retrieveVisuals(t),a.push(i)},this),mu(e,a),Xo(a),D(a,function(t){var r=t.close,i=[["<","≤"][r[1]],[">","≥"][r[0]]];t.text=t.text||this.formatValueText(t.value!=null?t.value:t.interval,!1,i)},this)}};function mu(a,e){var t=a.inverse;(a.orient==="vertical"?!t:t)&&e.reverse()}var Zx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.doRender=function(){var t=this.group;t.removeAll();var r=this.visualMapModel,i=r.get("textGap"),n=r.textStyleModel,o=n.getFont(),s=n.getTextColor(),l=this._getItemAlign(),u=r.itemSize,h=this._getViewData(),v=h.endsText,c=Vt(r.get("showLabel",!0),!v),f=!r.get("selectedMode");v&&this._renderEndsText(t,v[0],u,c,l),D(h.viewPieceList,function(p){var d=p.piece,g=new $;g.onclick=B(this._onItemClick,this,d),this._enableHoverLink(g,p.indexInModelPieceList);var y=r.getRepresentValue(d);if(this._createItemSymbol(g,y,[0,0,u[0],u[1]],f),c){var m=this.visualMapModel.getValueState(y);g.add(new ut({style:{x:l==="right"?-i:u[0]+i,y:u[1]/2,text:d.text,verticalAlign:"middle",align:l,font:o,fill:s,opacity:m==="outOfRange"?.5:1},silent:f}))}t.add(g)},this),v&&this._renderEndsText(t,v[1],u,c,l),zf(r.get("orient"),t,r.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,r){var i=this;t.on("mouseover",function(){return n("highlight")}).on("mouseout",function(){return n("downplay")});var n=function(o){var s=i.visualMapModel;s.option.hoverLink&&i.api.dispatchAction({type:o,batch:fa(s.findTargetDataIndices(r),s)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,r=t.option;if(r.orient==="vertical")return Wv(t,this.api,t.itemSize);var i=r.align;return(!i||i==="auto")&&(i="left"),i},e.prototype._renderEndsText=function(t,r,i,n,o){if(r){var s=new $,l=this.visualMapModel.textStyleModel;s.add(new ut({style:ct(l,{x:n?o==="right"?i[0]:0:i[0]/2,y:i[1]/2,verticalAlign:"middle",align:n?o:"center",text:r})})),t.add(s)}},e.prototype._getViewData=function(){var t=this.visualMapModel,r=W(t.getPieceList(),function(s,l){return{piece:s,indexInModelPieceList:l}}),i=t.get("text"),n=t.get("orient"),o=t.get("inverse");return(n==="horizontal"?o:!o)?r.reverse():i&&(i=i.slice().reverse()),{viewPieceList:r,endsText:i}},e.prototype._createItemSymbol=function(t,r,i,n){var o=Pt(this.getControllerVisual(r,"symbol"),i[0],i[1],i[2],i[3],this.getControllerVisual(r,"color"));o.silent=n,t.add(o)},e.prototype._onItemClick=function(t){var r=this.visualMapModel,i=r.option,n=i.selectedMode;if(n){var o=ht(i.selected),s=r.getSelectedMapKey(t);n==="single"||n===!0?(o[s]=!0,D(o,function(l,u){o[u]=u===s})):o[s]=!o[s],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(Hv);function Yv(a){a.registerComponentModel(Hx),a.registerComponentView(Zx),Zv(a)}function Ux(a){X(Uv),X(Yv)}var Yx={label:{enabled:!0},decal:{show:!1}},Su=Dt(),$x={};function Xx(a,e){var t=a.getModel("aria");if(!t.get("enabled"))return;var r=ht(Yx);zt(r.label,a.getLocaleModel().get("aria"),!1),zt(t.option,r,!1),i(),n();function i(){var u=t.getModel("decal"),h=u.get("show");if(h){var v=rt();a.eachSeries(function(c){if(!c.isColorBySeries()){var f=v.get(c.type);f||(f={},v.set(c.type,f)),Su(c).scope=f}}),a.eachRawSeries(function(c){if(a.isSeriesFiltered(c))return;if(st(c.enableAriaDecal)){c.enableAriaDecal();return}var f=c.getData();if(c.isColorBySeries()){var m=tn(c.ecModel,c.name,$x,a.getSeriesCount()),S=f.getVisual("decal");f.setVisual("decal",x(S,m))}else{var p=c.getRawData(),d={},g=Su(c).scope;f.each(function(b){var _=f.getRawIndex(b);d[_]=b});var y=p.count();p.each(function(b){var _=d[b],w=p.getName(b)||b+"",T=tn(c.ecModel,w,g,y),L=f.getItemVisual(_,"decal");f.setItemVisual(_,"decal",x(L,T))})}function x(b,_){var w=b?F(F({},_),b):_;return w.dirty=!0,w}})}}function n(){var u=e.getZr().dom;if(u){var h=a.getLocaleModel().get("aria"),v=t.getModel("label");if(v.option=ft(v.option,h),!!v.get("enabled")){if(u.setAttribute("role","img"),v.get("description")){u.setAttribute("aria-label",v.get("description"));return}var c=a.getSeriesCount(),f=v.get(["data","maxCount"])||10,p=v.get(["series","maxCount"])||10,d=Math.min(c,p),g;if(!(c<1)){var y=s();if(y){var m=v.get(["general","withTitle"]);g=o(m,{title:y})}else g=v.get(["general","withoutTitle"]);var S=[],x=c>1?v.get(["series","multiple","prefix"]):v.get(["series","single","prefix"]);g+=o(x,{seriesCount:c}),a.eachSeries(function(T,L){if(L<d){var A=void 0,C=T.get("name"),I=C?"withName":"withoutName";A=c>1?v.get(["series","multiple",I]):v.get(["series","single",I]),A=o(A,{seriesId:T.seriesIndex,seriesName:T.get("name"),seriesType:l(T.subType)});var M=T.getData();if(M.count()>f){var P=v.get(["data","partialData"]);A+=o(P,{displayCnt:f})}else A+=v.get(["data","allData"]);for(var R=v.get(["data","separator","middle"]),V=v.get(["data","separator","end"]),k=v.get(["data","excludeDimensionId"]),N=[],G=0;G<M.count();G++)if(G<f){var Z=M.getName(G),U=k?Gt(M.getValues(G),function(J,q){return Mt(k,q)===-1}):M.getValues(G),K=v.get(["data",Z?"withName":"withoutName"]);N.push(o(K,{name:Z,value:U.join(R)}))}A+=N.join(R)+V,S.push(A)}});var b=v.getModel(["series","multiple","separator"]),_=b.get("middle"),w=b.get("end");g+=S.join(_)+w,u.setAttribute("aria-label",g)}}}}function o(u,h){if(!Q(u))return u;var v=u;return D(h,function(c,f){v=v.replace(new RegExp("\\{\\s*"+f+"\\s*\\}","g"),c)}),v}function s(){var u=a.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var h=a.getLocaleModel().get(["series","typeNames"]);return h[u]||h.chart}}function Kx(a){if(!(!a||!a.aria)){var e=a.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},D(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function qx(a){a.registerPreprocessor(Kx),a.registerVisual(a.PRIORITY.VISUAL.ARIA,Xx)}var qi=Math.sin,ji=Math.cos,$v=Math.PI,Le=Math.PI*2,jx=180/$v,Xv=function(){function a(){}return a.prototype.reset=function(e){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,e||4)},a.prototype.moveTo=function(e,t){this._add("M",e,t)},a.prototype.lineTo=function(e,t){this._add("L",e,t)},a.prototype.bezierCurveTo=function(e,t,r,i,n,o){this._add("C",e,t,r,i,n,o)},a.prototype.quadraticCurveTo=function(e,t,r,i){this._add("Q",e,t,r,i)},a.prototype.arc=function(e,t,r,i,n,o){this.ellipse(e,t,r,r,0,i,n,o)},a.prototype.ellipse=function(e,t,r,i,n,o,s,l){var u=s-o,h=!l,v=Math.abs(u),c=ye(v-Le)||(h?u>=Le:-u>=Le),f=u>0?u%Le:u%Le+Le,p=!1;c?p=!0:ye(v)?p=!1:p=f>=$v==!!h;var d=e+r*ji(o),g=t+i*qi(o);this._start&&this._add("M",d,g);var y=Math.round(n*jx);if(c){var m=1/this._p,S=(h?1:-1)*(Le-m);this._add("A",r,i,y,1,+h,e+r*ji(o+S),t+i*qi(o+S)),m>.01&&this._add("A",r,i,y,0,+h,d,g)}else{var x=e+r*ji(s),b=t+i*qi(s);this._add("A",r,i,y,+p,+h,x,b)}},a.prototype.rect=function(e,t,r,i){this._add("M",e,t),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},a.prototype.closePath=function(){this._d.length>0&&this._add("Z")},a.prototype._add=function(e,t,r,i,n,o,s,l,u){for(var h=[],v=this._p,c=1;c<arguments.length;c++){var f=arguments[c];if(isNaN(f)){this._invalid=!0;return}h.push(Math.round(f*v)/v)}this._d.push(e+h.join(" ")),this._start=e==="Z"},a.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},a.prototype.getStr=function(){return this._str},a}(),Ao="none",Jx=Math.round;function Qx(a){var e=a.fill;return e!=null&&e!==Ao}function tb(a){var e=a.stroke;return e!=null&&e!==Ao}var Dn=["lineCap","miterLimit","lineJoin"],eb=W(Dn,function(a){return"stroke-"+a.toLowerCase()});function rb(a,e,t,r){var i=e.opacity==null?1:e.opacity;if(t instanceof pe){a("opacity",i);return}if(Qx(e)){var n=Tr(e.fill);a("fill",n.color);var o=e.fillOpacity!=null?e.fillOpacity*n.opacity*i:n.opacity*i;o<1&&a("fill-opacity",o)}else a("fill",Ao);if(tb(e)){var s=Tr(e.stroke);a("stroke",s.color);var l=e.strokeNoScale?t.getLineScale():1,u=l?(e.lineWidth||0)/l:0,h=e.strokeOpacity!=null?e.strokeOpacity*s.opacity*i:s.opacity*i,v=e.strokeFirst;if(u!==1&&a("stroke-width",u),v&&a("paint-order",v?"stroke":"fill"),h<1&&a("stroke-opacity",h),e.lineDash){var c=Of(t),f=c[0],p=c[1];f&&(p=Jx(p||0),a("stroke-dasharray",f.join(",")),(p||r)&&a("stroke-dashoffset",p))}for(var d=0;d<Dn.length;d++){var g=Dn[d];if(e[g]!==Ko[g]){var y=e[g]||Ko[g];y&&a(eb[d],y)}}}}var Kv="http://www.w3.org/2000/svg",qv="http://www.w3.org/1999/xlink",ab="http://www.w3.org/2000/xmlns/",ib="http://www.w3.org/XML/1998/namespace",xu="ecmeta_";function jv(a){return document.createElementNS(Kv,a)}function dt(a,e,t,r,i){return{tag:a,attrs:t||{},children:r,text:i,key:e}}function nb(a,e){var t=[];if(e)for(var r in e){var i=e[r],n=r;i!==!1&&(i!==!0&&i!=null&&(n+='="'+i+'"'),t.push(n))}return"<"+a+" "+t.join(" ")+">"}function ob(a){return"</"+a+">"}function To(a,e){e=e||{};var t=e.newline?`
`:"";function r(i){var n=i.children,o=i.tag,s=i.attrs,l=i.text;return nb(o,s)+(o!=="style"?Gf(l):l||"")+(n?""+t+W(n,function(u){return r(u)}).join(t)+t:"")+ob(o)}return r(a)}function sb(a,e,t){t=t||{};var r=t.newline?`
`:"",i=" {"+r,n=r+"}",o=W(pt(a),function(l){return l+i+W(pt(a[l]),function(u){return u+":"+a[l][u]+";"}).join(r)+n}).join(r),s=W(pt(e),function(l){return"@keyframes "+l+i+W(pt(e[l]),function(u){return u+i+W(pt(e[l][u]),function(h){var v=e[l][u][h];return h==="d"&&(v='path("'+v+'")'),h+":"+v+";"}).join(r)+n}).join(r)+n}).join(r);return!o&&!s?"":["<![CDATA[",o,s,"]]>"].join(r)}function Ln(a){return{zrId:a,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function bu(a,e,t,r){return dt("svg","root",{width:a,height:e,xmlns:Kv,"xmlns:xlink":qv,version:"1.1",baseProfile:"full",viewBox:r?"0 0 "+a+" "+e:!1},t)}var lb=0;function Jv(){return lb++}var _u={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},Re="transform-origin";function ub(a,e,t){var r=F({},a.shape);F(r,e),a.buildPath(t,r);var i=new Xv;return i.reset(yh(a)),t.rebuildPath(i,1),i.generateStr(),i.getStr()}function hb(a,e){var t=e.originX,r=e.originY;(t||r)&&(a[Re]=t+"px "+r+"px")}var vb={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function Qv(a,e){var t=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[t]=a,t}function cb(a,e,t){var r=a.shape.paths,i={},n,o;if(D(r,function(l){var u=Ln(t.zrId);u.animation=!0,Qa(l,{},u,!0);var h=u.cssAnims,v=u.cssNodes,c=pt(h),f=c.length;if(f){o=c[f-1];var p=h[o];for(var d in p){var g=p[d];i[d]=i[d]||{d:""},i[d].d+=g.d||""}for(var y in v){var m=v[y].animation;m.indexOf(o)>=0&&(n=m)}}}),!!n){e.d=!1;var s=Qv(i,t);return n.replace(o,s)}}function wu(a){return Q(a)?_u[a]?"cubic-bezier("+_u[a]+")":Bf(a)?a:"":""}function Qa(a,e,t,r){var i=a.animators,n=i.length,o=[];if(a instanceof Gu){var s=cb(a,e,t);if(s)o.push(s);else if(!n)return}else if(!n)return;for(var l={},u=0;u<n;u++){var h=i[u],v=[h.getMaxTime()/1e3+"s"],c=wu(h.getClip().easing),f=h.getDelay();c?v.push(c):v.push("linear"),f&&v.push(f/1e3+"s"),h.getLoop()&&v.push("infinite");var p=v.join(" ");l[p]=l[p]||[p,[]],l[p][1].push(h)}function d(m){var S=m[1],x=S.length,b={},_={},w={},T="animation-timing-function";function L(j,Y,ot){for(var et=j.getTracks(),lt=j.getMaxTime(),xt=0;xt<et.length;xt++){var Ct=et[xt];if(Ct.needsAnimate()){var vt=Ct.keyframes,At=Ct.propName;if(ot&&(At=ot(At)),At)for(var Rt=0;Rt<vt.length;Rt++){var Xt=vt[Rt],Te=Math.round(Xt.time/lt*100)+"%",ir=wu(Xt.easing),Mo=Xt.rawValue;(Q(Mo)||he(Mo))&&(Y[Te]=Y[Te]||{},Y[Te][At]=Xt.rawValue,ir&&(Y[Te][T]=ir))}}}}for(var A=0;A<x;A++){var C=S[A],I=C.targetName;I?I==="shape"&&L(C,_):!r&&L(C,b)}for(var M in b){var P={};Ff(P,a),F(P,b[M]);var R=gh(P),V=b[M][T];w[M]=R?{transform:R}:{},hb(w[M],P),V&&(w[M][T]=V)}var k,N=!0;for(var M in _){w[M]=w[M]||{};var G=!k,V=_[M][T];G&&(k=new Hf);var Z=k.len();k.reset(),w[M].d=ub(a,_[M],k);var U=k.len();if(!G&&Z!==U){N=!1;break}V&&(w[M][T]=V)}if(!N)for(var M in w)delete w[M].d;if(!r)for(var A=0;A<x;A++){var C=S[A],I=C.targetName;I==="style"&&L(C,w,function(et){return vb[et]})}for(var K=pt(w),J=!0,q,A=1;A<K.length;A++){var tt=K[A-1],Lt=K[A];if(w[tt][Re]!==w[Lt][Re]){J=!1;break}q=w[tt][Re]}if(J&&q){for(var M in w)w[M][Re]&&delete w[M][Re];e[Re]=q}if(Gt(K,function(j){return pt(w[j]).length>0}).length){var ge=Qv(w,t);return ge+" "+m[0]+" both"}}for(var g in l){var s=d(l[g]);s&&o.push(s)}if(o.length){var y=t.zrId+"-cls-"+Jv();t.cssNodes["."+y]={animation:o.join(",")},e.class=y}}function fb(a,e,t){if(!a.ignore)if(a.isSilent()){var r={"pointer-events":"none"};Au(r,e,t)}else{var i=a.states.emphasis&&a.states.emphasis.style?a.states.emphasis.style:{},n=i.fill;if(!n){var o=a.style&&a.style.fill,s=a.states.select&&a.states.select.style&&a.states.select.style.fill,l=a.currentStates.indexOf("select")>=0&&s||o;l&&(n=Wf(l))}var u=i.lineWidth;if(u){var h=!i.strokeNoScale&&a.transform?a.transform[0]:1;u=u/h}var r={cursor:"pointer"};n&&(r.fill=n),i.stroke&&(r.stroke=i.stroke),u&&(r["stroke-width"]=u),Au(r,e,t)}}function Au(a,e,t,r){var i=JSON.stringify(a),n=t.cssStyleCache[i];n||(n=t.zrId+"-cls-"+Jv(),t.cssStyleCache[i]=n,t.cssNodes["."+n+":hover"]=a),e.class=e.class?e.class+" "+n:n}var Rr=Math.round;function tc(a){return a&&Q(a.src)}function ec(a){return a&&st(a.toDataURL)}function Do(a,e,t,r){rb(function(i,n){var o=i==="fill"||i==="stroke";o&&mh(n)?ac(e,a,i,r):o&&Yn(n)?ic(t,a,i,r):a[i]=n,o&&r.ssr&&n==="none"&&(a["pointer-events"]="visible")},e,t,!1),xb(t,a,r)}function Lo(a,e){var t=ap(e);t&&(t.each(function(r,i){r!=null&&(a[(xu+i).toLowerCase()]=r+"")}),e.isSilent()&&(a[xu+"silent"]="true"))}function Tu(a){return ye(a[0]-1)&&ye(a[1])&&ye(a[2])&&ye(a[3]-1)}function pb(a){return ye(a[4])&&ye(a[5])}function Co(a,e,t){if(e&&!(pb(e)&&Tu(e))){var r=1e4;a.transform=Tu(e)?"translate("+Rr(e[4]*r)/r+" "+Rr(e[5]*r)/r+")":rp(e)}}function Du(a,e,t){for(var r=a.points,i=[],n=0;n<r.length;n++)i.push(Rr(r[n][0]*t)/t),i.push(Rr(r[n][1]*t)/t);e.points=i.join(" ")}function Lu(a){return!a.smooth}function db(a){var e=W(a,function(t){return typeof t=="string"?[t,t]:t});return function(t,r,i){for(var n=0;n<e.length;n++){var o=e[n],s=t[o[0]];s!=null&&(r[o[1]]=Rr(s*i)/i)}}}var gb={circle:[db(["cx","cy","r"])],polyline:[Du,Lu],polygon:[Du,Lu]};function yb(a){for(var e=a.animators,t=0;t<e.length;t++)if(e[t].targetName==="shape")return!0;return!1}function rc(a,e){var t=a.style,r=a.shape,i=gb[a.type],n={},o=e.animation,s="path",l=a.style.strokePercent,u=e.compress&&yh(a)||4;if(i&&!e.willUpdate&&!(i[1]&&!i[1](r))&&!(o&&yb(a))&&!(l<1)){s=a.type;var h=Math.pow(10,u);i[0](r,n,h)}else{var v=!a.path||a.shapeChanged();a.path||a.createPathProxy();var c=a.path;v&&(c.beginPath(),a.buildPath(c,a.shape),a.pathUpdated());var f=c.getVersion(),p=a,d=p.__svgPathBuilder;(p.__svgPathVersion!==f||!d||l!==p.__svgPathStrokePercent)&&(d||(d=p.__svgPathBuilder=new Xv),d.reset(u),c.rebuildPath(d,l),d.generateStr(),p.__svgPathVersion=f,p.__svgPathStrokePercent=l),n.d=d.getStr()}return Co(n,a.transform),Do(n,t,a,e),Lo(n,a),e.animation&&Qa(a,n,e),e.emphasis&&fb(a,n,e),dt(s,a.id+"",n)}function mb(a,e){var t=a.style,r=t.image;if(r&&!Q(r)&&(tc(r)?r=r.src:ec(r)&&(r=r.toDataURL())),!!r){var i=t.x||0,n=t.y||0,o=t.width,s=t.height,l={href:r,width:o,height:s};return i&&(l.x=i),n&&(l.y=n),Co(l,a.transform),Do(l,t,a,e),Lo(l,a),e.animation&&Qa(a,l,e),dt("image",a.id+"",l)}}function Sb(a,e){var t=a.style,r=t.text;if(r!=null&&(r+=""),!(!r||isNaN(t.x)||isNaN(t.y))){var i=t.font||Zf,n=t.x||0,o=Uf(t.y||0,Yf(i),t.textBaseline),s=$f[t.textAlign]||t.textAlign,l={"dominant-baseline":"central","text-anchor":s};if(Xf(t)){var u="",h=t.fontStyle,v=Kf(t.fontSize);if(!parseFloat(v))return;var c=t.fontFamily||qf,f=t.fontWeight;u+="font-size:"+v+";font-family:"+c+";",h&&h!=="normal"&&(u+="font-style:"+h+";"),f&&f!=="normal"&&(u+="font-weight:"+f+";"),l.style=u}else l.style="font: "+i;return r.match(/\s/)&&(l["xml:space"]="preserve"),n&&(l.x=n),o&&(l.y=o),Co(l,a.transform),Do(l,t,a,e),Lo(l,a),e.animation&&Qa(a,l,e),dt("text",a.id+"",l,void 0,r)}}function Cu(a,e){if(a instanceof Ut)return rc(a,e);if(a instanceof pe)return mb(a,e);if(a instanceof Vu)return Sb(a,e)}function xb(a,e,t){var r=a.style;if(ip(r)){var i=np(a),n=t.shadowCache,o=n[i];if(!o){var s=a.getGlobalScale(),l=s[0],u=s[1];if(!l||!u)return;var h=r.shadowOffsetX||0,v=r.shadowOffsetY||0,c=r.shadowBlur,f=Tr(r.shadowColor),p=f.opacity,d=f.color,g=c/2/l,y=c/2/u,m=g+" "+y;o=t.zrId+"-s"+t.shadowIdx++,t.defs[o]=dt("filter",o,{id:o,x:"-100%",y:"-100%",width:"300%",height:"300%"},[dt("feDropShadow","",{dx:h/l,dy:v/u,stdDeviation:m,"flood-color":d,"flood-opacity":p})]),n[i]=o}e.filter=Xa(o)}}function ac(a,e,t,r){var i=a[t],n,o={gradientUnits:i.global?"userSpaceOnUse":"objectBoundingBox"};if(jf(i))n="linearGradient",o.x1=i.x,o.y1=i.y,o.x2=i.x2,o.y2=i.y2;else if(Jf(i))n="radialGradient",o.cx=Tt(i.x,.5),o.cy=Tt(i.y,.5),o.r=Tt(i.r,.5);else return;for(var s=i.colorStops,l=[],u=0,h=s.length;u<h;++u){var v=Qf(s[u].offset)*100+"%",c=s[u].color,f=Tr(c),p=f.color,d=f.opacity,g={offset:v};g["stop-color"]=p,d<1&&(g["stop-opacity"]=d),l.push(dt("stop",u+"",g))}var y=dt(n,"",o,l),m=To(y),S=r.gradientCache,x=S[m];x||(x=r.zrId+"-g"+r.gradientIdx++,S[m]=x,o.id=x,r.defs[x]=dt(n,x,o,l)),e[t]=Xa(x)}function ic(a,e,t,r){var i=a.style[t],n=a.getBoundingRect(),o={},s=i.repeat,l=s==="no-repeat",u=s==="repeat-x",h=s==="repeat-y",v;if(tp(i)){var c=i.imageWidth,f=i.imageHeight,p=void 0,d=i.image;if(Q(d)?p=d:tc(d)?p=d.src:ec(d)&&(p=d.toDataURL()),typeof Image>"u"){var g="Image width/height must been given explictly in svg-ssr renderer.";_r(c,g),_r(f,g)}else if(c==null||f==null){var y=function(A,C){if(A){var I=A.elm,M=c||C.width,P=f||C.height;A.tag==="pattern"&&(u?(P=1,M/=n.width):h&&(M=1,P/=n.height)),A.attrs.width=M,A.attrs.height=P,I&&(I.setAttribute("width",M),I.setAttribute("height",P))}},m=ep(p,null,a,function(A){l||y(_,A),y(v,A)});m&&m.width&&m.height&&(c=c||m.width,f=f||m.height)}v=dt("image","img",{href:p,width:c,height:f}),o.width=c,o.height=f}else i.svgElement&&(v=ht(i.svgElement),o.width=i.svgWidth,o.height=i.svgHeight);if(v){var S,x;l?S=x=1:u?(x=1,S=o.width/n.width):h?(S=1,x=o.height/n.height):o.patternUnits="userSpaceOnUse",S!=null&&!isNaN(S)&&(o.width=S),x!=null&&!isNaN(x)&&(o.height=x);var b=gh(i);b&&(o.patternTransform=b);var _=dt("pattern","",o,[v]),w=To(_),T=r.patternCache,L=T[w];L||(L=r.zrId+"-p"+r.patternIdx++,T[w]=L,o.id=L,_=r.defs[L]=dt("pattern",L,o,[v])),e[t]=Xa(L)}}function bb(a,e,t){var r=t.clipPathCache,i=t.defs,n=r[a.id];if(!n){n=t.zrId+"-c"+t.clipPathIdx++;var o={id:n};r[a.id]=n,i[n]=dt("clipPath",n,o,[rc(a,t)])}e["clip-path"]=Xa(n)}function Iu(a){return document.createTextNode(a)}function ke(a,e,t){a.insertBefore(e,t)}function Mu(a,e){a.removeChild(e)}function Pu(a,e){a.appendChild(e)}function nc(a){return a.parentNode}function oc(a){return a.nextSibling}function Ji(a,e){a.textContent=e}var Ru=58,_b=120,wb=dt("","");function Cn(a){return a===void 0}function ae(a){return a!==void 0}function Ab(a,e,t){for(var r={},i=e;i<=t;++i){var n=a[i].key;n!==void 0&&(r[n]=i)}return r}function mr(a,e){var t=a.key===e.key,r=a.tag===e.tag;return r&&t}function Er(a){var e,t=a.children,r=a.tag;if(ae(r)){var i=a.elm=jv(r);if(Io(wb,a),H(t))for(e=0;e<t.length;++e){var n=t[e];n!=null&&Pu(i,Er(n))}else ae(a.text)&&!Ot(a.text)&&Pu(i,Iu(a.text))}else a.elm=Iu(a.text);return a.elm}function sc(a,e,t,r,i){for(;r<=i;++r){var n=t[r];n!=null&&ke(a,Er(n),e)}}function Va(a,e,t,r){for(;t<=r;++t){var i=e[t];if(i!=null)if(ae(i.tag)){var n=nc(i.elm);Mu(n,i.elm)}else Mu(a,i.elm)}}function Io(a,e){var t,r=e.elm,i=a&&a.attrs||{},n=e.attrs||{};if(i!==n){for(t in n){var o=n[t],s=i[t];s!==o&&(o===!0?r.setAttribute(t,""):o===!1?r.removeAttribute(t):t==="style"?r.style.cssText=o:t.charCodeAt(0)!==_b?r.setAttribute(t,o):t==="xmlns:xlink"||t==="xmlns"?r.setAttributeNS(ab,t,o):t.charCodeAt(3)===Ru?r.setAttributeNS(ib,t,o):t.charCodeAt(5)===Ru?r.setAttributeNS(qv,t,o):r.setAttribute(t,o))}for(t in i)t in n||r.removeAttribute(t)}}function Tb(a,e,t){for(var r=0,i=0,n=e.length-1,o=e[0],s=e[n],l=t.length-1,u=t[0],h=t[l],v,c,f,p;r<=n&&i<=l;)o==null?o=e[++r]:s==null?s=e[--n]:u==null?u=t[++i]:h==null?h=t[--l]:mr(o,u)?(Ye(o,u),o=e[++r],u=t[++i]):mr(s,h)?(Ye(s,h),s=e[--n],h=t[--l]):mr(o,h)?(Ye(o,h),ke(a,o.elm,oc(s.elm)),o=e[++r],h=t[--l]):mr(s,u)?(Ye(s,u),ke(a,s.elm,o.elm),s=e[--n],u=t[++i]):(Cn(v)&&(v=Ab(e,r,n)),c=v[u.key],Cn(c)?ke(a,Er(u),o.elm):(f=e[c],f.tag!==u.tag?ke(a,Er(u),o.elm):(Ye(f,u),e[c]=void 0,ke(a,f.elm,o.elm))),u=t[++i]);(r<=n||i<=l)&&(r>n?(p=t[l+1]==null?null:t[l+1].elm,sc(a,p,t,i,l)):Va(a,e,r,n))}function Ye(a,e){var t=e.elm=a.elm,r=a.children,i=e.children;a!==e&&(Io(a,e),Cn(e.text)?ae(r)&&ae(i)?r!==i&&Tb(t,r,i):ae(i)?(ae(a.text)&&Ji(t,""),sc(t,null,i,0,i.length-1)):ae(r)?Va(t,r,0,r.length-1):ae(a.text)&&Ji(t,""):a.text!==e.text&&(ae(r)&&Va(t,r,0,r.length-1),Ji(t,e.text)))}function Db(a,e){if(mr(a,e))Ye(a,e);else{var t=a.elm,r=nc(t);Er(e),r!==null&&(ke(r,e.elm,oc(t)),Va(r,[a],0,0))}return e}var Lb=0,Cb=function(){function a(e,t,r){if(this.type="svg",this.refreshHover=Eu(),this.configLayer=Eu(),this.storage=t,this._opts=r=F({},r),this.root=e,this._id="zr"+Lb++,this._oldVNode=bu(r.width,r.height),e&&!r.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var n=this._svgDom=this._oldVNode.elm=jv("svg");Io(null,this._oldVNode),i.appendChild(n),e.appendChild(i)}this.resize(r.width,r.height)}return a.prototype.getType=function(){return this.type},a.prototype.getViewportRoot=function(){return this._viewport},a.prototype.getViewportRootOffset=function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},a.prototype.getSvgDom=function(){return this._svgDom},a.prototype.refresh=function(){if(this.root){var e=this.renderToVNode({willUpdate:!0});e.attrs.style="position:absolute;left:0;top:0;user-select:none",Db(this._oldVNode,e),this._oldVNode=e}},a.prototype.renderOneToVNode=function(e){return Cu(e,Ln(this._id))},a.prototype.renderToVNode=function(e){e=e||{};var t=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=Ln(this._id);n.animation=e.animation,n.willUpdate=e.willUpdate,n.compress=e.compress,n.emphasis=e.emphasis,n.ssr=this._opts.ssr;var o=[],s=this._bgVNode=Ib(r,i,this._backgroundColor,n);s&&o.push(s);var l=e.compress?null:this._mainVNode=dt("g","main",{},[]);this._paintList(t,n,l?l.children:o),l&&o.push(l);var u=W(pt(n.defs),function(c){return n.defs[c]});if(u.length&&o.push(dt("defs","defs",{},u)),e.animation){var h=sb(n.cssNodes,n.cssAnims,{newline:!0});if(h){var v=dt("style","stl",{},[],h);o.push(v)}}return bu(r,i,o,e.useViewBox)},a.prototype.renderToString=function(e){return e=e||{},To(this.renderToVNode({animation:Tt(e.cssAnimation,!0),emphasis:Tt(e.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:Tt(e.useViewBox,!0)}),{newline:!0})},a.prototype.setBackgroundColor=function(e){this._backgroundColor=e},a.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},a.prototype._paintList=function(e,t,r){for(var i=e.length,n=[],o=0,s,l,u=0,h=0;h<i;h++){var v=e[h];if(!v.invisible){var c=v.__clipPaths,f=c&&c.length||0,p=l&&l.length||0,d=void 0;for(d=Math.max(f-1,p-1);d>=0&&!(c&&l&&c[d]===l[d]);d--);for(var g=p-1;g>d;g--)o--,s=n[o-1];for(var y=d+1;y<f;y++){var m={};bb(c[y],m,t);var S=dt("g","clip-g-"+u++,m,[]);(s?s.children:r).push(S),n[o++]=S,s=S}l=c;var x=Cu(v,t);x&&(s?s.children:r).push(x)}}},a.prototype.resize=function(e,t){var r=this._opts,i=this.root,n=this._viewport;if(e!=null&&(r.width=e),t!=null&&(r.height=t),i&&n&&(n.style.display="none",e=qo(i,0,r),t=qo(i,1,r),n.style.display=""),this._width!==e||this._height!==t){if(this._width=e,this._height=t,n){var o=n.style;o.width=e+"px",o.height=t+"px"}if(Yn(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",e),s.setAttribute("height",t));var l=this._bgVNode&&this._bgVNode.elm;l&&(l.setAttribute("width",e),l.setAttribute("height",t))}}},a.prototype.getWidth=function(){return this._width},a.prototype.getHeight=function(){return this._height},a.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},a.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},a.prototype.toDataURL=function(e){var t=this.renderToString(),r="data:image/svg+xml;";return e?(t=op(t),t&&r+"base64,"+t):r+"charset=UTF-8,"+encodeURIComponent(t)},a}();function Eu(a){return function(){}}function Ib(a,e,t,r){var i;if(t&&t!=="none")if(i=dt("rect","bg",{width:a,height:e,x:"0",y:"0"}),mh(t))ac({fill:t},i.attrs,"fill",r);else if(Yn(t))ic({style:{fill:t},dirty:Ge,getBoundingRect:function(){return{width:a,height:e}}},i.attrs,"fill",r);else{var n=Tr(t),o=n.color,s=n.opacity;i.attrs.fill=o,s<1&&(i.attrs["fill-opacity"]=s)}return i}function Mb(a){a.registerPainter("svg",Cb)}X([sp]);X([Mb]);X([lp,up,hp,Dp,vp,dd,Wd,Dg,Xg,ty,uy,Hy,fm,Am,Bm,Zm,t0,s0,m0,A0,V0,yS]);X(cp);X(WS);X(Ph);X(r1);X(rv);X(o1);X(d1);X(fp);X(pp);X(Zn);X(R1);X(dp);X(H1);X(K1);X(ex);X(sx);X(gp);X(Tx);X(Gv);X(Bv);X(Ux);X(Uv);X(Yv);X(qx);X(yp);X(mp);X(Sp);X(xp);
