import{_}from"./copy-BxkeU8Ds.js";import{d as y,r as w,ac as $,ad as R,m as C,o as k,b as l,ab as q,w as d,a as t,g as e,B,t as o,ae as T,$ as I}from"./index-pY9FjpQW.js";import{_ as z}from"./Table-DoHSPnrC.js";const j={class:"border border-orange rounded-lg p-2 mt-4"},N={class:"font-bold"},P={class:"border rounded-lg p-2 mt-4"},S={class:"mt-4 overflow-x-auto"},D={class:"w-1/4"},G={class:"w-1/4"},O={class:"w-1/4"},V={class:"w-1/4"},A={class:"mt-4"},E={class:"mt-4 overflow-x-auto"},F={class:"w-1/4"},J={class:"w-1/4"},K={class:"w-1/4"},L={class:"w-1/4"},M={class:"mt-4"},U={class:"bg-black/70 rounded-lg px-4 pt-4"},x={class:"absolute right-0 mr-10"},H={class:"mt-4"},Q={class:"bg-black/70 rounded-lg px-4 pt-4"},W={class:"absolute right-0 mr-10"},h=y({__name:"docGerBetRecord",setup(X){const u=w(null),{t:f}=$(),b=R(),i=()=>{const s=u.value?.innerText;s&&navigator.clipboard.writeText(s).then(()=>{b.success(f("copysuccess"))})};return(s,n)=>{const r=B,a=T,p=z,g=_,m=I,v=q;return k(),C("div",null,[l(v,null,{default:d(()=>[n[29]||(n[29]=t("div",{class:"font-bold"},"Get Bet Records",-1)),t("div",null,[l(r,{type:"success",round:"",size:"small",class:"mt-3 cursor-default"},{default:d(()=>[e(o(s.$t("update")),1)]),_:1}),e(" "+o(s.$t("lastupdate")),1)]),t("div",j,[l(r,{class:"cursor-default",type:"warning",round:"",size:"tiny"},{default:d(()=>n[0]||(n[0]=[e("POST")])),_:1}),n[1]||(n[1]=t("span",null," {{ API_URL }}/seamless/betTransactionReplay",-1))]),l(a),t("div",null,[t("div",N,o(s.$t("security")),1),t("div",P,[t("div",null,[l(r,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:d(()=>n[2]||(n[2]=[e("Authorization")])),_:1})]),n[4]||(n[4]=t("span",null," apiKey : Base64({{ agent_username }}:{{ secret_key }})",-1)),n[5]||(n[5]=t("hr",{class:"my-2"},null,-1)),t("div",null,[l(r,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:d(()=>n[3]||(n[3]=[e("Content Type")])),_:1})]),n[6]||(n[6]=t("span",null," Type: application/json",-1))])]),l(a),t("div",null,[n[19]||(n[19]=t("div",{class:"font-bold"},"Parameter Description",-1)),t("div",S,[l(p,null,{default:d(()=>[t("thead",null,[t("tr",null,[t("th",D,o(s.$t("property")),1),t("th",G,o(s.$t("type")),1),t("th",O,o(s.$t("required")),1),t("th",V,o(s.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[7]||(n[7]=t("td",null,"gameCode",-1)),n[8]||(n[8]=t("td",null,"string",-1)),n[9]||(n[9]=t("td",null,"Required",-1)),t("td",null,o(s.$t("doc.gbr1")),1)]),t("tr",null,[n[10]||(n[10]=t("td",null,"productCode",-1)),n[11]||(n[11]=t("td",null,"string",-1)),n[12]||(n[12]=t("td",null,"Required",-1)),t("td",null,o(s.$t("doc.gbr2")),1)]),t("tr",null,[n[13]||(n[13]=t("td",null,"RoundId",-1)),n[14]||(n[14]=t("td",null,"string",-1)),n[15]||(n[15]=t("td",null,"Required",-1)),t("td",null,o(s.$t("doc.gbr3")),1)]),t("tr",null,[n[16]||(n[16]=t("td",null,"txnId",-1)),n[17]||(n[17]=t("td",null,"string",-1)),n[18]||(n[18]=t("td",null,"Required",-1)),t("td",null,o(s.$t("doc.gbr4")),1)])])]),_:1})])]),t("div",A,[n[26]||(n[26]=t("div",{class:"font-bold"},"Response Description",-1)),t("div",E,[l(p,null,{default:d(()=>[t("thead",null,[t("tr",null,[t("th",F,o(s.$t("property")),1),t("th",J,o(s.$t("type")),1),t("th",K,o(s.$t("required")),1),t("th",L,o(s.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[20]||(n[20]=t("td",null,"data",-1)),n[21]||(n[21]=t("td",null,"string",-1)),n[22]||(n[22]=t("td",null,"Required",-1)),t("td",null,o(s.$t("doc.gbr5")),1)]),t("tr",null,[n[23]||(n[23]=t("td",null,"err",-1)),n[24]||(n[24]=t("td",null,"string",-1)),n[25]||(n[25]=t("td",null,"Required",-1)),t("td",null,o(s.$t("doc.gbr6")),1)])])]),_:1})])]),t("div",M,[n[27]||(n[27]=t("div",{class:"font-bold mb-4"},"Request Body",-1)),t("div",U,[t("div",x,[l(m,{trigger:"hover"},{trigger:d(()=>[l(r,{class:"text-white",onClick:i},{default:d(()=>[l(g)]),_:1})]),default:d(()=>[e(" "+o(s.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:u,class:"font-normal text-white overflow-x-auto"},`{
    "gameCode": "string",
    "productCode": "PGSOFT",
    "RoundId": "string",
    "txnId": "string"
}
    `,512)])]),t("div",H,[n[28]||(n[28]=t("div",{class:"font-bold mb-4"},"JSON response example",-1)),t("div",Q,[t("div",W,[l(m,{trigger:"hover"},{trigger:d(()=>[l(r,{class:"text-white",onClick:i},{default:d(()=>[l(g)]),_:1})]),default:d(()=>[e(" "+o(s.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:u,class:"font-normal text-white overflow-x-auto"},`{
    "data": {
    "url": "string"
}
    "err": {}
}
    `,512)])])]),_:1})])}}});export{h as _};
