import{_ as F}from"./add-one-BP9Rv5dm.js";import{d as I,ac as L,r as b,ad as Q,U as m,a3 as H,fQ as G,c as i,o as d,w as n,b as a,ab as J,a as _,t as h,f as t,fT as K,af as j,m as w,G as $,a8 as y,fD as O,B as W,g as X,g3 as Y}from"./index-pY9FjpQW.js";import{_ as Z,a as x}from"./RadioGroup-DnXU-fFU.js";import{_ as ee}from"./Form-_sVFK3VR.js";import{_ as ae}from"./FormItem-B4P0FvHJ.js";const le={class:"mb-5"},ne={class:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-5"},te={class:"mb-5"},se={class:"flex justify-center"},ue={class:"grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-5"},_e=I({__name:"index",setup(oe){const{t:o}=L(),v=b(!1),k=Q(),U=b(null),l=b({username:"",name:"",password:"",phone:"",management:0,setting:0,report:0,player:0}),q=m(()=>[{value:0,label:o("off")},{value:1,label:o("view")},{value:2,label:o("edit")}]),C=m(()=>[{value:0,label:o("off")},{value:1,label:o("view")},{value:2,label:o("edit")}]),D=m(()=>[{value:0,label:o("off")},{value:1,label:o("view")}]),P=m(()=>[{value:0,label:o("off")},{value:1,label:o("view")},{value:2,label:o("edit")}]),V=m(()=>({username:[{required:!0,message:o("validusername"),trigger:"blur"}],name:[{required:!0,message:o("validname"),trigger:"blur"}],password:[{required:!0,message:o("validpassword"),trigger:"blur"}]})),B=b(null);H(()=>{z()});function z(){G.get("PG/Profile/agent").then(s=>{s.data.success&&(B.value=s.data.data.username)})}function A(s){s.preventDefault(),U.value?.validate(u=>{if(!u){v.value=!0;const p={username:l.value.username,password:l.value.password,name:l.value.name,tel:l.value.phone,permissions:{management:l.value.management,player:l.value.player,report:l.value.report,setting:l.value.setting}};G.post("PG/Store/SubAccount",p).then(r=>{r.data.success?(k.success(r.data.mes),Y.push("/management/sub-account"),v.value=!1):(k.error(r.data.mes),v.value=!1)})}})}return(s,u)=>{const p=K,r=ae,M=ee,N=J,c=x,f=j,g=Z,S=j,R=F,T=W,E=O;return d(),i(S,{vertical:"",size:"large"},{default:n(()=>[a(N,null,{default:n(()=>[_("div",le,h(s.$t("basicinfomation")),1),a(M,{ref_key:"formRef",ref:U,model:t(l),"label-placement":"left","show-feedback":!0,rules:t(V)},{default:n(()=>[_("div",ne,[a(r,{label:`${t(B)}@`,path:"username"},{default:n(()=>[a(p,{value:t(l).username,"onUpdate:value":u[0]||(u[0]=e=>t(l).username=e),placeholder:s.$t("username")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(r,{label:s.$t("name"),path:"name"},{default:n(()=>[a(p,{value:t(l).name,"onUpdate:value":u[1]||(u[1]=e=>t(l).name=e),placeholder:s.$t("name")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(r,{label:s.$t("password"),path:"password"},{default:n(()=>[a(p,{value:t(l).password,"onUpdate:value":u[2]||(u[2]=e=>t(l).password=e),placeholder:s.$t("password")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(r,{label:s.$t("phone")},{default:n(()=>[a(p,{value:t(l).phone,"onUpdate:value":u[3]||(u[3]=e=>t(l).phone=e),placeholder:s.$t("phone")},null,8,["value","placeholder"])]),_:1},8,["label"])])]),_:1},8,["model","rules"])]),_:1}),a(N,null,{default:n(()=>[_("div",te,h(s.$t("permissions")),1),a(S,{vertical:"",size:"large"},{default:n(()=>[_("div",se,[_("div",ue,[a(r,{label:s.$t("management"),class:"sm:mx-10"},{default:n(()=>[a(g,{value:t(l).management,"onUpdate:value":u[4]||(u[4]=e=>t(l).management=e)},{default:n(()=>[a(f,{vertical:""},{default:n(()=>[(d(!0),w($,null,y(t(q),e=>(d(),i(c,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"]),a(r,{label:s.$t("settingmanagement"),class:"sm:mx-10"},{default:n(()=>[a(g,{value:t(l).setting,"onUpdate:value":u[5]||(u[5]=e=>t(l).setting=e)},{default:n(()=>[a(f,{vertical:""},{default:n(()=>[(d(!0),w($,null,y(t(C),e=>(d(),i(c,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"]),a(r,{label:s.$t("report"),class:"sm:mx-10"},{default:n(()=>[a(g,{value:t(l).report,"onUpdate:value":u[6]||(u[6]=e=>t(l).report=e)},{default:n(()=>[a(f,{vertical:""},{default:n(()=>[(d(!0),w($,null,y(t(D),e=>(d(),i(c,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"]),a(r,{label:s.$t("player"),class:"sm:mx-10"},{default:n(()=>[a(g,{value:t(l).player,"onUpdate:value":u[7]||(u[7]=e=>t(l).player=e)},{default:n(()=>[a(f,{vertical:""},{default:n(()=>[(d(!0),w($,null,y(t(P),e=>(d(),i(c,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])])])]),_:1}),a(E,{justify:"end",class:"mt-10"},{default:n(()=>[a(T,{loading:t(v),type:"success",onClick:A},{icon:n(()=>[a(R)]),default:n(()=>[X(" "+h(s.$t("save")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})}}});export{_e as default};
