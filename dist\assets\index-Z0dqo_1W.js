import{_ as $e,a as xe}from"./Pagination.vue_vue_type_script_setup_true_lang-tK6i-N0U.js";import{a7 as ce,m as Z,o as k,a as u,d as De,ac as Pe,r as d,gg as re,gh as oe,fB as Ue,l as Ce,ad as He,gd as je,O as Be,U as O,b as n,B as y,g as i,a3 as Me,fQ as C,w as s,ab as Te,t as m,fT as Ye,f as a,i as x,fZ as Se,gc as Re,af as V,ge as Ie,c as T,n as Y,fD as Le,fR as We,gi as ze}from"./index-pY9FjpQW.js";import{_ as Ge}from"./add-one-BP9Rv5dm.js";import{m as w}from"./index-DZ56Tu_n.js";import{t as ue,d as ie,_ as Ne}from"./DatePicker-HFFCQBdF.js";import"./Checkbox-CwEpY3xE.js";import"./RadioGroup-DnXU-fFU.js";import"./download-C2161hUv.js";const Ee={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ke(Q,l){return k(),Z("svg",Ee,l[0]||(l[0]=[u("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"M10 6a2 2 0 0 1 2-2h24a2 2 0 0 1 2 2v38l-7-5l-7 5l-7-5l-7 5zm8 16h12m-12 8h12M18 14h12"},null,-1)]))}const Ae=ce({name:"icon-park-outline-bill",render:Ke}),Fe={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Oe(Q,l){return k(),Z("svg",Fe,l[0]||(l[0]=[u("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[u("path",{d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"}),u("path",{"stroke-linecap":"round",d:"M16 24h16"})],-1)]))}const Ve=ce({name:"icon-park-outline-reduce-one",render:Oe}),Ze={class:"xl:flex items-center gap-3 xl:w-1/2"},Qe={class:"my-1 text-sm text-nowrap"},qe={class:"w-full my-1"},Je={class:"my-1 text-sm text-nowrap"},Xe={class:"w-full my-1"},et={class:"ml-2"},tt={class:"flex justify-start items-center text-nowrap gap-3 my-3"},nt={class:"flex justify-center items-center text-nowrap gap-3 my-3"},at={class:"flex justify-start items-center text-nowrap gap-3 my-3"},lt={class:"flex justify-center items-center text-nowrap gap-3 my-3"},st={class:"sm:flex justify-center gap-3 mb-3"},yt=De({__name:"index",setup(Q){const{t:l,locale:S}=Pe(),R=d(S.value==="thTH"?ue:re),I=d(S.value==="thTH"?ie:oe),{userData:g}=Ue();Ce();const f=He(),q=je(),H=d(""),L=d(10),W=d(1),j=d(10),B=d(1),z=d(1),J=d(1),h=d(!1),X=d([]),G=d([]);Be(()=>S.value,t=>{t==="thTH"?(I.value=ie,R.value=ue):t==="enUS"&&(I.value=oe,R.value=re)});const ee=t=>t?w(t).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm"):"-",de=O(()=>{const t=[{title:l("no."),align:"center",key:"index",render:(e,o)=>L.value*(W.value-1)+o+1},{title:l("loginname"),align:"center",key:"username",render:e=>e.position_type&&(e.position_type==2||e.position_type==3)?n(y,{class:"cursor-default",tertiary:!0,round:!0,type:e.position_type===2?"warning":e.position_type===3?"success":"info"},{default:()=>[e.username]}):n(y,{class:"cursor-default",tertiary:!0,round:!0,type:e.position_type===2?"warning":e.position_type===3?"success":"info"},{default:()=>[e.username]})},{title:l("position"),align:"center",key:"position",render:e=>n(y,{size:"small",class:"cursor-default",ghost:!0,type:e.position_type===2?"warning":e.position_type===3?"success":"info"},{default:()=>[e.position_type===2?l("reseller"):e.position_type===3?l("agent"):l("member")]})},{title:l("currency"),align:"center",key:"currency",render:e=>{let o="";return e.position_type==3?o=e.ag_currency:(e.reseller_thb==1&&(o="THB"),e.reseller_krw==1&&(o?o+=",KRW":o="KRW"),e.reseller_usd==1&&(o?o+=",USD":o="USD")),!o&&e.currency&&(o=e.currency),o}},{title:l("credit"),align:"center",key:"credit",render:(e,o)=>n("div",{class:"text-start "},[e.reseller_thb===1&&n("div",{class:"flex justify-start items-center gap-2"},[n("img",{src:"/images/country/th.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit_thb),i(" ฿")]),e.reseller_krw===1&&n("div",{class:"flex justify-start items-center gap-2"},[n("img",{src:"/images/country/kr.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit_krw),i(" ₩")]),e.reseller_usd===1&&n("div",{class:"flex justify-start items-center gap-2"},[n("img",{src:"/images/country/us.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit_usd),i(" $")]),e.ag_currency==="THB"&&n("div",{class:"flex justify-start items-center gap-2"},[n("img",{src:"/images/country/th.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit_thb),i(" ฿")]),e.ag_currency==="KRW"&&n("div",{class:"flex justify-start items-center gap-2"},[n("img",{src:"/images/country/kr.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit_krw),i(" ₩")]),e.ag_currency==="USD"&&n("div",{class:"flex justify-start items-center gap-2"},[n("img",{src:"/images/country/us.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit_usd),i(" $")]),g.position_type==4&&g.upline_position_type==3||g.position_type==3&&e.currency==="THB"&&n("div",{class:"flex justify-center items-center gap-2"},[n("img",{src:"/images/country/th.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit),i(" ฿")]),g.position_type==4&&g.upline_position_type==3||g.position_type==3&&e.currency==="KRW"&&n("div",{class:"flex justify-center items-center gap-2"},[n("img",{src:"/images/country/kr.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit),i(" ₩")]),g.position_type==4&&g.upline_position_type==3||g.position_type==3&&e.currency==="USD"&&n("div",{class:"flex justify-center items-center gap-2"},[n("img",{src:"/images/country/us.webp",alt:"Language Icon",width:"25",height:"20"},null)," ",_(e.credit),i(" $")])])}];return(g.position_type==2||g.position_type==1)&&t.push({title:l("deposit"),align:"center",key:"actions",render:e=>n(y,{onClick:()=>me(e),secondary:!0,type:"success",size:"small"},{default:()=>[n(Ge,null,null)]})},{title:l("withdraw"),align:"center",key:"actions",render:e=>n(y,{onClick:()=>ye(e),secondary:!0,type:"error",size:"small"},{default:()=>[n(Ve,null,null)]})},{title:l("detail"),align:"center",key:"",render:e=>n(y,{onClick:()=>_e(e),secondary:!0,type:"info",size:"small"},{default:()=>[n(Ae,null,null)]})}),t.push({title:l("lastlogindate"),align:"center",key:"last_login",render:e=>ee(e.last_login)},{title:l("lastloginip"),align:"center",key:"last_ip",render:e=>e.last_ip||"-"}),t}),pe=O(()=>[{title:l("no."),align:"center",key:"index",render:(t,e)=>j.value*(B.value-1)+(e+1)},{title:l("detail"),align:"center",key:"detail",render:(t,e)=>n("div",null,[" ",n(y,{class:"cursor-default",secondary:!0,type:t.action==="deposit"?"success":"error",size:"small"},{default:()=>[t.action==="deposit"?l("deposit"):l("withdraw")]})])},{title:l("currency"),align:"center",key:"currency",render:(t,e)=>t.currency},{title:l("amount"),align:"center",key:"amount",render:(t,e)=>_(t.amount)},{title:l("date"),align:"center",key:"created_at",render:(t,e)=>ee(t.created_at)}]);Me(()=>{D()});const M=d("THB"),te=O(()=>[{label:"THB",value:"THB"},{label:"KRW",value:"KRW"},{label:"USD",value:"USD"}]),D=async()=>{h.value=!0;try{const t={Page:W.value,perPage:L.value,currency:M.value,username:H.value},{data:e}=await C.get("PG/Payment/list",{params:t});X.value=e.data,J.value=e.total,h.value=!1}catch(t){console.error("Error fetching data:",t)}},b=d(!1),p=d([]),me=t=>{p.value=t,b.value=!0},$=d(!1),c=d(null),ye=t=>{t&&(c.value=t,$.value=!0)},ge=()=>{const t=c.value.ag_currency||c.value.currency;if(!t||!Number(c.value.amountcurrency))return f.error(l("valid"));q.warning({title:l("confirmsave"),content:l("areyousure"),positiveText:l("confirm"),negativeText:l("cancel"),maskClosable:!1,onPositiveClick:()=>{const e={username:c.value.username,agent_id:c.value.id,amount:c.value.amountcurrency,currency:t};C.post("PG/Payment/Withdrawal",e).then(o=>{o.data.status?(f.success(l("commitsuccess")),$.value=!1,D()):f.error(o.data.mes)})},onEsc:()=>{f.success(l("cancel"))}})},fe=()=>{const t=p.value.ag_currency||p.value.currency;if(!t||!Number(p.value.amountcurrency))return f.error(l("valid"));q.warning({title:l("confirmsave"),content:l("areyousure"),positiveText:l("confirm"),negativeText:l("cancel"),maskClosable:!1,onPositiveClick:()=>{const e={username:p.value.username,agent_id:p.value.id,amount:p.value.amountcurrency,currency:t};C.post("PG/Payment/Deposit",e).then(o=>{o.data.status?(f.success(l("commitsuccess")),b.value=!1,D()):f.error(o.data.mes)})},onEsc:()=>{f.success(l("cancel"))}})},ve=t=>{const e=[];return t.reseller_thb&&e.push({label:"THB",value:"THB"}),t.reseller_krw&&e.push({label:"KRW",value:"KRW"}),t.reseller_usd&&e.push({label:"USD",value:"USD"}),e},P=d(!1),U=d([]),_e=t=>{h.value=!0;const e={perPage:j.value,page:B.value,agent_id:t.id,startDate:v.value?w(v.value[0]).format("YYYY-MM-DD HH:mm:ss"):null,endDate:v.value?w(v.value[1]).format("YYYY-MM-DD HH:mm:ss"):null};C.get("PG/Payment/GetDetail",{params:e}).then(o=>{U.value=t,G.value=o.data.data,z.value=o.data.total||1,P.value=!0,h.value=!1})},ne=()=>{h.value=!0;const t={perPage:j.value,page:B.value,agent_id:U.value.id,startDate:v.value?w(v.value[0]).format("YYYY-MM-DD HH:mm:ss"):null,endDate:v.value?w(v.value[1]).format("YYYY-MM-DD HH:mm:ss"):null};C.get("PG/Payment/GetDetail",{params:t}).then(e=>{G.value=e.data.data,z.value=e.data.total||1,h.value=!1})};function he(t,e){f.success(`${l("gotopage")} : ${t}/${e}`),L.value=e,W.value=t,D()}function we(t,e){f.success(`${l("gotopage")} : ${t}/${e}`),j.value=e,B.value=t,ne()}const _=t=>!t||t=="-0"?0:t%1!==0?(Math.round(t*100)/100).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),v=d([w().startOf("day").toDate(),w().endOf("day").toDate()]);return(t,e)=>{const o=Ye,N=Se,ae=Re,E=Te,le=$e,se=xe,K=Le,A=Ie,F=We,ke=Ne,be=ze;return k(),Z("div",null,[n(a(V),{vertical:"",size:"large"},{default:s(()=>[n(E,null,{default:s(()=>[u("div",Ze,[u("div",null,[u("label",Qe,m(t.$t("loginname")),1)]),u("div",qe,[n(o,{class:"w-full",value:a(H),"onUpdate:value":e[0]||(e[0]=r=>x(H)?H.value=r:null),type:"text",placeholder:t.$t("loginname")},null,8,["value","placeholder"])]),u("div",null,[u("label",Je,m(t.$t("currency")),1)]),u("div",Xe,[n(N,{value:a(M),"onUpdate:value":e[1]||(e[1]=r=>x(M)?M.value=r:null),class:"w-full",options:a(te)},null,8,["value","options"])]),n(a(y),{type:"primary",class:"w-full xl:w-auto my-1",onClick:e[2]||(e[2]=r=>D())},{default:s(()=>[n(ae),u("span",et,m(t.$t("search")),1)]),_:1})])]),_:1}),n(E,null,{default:s(()=>[n(a(V),{vertical:""},{default:s(()=>[n(le,{"scroll-x":a(g).position_type==1||a(g).position_type==2?1200:1e3,loading:a(h),columns:a(de),data:a(X),bordered:!1},null,8,["scroll-x","loading","columns","data"]),n(se,{class:"overflow-auto",count:a(J),onChange:he},null,8,["count"])]),_:1})]),_:1})]),_:1}),n(F,{show:a(b),"onUpdate:show":e[8]||(e[8]=r=>x(b)?b.value=r:null),class:"w-350px sm:w-500px fixed inset-x-0 max-h-[80vh] h-70 overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:s(()=>[i(m(t.$t("deposit")),1)]),default:s(()=>[n(A,null,{default:s(()=>[u("div",null,[u("div",tt,[u("p",null,m(t.$t("username"))+" :",1),n(a(y),{tertiary:"",type:a(p).position_type===2?"warning":a(p).position_type===3?"success":"info",class:"cursor-default"},{default:s(()=>[i(m(a(p).username),1)]),_:1},8,["type"])]),u("div",nt,[u("p",null,m(t.$t("deposit"))+" :",1),a(p).position_type==1||a(p).position_type==2?(k(),T(N,{key:0,value:a(p).currency,"onUpdate:value":e[3]||(e[3]=r=>a(p).currency=r),options:a(te),placeholder:t.$t("currency")},null,8,["value","options","placeholder"])):Y("",!0),a(p).position_type==3?(k(),T(o,{key:1,value:a(p).ag_currency,"onUpdate:value":e[4]||(e[4]=r=>a(p).ag_currency=r),disabled:"",placeholder:t.$t("currency")},null,8,["value","placeholder"])):Y("",!0),n(o,{value:a(p).amountcurrency,"onUpdate:value":e[5]||(e[5]=r=>a(p).amountcurrency=r),placeholder:t.$t("amount"),type:"number"},null,8,["value","placeholder"])])]),n(K,{justify:"end",class:"mt-15"},{default:s(()=>[n(a(y),{type:"error",class:"mr-2",onClick:e[6]||(e[6]=r=>b.value=!1)},{icon:s(()=>e[19]||(e[19]=[])),default:s(()=>[i(" "+m(t.$t("cancel")),1)]),_:1}),n(a(y),{type:"success",onClick:e[7]||(e[7]=r=>fe())},{icon:s(()=>e[20]||(e[20]=[])),default:s(()=>[i(" "+m(t.$t("save")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["show"]),n(F,{show:a($),"onUpdate:show":e[14]||(e[14]=r=>x($)?$.value=r:null),class:"w-350px sm:w-500px fixed inset-x-0 max-h-[80vh] h-70 overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:s(()=>[i(m(t.$t("withdraw")),1)]),default:s(()=>[n(A,null,{default:s(()=>[u("div",null,[u("div",at,[u("p",null,m(t.$t("username"))+" :",1),n(a(y),{tertiary:"",type:a(c).position_type===2?"warning":a(c).position_type===3?"success":"info",class:"cursor-default"},{default:s(()=>[i(m(a(c).username),1)]),_:1},8,["type"])]),u("div",lt,[u("p",null,m(t.$t("withdraw"))+" :",1),a(c).position_type==1||a(c).position_type==2?(k(),T(N,{key:0,value:a(c).currency,"onUpdate:value":e[9]||(e[9]=r=>a(c).currency=r),options:ve(a(c)),placeholder:t.$t("currency")},null,8,["value","options","placeholder"])):Y("",!0),a(c).position_type==3?(k(),T(o,{key:1,value:a(c).ag_currency,"onUpdate:value":e[10]||(e[10]=r=>a(c).ag_currency=r),disabled:"",placeholder:t.$t("currency")},null,8,["value","placeholder"])):Y("",!0),n(o,{value:a(c).amountcurrency,"onUpdate:value":e[11]||(e[11]=r=>a(c).amountcurrency=r),placeholder:t.$t("amount"),type:"number"},null,8,["value","placeholder"])])]),n(K,{justify:"end",class:"mt-15"},{default:s(()=>[n(a(y),{type:"error",class:"mr-2",onClick:e[12]||(e[12]=r=>$.value=!1)},{icon:s(()=>e[21]||(e[21]=[])),default:s(()=>[i(" "+m(t.$t("cancel")),1)]),_:1}),n(a(y),{type:"success",onClick:e[13]||(e[13]=r=>ge())},{icon:s(()=>e[22]||(e[22]=[])),default:s(()=>[i(" "+m(t.$t("save")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["show"]),n(F,{show:a(P),"onUpdate:show":e[18]||(e[18]=r=>x(P)?P.value=r:null),class:"w-350px sm:w-800px fixed inset-x-0 max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:s(()=>[i(m(t.$t("detail"))+" ",1),n(a(y),{round:"",tertiary:"",type:a(U).position_type===2?"warning":a(U).position_type===3?"success":"info",class:"cursor-default ml-2"},{default:s(()=>[i(m(a(U).username),1)]),_:1},8,["type"])]),default:s(()=>[n(A,null,{default:s(()=>[u("div",st,[n(be,{locale:a(R),"date-locale":a(I)},{default:s(()=>[n(ke,{class:"my-2",value:a(v),"onUpdate:value":e[15]||(e[15]=r=>x(v)?v.value=r:null),type:"daterange",format:"dd-MM-yyyy",clearable:""},null,8,["value"])]),_:1},8,["locale","date-locale"]),n(a(y),{type:"primary",class:"my-2 w-full sm:w-auto",onClick:e[16]||(e[16]=r=>ne())},{icon:s(()=>[n(ae)]),default:s(()=>[i(" "+m(t.$t("search")),1)]),_:1})]),n(E,null,{default:s(()=>[n(a(V),{vertical:""},{default:s(()=>[n(le,{"scroll-x":500,loading:a(h),columns:a(pe),data:a(G),bordered:!1},null,8,["loading","columns","data"]),n(se,{class:"overflow-auto",count:a(z),onChange:we},null,8,["count"])]),_:1})]),_:1}),n(K,{justify:"end",class:"mt-5"},{default:s(()=>[n(a(y),{type:"error",class:"mr-2",onClick:e[17]||(e[17]=r=>P.value=!1)},{icon:s(()=>e[23]||(e[23]=[])),default:s(()=>[i(" "+m(t.$t("cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["show"])])}}});export{yt as default};
