<script setup lang="ts">
import http from "@/service/axios";
const { t, locale } = useI18n();
import Baccarat from "./setting/baccarat.vue";

const status = ref(false);
function handleSave(newStatus) {
  status.value = newStatus;
}

const UserData = ref(null);

onMounted(() => {
  getProfile();
});

const gamelist = ref([
  {
    id: 1,
    gameName_th: "บาคาร่า",
    gameName_en: "Baccarat",
    gameIcon: "/images/demo/baccarat.jpg",
    isComming: true,
  },
  {
    id: 2,
    gameName_th: "เสือมังกร",
    gameName_en: "Tiger Dragon",
    gameIcon: "/images/demo/tigerdragon.jpg",
    isComming: false,
  },
]);

const playGame = async (data: any) => {
  if (data.id === 1) {
    // Baccarat game
    status.value = true;
  } else {
    // Handle other games
    console.log("Playing game:", data.gameName_en);
  }
};

const getProfile = async () => {
  try {
    const res = await http.get("v1/Profile/agent");
    if (res.data) {
      UserData.value = res.data;
    }
  } catch (error) {
    console.log(error);
  }
};
</script>

<template>
  <div v-if="UserData">
    <n-space vertical size="large">
      <n-card v-if="status">
        <Baccarat @save="handleSave" />
      </n-card>

      <n-card v-if="!status" class="">
        <div
          class="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-10"
        >
          <div
            v-for="item in gamelist"
            :key="item.id"
            class="text-center"
            @click="playGame(item)"
          >
            <div
              class="relative transition-transform duration-300 hover:scale-95"
              :class="{ grayscale: !item.isComming }"
            >
              <img
                :src="item.gameIcon"
                class="w-full h-full object-cover rounded-xl cursor-pointer"
              />
              <div
                class="absolute top-2 right-2 sm:top-3 sm:right-4 bg-black/70 text-white p-1 sm:p-2 rounded-md text-[9px] sm:text-xs font-semibold backdrop-blur-lg uppercase"
              >
                {{
                  item.isComming
                    ? locale == "thTH"
                      ? "ทดลองเล่น"
                      : "Demo"
                    : locale == "thTH"
                    ? "เร็วๆ นี้"
                    : "Coming Soon"
                }}
              </div>
            </div>
            <div class="text-md mt-2">
              {{ locale == "thTH" ? item.gameName_th : item.gameName_en }}
            </div>
          </div>
        </div>
      </n-card>
    </n-space>
  </div>
</template>
