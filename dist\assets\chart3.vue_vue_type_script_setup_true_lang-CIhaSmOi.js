import{d as t,r as a,aj as o,m as r,o as n}from"./index-pY9FjpQW.js";const l={ref:"lineRef",class:"h-400px"},p=t({__name:"chart3",setup(i){const e=a({tooltip:{trigger:"item",formatter:"{b} : {d}%"},legend:{orient:"horizontal",top:30,padding:5,itemWidth:20,itemHeight:12,textStyle:{color:"#777"}},series:[{type:"pie",radius:["45%","60%"],center:["50%","50%"],label:{show:!0,formatter:"{b} : {d}%",color:"#777"},labelLine:{show:!0,length2:10},data:[{value:335,name:"ทดสอบ"},{value:77,name:"ทดสอบ1"},{value:82,name:"ทดสอบ2"},{value:421,name:"ทดสอบ3"}]}]});return o("lineRef",e),(s,c)=>(n(),r("div",l,null,512))}});export{p as _};
