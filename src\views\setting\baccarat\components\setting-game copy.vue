<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useMessage } from "naive-ui";
import { useI18n } from "vue-i18n";
import http from "@/service/axios";
import { useAuthStore } from "@/store/auth";

const { userData } = useAuthStore();
const message = useMessage();
const { t } = useI18n();
const isChecked = ref(false);
const level = ref(0);

const handleSubmit = () => {
  const obj = {
    level: level.value,
    status: isChecked.value,
  };
  Uncomment and adjust the API call as needed
  http.post("v1/Management/UpdateSetting", obj).then((response) => {
    if (response.data.status) {
      message.success(response.data.mes);
      getData();
    } else {
      message.error(response.data.mes);
    }
  });
};

const checkPrime = () => {
  if (
    (userData?.position_type == 4 && userData?.upline_position_type == 3) ||
    (userData?.position_type == 4 && userData?.upline_position_type == 2) ||
    (userData?.position_type == 4 && userData?.upline_position_type == 1)
  ) {
    const permis = JSON.parse(userData?.permissions);
    if (permis.setting !== 2) {
      return false;
    }
  }
  return true;
};

const handleSwitchChange = (value: boolean) => {
  isChecked.value = value;
};

onMounted(() => {
  getData();
});

const getData = async () => {
  try {
    const { data: res } = await http.get("v1/Management/Setting");
    if (res) {
      level.value = res.level;
      isChecked.value = res.status;
    }
  } catch (err) {
    console.log(err);
  }
};
</script>

<template>
  <div class="flex items-start justify-center">
    <div class="w-full max-w-4xl">
      <n-card class="shadow-xl">
        <template #header>
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-3">
              <nova-icon icon="icon-park-outline:setting" class="text-2xl" />
              <span class="text-lg font-medium">{{ $t("setting") }}</span>
            </div>
            <div v-if="checkPrime()">
              <n-button type="primary" @click="handleSubmit">
                <template #icon>
                  <icon-park-outline-check />
                </template>
                {{ $t("save") }}
              </n-button>
            </div>
          </div>
        </template>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 min-h-[60vh]">
          <!-- Left Section - Controls -->
          <div class="flex flex-col justify-center space-y-8">
            <!-- Switch Section -->
            <n-card class="p-6">
              <div class="text-center space-y-4">
                <h3 class="text-xl font-semibold mb-4">{{ $t("status") }}</h3>
                <n-switch
                  v-model:value="isChecked"
                  @update:value="handleSwitchChange"
                  :disabled="!checkPrime()"
                  size="large"
                >
                  <template #checked>
                {{ $t("enabled") }}
                  </template>
                  <template #unchecked>
                {{
                      $t("disabled")
                    }}
                  </template>
                </n-switch>
              </div>
            </n-card>

            <!-- Level Input Section -->
            <n-card class="p-6">
              <div class="space-y-6">
                <div class="flex items-center justify-center gap-4">
                  <icon-park-outline-game class="text-xl text-orange-500" />
                  <n-text class="text-xl font-semibold ">{{
                    $t("ปรับแตก")
                  }}</n-text>
                  <n-tag type="warning" size="large" class="text-lg px-4 py-2"
                    >{{ level }}%</n-tag
                  >
                </div>

                <div class="max-w-md mx-auto">
                  <n-input-number
                    v-model:value="level"
                    :disabled="!checkPrime()"
                    :min="0"
                    :max="100"
                    size="large"
                    class="w-full"
                  >
                    <template #suffix>%</template>
                  </n-input-number>
                </div>
              </div>
            </n-card>
          </div>

          <!-- Right Section - Slider -->
          <div class="flex items-center justify-center">
            <n-card class="p-8 h-full flex items-center justify-center">
              <div class="flex flex-col items-center space-y-4">
                <h3 class="text-xl font-semibold mb-4">
                  {{ $t("level") }} {{ $t("control") }}
                </h3>
                <div
                  class="flex items-center justify-center"
                  style="height: 400px"
                >
                  <n-slider
                    vertical
                    v-model:value="level"
                    :min="0"
                    :max="100"
                    :step="1"
                    :disabled="!checkPrime()"
                    :marks="{
                      0: '0%',
                      25: '25%',
                      50: '50%',
                      75: '75%',
                      100: '100%',
                    }"
                    class="h-80"
                  />
                </div>
                <div class="text-center">
                  <n-text class="text-3xl font-bold text-primary"
                    >{{ level }}%</n-text
                  >
                </div>
              </div>
            </n-card>
          </div>
        </div>
      </n-card>
    </div>
  </div>
</template>

<style scoped></style>
