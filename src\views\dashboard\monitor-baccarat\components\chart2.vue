<script setup lang="ts">
import { useAppStore } from "@/store";
import * as echarts from "echarts";

const appStore = useAppStore();
const primaryColor = computed(() => appStore.primaryColor);

function hexToRgba(hex: string, alpha = 1): string {
  const sanitizedHex = hex.replace("#", "");

  if (sanitizedHex.length !== 6) return hex;

  const bigint = parseInt(sanitizedHex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

const rgbaPrimary = computed(() => hexToRgba(primaryColor.value, 0.2));
const rgbaPrimary2 = computed(() => hexToRgba(primaryColor.value, 0.05));

let myChart: echarts.ECharts | null = null;
const barRef = ref<HTMLElement | null>(null);

const thaiMonths = [
  "ม.ค.",
  "ก.พ.",
  "มี.ค.",
  "เม.ย.",
  "พ.ค.",
  "มิ.ย.",
  "ก.ค.",
  "ส.ค.",
  "ก.ย.",
  "ต.ค.",
  "พ.ย.",
  "ธ.ค.",
];

const updateChart = () => {
  if (myChart) {
    const option: echarts.EChartsOption = {
      title: {
        left: "center",
        textStyle: {
          color: primaryColor.value,
          fontSize: 18,
        },
        subtextStyle: {
          color: "#6B7280",
          fontSize: 12,
        },
      },
      tooltip: {
         axisPointer: {
          type: "shadow",
          label: {
            backgroundColor: "#6a7985",
          },
        },
        trigger: "axis",
        // axisPointer: {
        //   type: "shadow",
        // },
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        borderRadius: 10,
        padding: [10, 14],
        borderColor: "#E5E7EB",
        borderWidth: 1,
        textStyle: { color: "#374151", fontSize: 13 },
        formatter: (params: any) => {
          const item = params[0];
          const value = new Intl.NumberFormat("th-TH").format(item.value);
          return `<div style="font-weight:600;margin-bottom:6px;font-size:14px">${item.axisValue}</div>
                  <div style="color:${primaryColor.value};">${item.marker} จำนวนเงินทั้งหมด : ${value}</div>`;
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: thaiMonths,
        axisTick: { alignWithLabel: true, lineStyle: { color: "#E5E7EB" } },
        axisLine: { lineStyle: { color: "#E5E7EB" } },
        axisLabel: { fontSize: 13, color: "#6B7280", padding: [8, 0] },
      },
      yAxis: {
        type: "value",
        splitLine: { lineStyle: { type: "dashed" } },
        axisLabel: {
          color: "#6B7280",
          fontSize: 13,
          padding: [0, 8],
          formatter: (v: number) =>
            new Intl.NumberFormat("th-TH", {
              notation: "compact",
              compactDisplay: "short",
            }).format(v),
        },
      },
      series: [
        {
          type: "bar",
          barWidth: "60%",
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: primaryColor.value },
              { offset: 1, color: rgbaPrimary.value },
            ]),
          },
          data: data.value,
        },
      ],
      legend: {
        data: ["Reports"],
        bottom: "0",
        textStyle: {
          color: "#6B7280",
        },
      },
    };
    myChart.setOption(option);
  }
};

const data = ref([]);
const randomData = () => {
  for (let i = 0; i < 12; i++) {
    data.value.push(Math.floor(Math.random() * 10000));
  }
};

onMounted(() => {
  randomData();
  if (barRef.value) {
    myChart = echarts.init(barRef.value);
    updateChart();
  }
  window.addEventListener("resize", () => {
    if (myChart) {
      myChart.resize();
    }
  });
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
  }
});

watch(() => appStore.primaryColor, updateChart);
</script>

<template>
  <div class="-mt-5">
    <div ref="barRef" class="h-400px w-full" />
  </div>
</template>
