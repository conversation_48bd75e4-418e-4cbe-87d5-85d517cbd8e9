import{d as P,ac as T,U as V,r as i,m as B,o as C,b as e,w as n,g as o,t as a,f as l,fT as R,af as N,B as z,fD as D}from"./index-pY9FjpQW.js";import{a as I}from"./headers-CSI__REg.js";import{_ as U}from"./Form-_sVFK3VR.js";import{_ as j}from"./FormItem-B4P0FvHJ.js";import{_ as q}from"./text-GY30jg6U.js";const G=P({__name:"index",emits:["update:modelValue"],setup(A,{emit:p}){const m=p;function f(){m("update:modelValue","login")}const{t:d}=T(),g=V(()=>({account:{required:!0,trigger:"blur",message:d("login.resetPasswordRuleTip")}})),s=i({account:""}),_=i(null);function h(){_.value?.validate()}return(t,r)=>{const v=I,w=R,u=j,c=z,y=q,b=D,k=N,$=U;return C(),B("div",null,[e(v,{depth:"3",class:"text-center"},{default:n(()=>[o(a(t.$t("login.resetPasswordTitle")),1)]),_:1}),e($,{ref_key:"formRef",ref:_,rules:l(g),model:l(s),"show-label":!1,size:"large"},{default:n(()=>[e(u,{path:"account"},{default:n(()=>[e(w,{value:l(s).account,"onUpdate:value":r[0]||(r[0]=x=>l(s).account=x),clearable:"",placeholder:t.$t("login.resetPasswordPlaceholder")},null,8,["value","placeholder"])]),_:1}),e(u,null,{default:n(()=>[e(k,{vertical:"",size:20,class:"w-full"},{default:n(()=>[e(c,{block:"",type:"primary",onClick:h},{default:n(()=>[o(a(t.$t("login.resetPassword")),1)]),_:1}),e(b,{justify:"center"},{default:n(()=>[e(y,null,{default:n(()=>[o(a(t.$t("login.haveAccountText")),1)]),_:1}),e(c,{text:"",type:"primary",onClick:f},{default:n(()=>[o(a(t.$t("login.signIn")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"])])}}});export{G as _};
