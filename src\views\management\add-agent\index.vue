<script setup lang="tsx">
// eslint-disable-next-line ts/ban-ts-comment
// @ts-nocheck
import http from "@/service/axios";
const router = useRouter();
const message = useMessage();
const current = ref<number>(1);
const currentStatus = ref<string>("process");
const selectedCurrencies = ref<string[]>([]);
const walletAmounts = ref<Record<string, number>>({});
const agent = ref(false);
const positionType = ref("RESELLER");
const typeAgent = ref("SEAMLESS");
const currencyOptions = ref([
  { label: "THB", value: "THB" },
  { label: "KRW", value: "KRW" },
  { label: "USD", value: "USD" },
]);

const formRef = ref();
const formModel = ref({
  username: "",
  password: "",
  confirmPassword: "",
  seamlessInput: "",
});
const { t } = useI18n();
const rules = computed(() => ({
  username: [{ required: true, message: t("validusername"), trigger: "blur" }],
  password: [{ required: true, message: t("validpassword"), trigger: "blur" }],
  confirmPassword: [
    {
      required: true,
      message: t("validconfirmpassword"),
      trigger: "blur",
    },
    {
      validator: (rule, value) => value === formModel.value.password,
      message: t("validmatchpassword"),
      trigger: "blur",
    },
  ],
}));

const selectedPercentage = ref("0.00%");
const selectedHoldPercentage = ref(100);
const options = ref([
  {
    whateverLabel: "0.00%",
    whateverValue: "0.00%",
  },
  {
    whateverLabel: "0.50%",
    whateverValue: "0.50%",
  },
  {
    whateverLabel: "1.00%",
    whateverValue: "1.00%",
  },
]);

const percents = ref([
  {
    whateverLabel: "100.00%",
    whateverValue: 100,
  },
  {
    whateverLabel: "50.00%",
    whateverValue: 50,
  },
  {
    whateverLabel: "0.00%",
    whateverValue: 0,
  },
]);

const canProceedToStep = computed(() => {
  return selectedCurrencies.value.every(
    (currency) => walletAmounts.value[currency] > 0
  );
});

async function next() {
  if (current.value === 1) {
    const valid = await formRef.value?.validate();
    if (!valid) return;
    const params = {
      username: formModel.value.username,
    };
    await http.get("v1/Profile/CheckUser", { params }).then((response) => {
      if (!response.data.success) {
        message.error(response.data.mes);
      } else {
        current.value++;
      }
    });
  } else if (
    (current.value === 2 && !canProceedToStep.value) ||
    !selectedCurrencies.value.length ||
    CurrencyCheck.value
  ) {
    message.error(t("balaceNot0"));
    return;
  } else {
    current.value++;
  }
  // if (current.value < 3)
}

function prev() {
  if (current.value > 1) current.value--;
}

function reset() {
  current.value = 1;
  positionType.value = "RESELLER";
  agent.value = false;
  our_percentage.value = 0;
  our_percentage_krw.value = 0;
  our_percentage_usd.value = 0;
  formModel.value = {
    username: "",
    password: "",
    confirmPassword: "",
    seamlessInput: "",
  };
  selectedCurrencies.value = [];
  walletAmounts.value = {};
  Credit();
  Pct();
}
const isMobile = computed(() => screenWidth.value <= 768);
const screenWidth = ref(window.innerWidth);
function updateScreenWidth() {
  screenWidth.value = window.innerWidth;
}
onMounted(() => {
  Credit();
  Pct();
  window.addEventListener("resize", updateScreenWidth);
});
onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
});
const credit_thb = ref(0);
const credit_krw = ref(0);
const credit_usd = ref(0);
const hold_percent = ref(0);
const hold_percent_krw = ref(0);
const hold_percent_usd = ref(0);
const hold_percentage = ref(0);
const our_percentage = ref(0);
const hold_percentage_krw = ref(0);
const our_percentage_krw = ref(0);
const hold_percentage_usd = ref(0);
const our_percentage_usd = ref(0);
const Credit = async () => {
  const { data: res } = await http.get("v1/wallet/credit");
  credit_thb.value = res.credit_thb;
  credit_krw.value = res.credit_krw;
  credit_usd.value = res.credit_usd;
};
const Pct = async () => {
  const { data: res } = await http.get("v1/Profile/agent");
  if (res.success) {
    hold_percent.value = res.data.hold_percent;
    hold_percent_krw.value = res.data.hold_percent_krw;
    hold_percent_usd.value = res.data.hold_percent_usd;

    hold_percentage.value = res.data.hold_percent;
    hold_percentage_krw.value = res.data.hold_percent_krw;
    hold_percentage_usd.value = res.data.hold_percent_usd;
  }
};
const CurrencyCheck = ref(false);
const CheckCredit = (type, Credit) => {
  if (type == "THB" && Number(Credit) > credit_thb.value) {
    CurrencyCheck.value = true;
  } else if (type == "KRW" && Number(Credit) > credit_krw.value) {
    CurrencyCheck.value = true;
  } else if (type == "USD" && Number(Credit) > credit_usd.value) {
    CurrencyCheck.value = true;
  } else {
    CurrencyCheck.value = false;
  }
};
const genPtSelect = (max_pt: any) => {
  try {
    const result = [];
    for (let i = 0; i <= max_pt; i += 0.5) {
      result.push({
        state: `${i.toFixed(2)}%`,
        abbr: i,
      });
    }
    return result;
  } catch (e) {
    console.log(e);
    return [];
  }
};

const calPtOur = (pt_type: string, currency: string) => {
  if (currency == "THB") {
    if (pt_type == "our") {
      hold_percentage.value = hold_percent.value - our_percentage.value;
    } else {
      our_percentage.value = hold_percent.value - hold_percentage.value;
    }
  } else if (currency == "KRW") {
    if (pt_type == "our") {
      hold_percentage_krw.value =
        hold_percent_krw.value - our_percentage_krw.value;
    } else {
      our_percentage_krw.value =
        hold_percent_krw.value - hold_percentage_krw.value;
    }
  } else if (currency == "USD") {
    if (pt_type == "our") {
      hold_percentage_usd.value =
        hold_percent_usd.value - our_percentage_usd.value;
    } else {
      our_percentage_usd.value =
        hold_percent_usd.value - hold_percentage_usd.value;
    }
  }
};
const Submit = () => {
  const obj = {
    username: formModel.value.username,
    password: formModel.value.password,
    position_type: agent.value ? 3 : 2,
    bet_type:
      agent.value && typeAgent.value == "SEAMLESS"
        ? 1
        : agent.value && typeAgent.value == "TRANSFER"
        ? 2
        : null,
    currency: selectedCurrencies.value,
    credit: walletAmounts.value.THB ? walletAmounts.value.THB : 0,
    credit_krw: walletAmounts.value.KRW ? walletAmounts.value.KRW : 0,
    credit_usd: walletAmounts.value.USD ? walletAmounts.value.USD : 0,
    callback_url:
      agent.value &&
      typeAgent.value == "SEAMLESS" &&
      formModel.value.seamlessInput
        ? formModel.value.seamlessInput
        : null,
    hold_percent: selectedCurrencies.value.includes("THB")
      ? hold_percentage.value
      : 0,
    our_percent: selectedCurrencies.value.includes("THB")
      ? our_percentage.value
      : 0,
    hold_percentage_krw: selectedCurrencies.value.includes("KRW")
      ? hold_percentage_krw.value
      : 0,
    our_percentage_krw: selectedCurrencies.value.includes("KRW")
      ? our_percentage_krw.value
      : 0,
    hold_percentage_usd: selectedCurrencies.value.includes("USD")
      ? hold_percentage_usd.value
      : 0,
    our_percentage_usd: selectedCurrencies.value.includes("USD")
      ? our_percentage_usd.value
      : 0,
  };
  http.post("v1/Account/Store", obj).then((response) => {
    if (response.data.success) {
      message.success(response.data.mes);
      router.push("/management/agent-list");
    } else {
      message.error(response.data.mes);
    }
  });
};
watch(selectedCurrencies, (newCurrencies) => {
  if (agent.value && newCurrencies.length > 1) {
    message.error(t("onlyOneCurrencyForAgent"));
    selectedCurrencies.value = [newCurrencies[0]]; // เลือกสกุลแรกเพื่อจำกัดแค่หนึ่งสกุล
  }
});
watch(selectedCurrencies, (newCurrencies, oldCurrencies) => {
  // เพิ่มค่าใหม่ที่ถูกเลือก
  newCurrencies.forEach((currency) => {
    if (!(currency in walletAmounts.value)) {
      walletAmounts.value[currency] = ""; // ตั้งค่าเริ่มต้น
    }
  });

  // ลบค่าที่ถูกนำออก
  Object.keys(walletAmounts.value).forEach((currency) => {
    if (!newCurrencies.includes(currency)) {
      delete walletAmounts.value[currency];
    }
    if (currency) {
      if (
        currency == "THB" &&
        Number(walletAmounts.value[currency]) > credit_thb.value
      ) {
        CurrencyCheck.value = true;
      } else if (
        currency == "KRW" &&
        Number(walletAmounts.value[currency]) > credit_krw.value
      ) {
        CurrencyCheck.value = true;
      } else if (
        currency == "USD" &&
        Number(walletAmounts.value[currency]) > credit_usd.value
      ) {
        CurrencyCheck.value = true;
      } else {
        CurrencyCheck.value = false;
      }
    }
  });
});
</script>

<template>
  <div>
    <div class="max-w-7xl mx-auto">
      <!-- Progress Steps -->
      <n-card class="mb-4">
        <n-steps
          :current="current"
          :status="currentStatus"
          :vertical="isMobile"
          class="px-4"
        >
          <n-step>
            <template #title>
              <div class="flex items-center gap-2">
                <icon-park-outline-user class="text-lg" />
                <span>{{ $t("accountsetup") }}</span>
              </div>
            </template>
            <template #description>
              {{ $t("createagentinfomation") }}
            </template>
          </n-step>

          <n-step>
            <template #title>
              <div class="flex items-center gap-2">
                <icon-park-outline-wallet class="text-lg" />
                <span>{{ $t("walletsetup") }}</span>
              </div>
            </template>
            <template #description>
              {{ $t("managewalletcredit") }}
            </template>
          </n-step>

          <n-step>
            <template #title>
              <div class="flex items-center gap-2">
                <icon-park-outline-setting class="text-lg" />
                <span>{{ $t("royaltysetting") }}</span>
              </div>
            </template>
            <template #description>
              {{ $t("configureproductroyalty") }}
            </template>
          </n-step>
        </n-steps>
      </n-card>

      <!-- Step Content -->
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
        <!-- Main Content -->
        <div class="lg:col-span-8">
          <!-- Step 1: Account Setup -->
          <n-card v-if="current === 1" :title="$t('accountsetup')">
            <template #header-extra>
              <icon-park-outline-user class="text-xl text-blue-500" />
            </template>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Account Form -->
              <div>
                <h3
                  class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300"
                >
                  {{ $t("accountinformation") }}
                </h3>
                <n-form
                  ref="formRef"
                  :model="formModel"
                  :rules="rules"
                  vertical
                >
                  <n-form-item :label="$t('username')" path="username">
                    <n-input
                      v-model:value="formModel.username"
                      :placeholder="$t('username')"
                      size="large"
                    >
                      <template #prefix>
                        <icon-park-outline-user />
                      </template>
                    </n-input>
                  </n-form-item>

                  <n-form-item :label="$t('password')" path="password">
                    <n-input
                      v-model:value="formModel.password"
                      type="password"
                      :placeholder="$t('password')"
                      size="large"
                      show-password-on="click"
                    >
                      <template #prefix>
                        <icon-park-outline-lock />
                      </template>
                    </n-input>
                  </n-form-item>

                  <n-form-item
                    :label="$t('confirmpassword')"
                    path="confirmPassword"
                  >
                    <n-input
                      v-model:value="formModel.confirmPassword"
                      type="password"
                      :placeholder="$t('confirmpassword')"
                      size="large"
                      show-password-on="click"
                    >
                      <template #prefix>
                        <icon-park-outline-lock />
                      </template>
                    </n-input>
                  </n-form-item>
                </n-form>
              </div>

              <!-- Position Type Selection -->
              <div>
                <h3
                  class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300"
                >
                  {{ $t("positiontype") }}
                </h3>

                <div class="space-y-4">
                  <n-card
                    :class="[
                      'cursor-pointer transition-all duration-200 hover:shadow-md',
                      positionType === 'RESELLER'
                        ? 'ring-2 ring-orange-400 bg-orange-50 dark:bg-orange-900/20'
                        : '',
                    ]"
                    @click="
                      positionType = 'RESELLER';
                      agent = false;
                    "
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-3">
                        <n-avatar color="#f59e0b" size="medium">
                          <icon-park-outline-crown />
                        </n-avatar>
                        <div>
                          <h4 class="font-medium">{{ $t("reseller") }}</h4>
                        </div>
                      </div>
                      <n-radio :checked="positionType === 'RESELLER'" />
                    </div>
                  </n-card>

                  <n-card
                    :class="[
                      'cursor-pointer transition-all duration-200 hover:shadow-md',
                      positionType === 'AGENT'
                        ? 'ring-2 ring-green-400 bg-green-50 dark:bg-green-900/20'
                        : '',
                    ]"
                    @click="
                      positionType = 'AGENT';
                      agent = true;
                      typeAgent = 'SEAMLESS';
                      selectedCurrencies = [];
                    "
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-3">
                        <n-avatar color="#10b981" size="medium">
                          <icon-park-outline-people />
                        </n-avatar>
                        <div>
                          <h4 class="font-medium">{{ $t("agent") }}</h4>
                        </div>
                      </div>
                      <n-radio :checked="positionType === 'AGENT'" />
                    </div>
                  </n-card>

                  <!-- Agent Type Selection -->
                  <div
                    v-if="agent"
                    class="mt-4 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
                  >
                    <h4 class="font-medium mb-3">{{ $t("bettype") }}</h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <n-button
                        :type="typeAgent === 'SEAMLESS' ? 'error' : 'default'"
                        size="large"
                        block
                        @click="typeAgent = 'SEAMLESS'"
                      >
                        <template #icon>
                          <icon-park-outline-api />
                        </template>
                        Seamless
                      </n-button>
                      <n-button
                        :type="typeAgent === 'TRANSFER' ? 'info' : 'default'"
                        size="large"
                        block
                        @click="typeAgent = 'TRANSFER'"
                      >
                        <template #icon>
                          <icon-park-outline-transfer />
                        </template>
                        Transfer
                      </n-button>
                    </div>

                    <div v-if="typeAgent === 'SEAMLESS'" class="mt-4">
                      <n-form-item label="Callback API URL">
                        <n-input
                          v-model:value="formModel.seamlessInput"
                          placeholder="https://your-api-endpoint.com/callback"
                          size="large"
                        >
                          <template #prefix>
                            <icon-park-outline-link />
                          </template>
                        </n-input>
                      </n-form-item>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </n-card>

          <!-- Step 2: Wallet Setup -->
          <n-card v-if="current === 2" :title="$t('walletsetup')">
            <template #header-extra>
              <icon-park-outline-wallet class="text-xl text-green-500" />
            </template>

            <div class="space-y-4">
              <n-form ref="formRef">
                <n-form-item :label="$t('walletcurrency')">
                  <n-select
                    v-model:value="selectedCurrencies"
                    multiple
                    :options="currencyOptions"
                    :placeholder="$t('selectcurrency')"
                    size="large"
                  />
                </n-form-item>
              </n-form>

              <div
                v-if="selectedCurrencies.length"
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
              >
                <n-card
                  v-for="currency in selectedCurrencies"
                  :key="currency"
                  class="shadow-md"
                >
                  <div class="flex items-center gap-2">
                    <img
                      :src="`/images/country/${
                        currency === 'THB'
                          ? 'th'
                          : currency === 'KRW'
                          ? 'kr'
                          : 'us'
                      }.webp`"
                      :alt="currency"
                      class="w-6 h-6 rounded"
                    />
                    <span class="font-semibold text-lg">{{ currency }}</span>
                  </div>
                  <n-form-item
                    :feedback="`${$t('min')} 1, ${$t('max')} ${
                      currency == 'THB'
                        ? credit_thb
                        : currency == 'KRW'
                        ? credit_krw
                        : credit_usd
                    }`"
                  >
                    <n-input-number
                      v-model:value="walletAmounts[currency]"
                      :placeholder="$t('amount')"
                      size="large"
                      :min="1"
                      :max="
                        currency == 'THB'
                          ? credit_thb
                          : currency == 'KRW'
                          ? credit_krw
                          : credit_usd
                      "
                      class="w-full"
                      @input="CheckCredit(currency, walletAmounts[currency])"
                    >
                      <template #suffix>
                        {{
                          currency === "THB"
                            ? "฿"
                            : currency === "KRW"
                            ? "₩"
                            : "$"
                        }}
                      </template>
                    </n-input-number>
                  </n-form-item>

                  <div
                    class="mt-3 p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
                  >
                    <div
                      class="flex flex-col justify-between text-sm text-center"
                    >
                      <span>{{ $t("availablebalance") }}</span>
                      <span class="font-semibold text-green-600">
                        {{
                          currency === "THB"
                            ? credit_thb.toLocaleString()
                            : currency === "KRW"
                            ? credit_krw.toLocaleString()
                            : credit_usd.toLocaleString()
                        }}
                        {{
                          currency === "THB"
                            ? "฿"
                            : currency === "KRW"
                            ? "₩"
                            : "$"
                        }}
                      </span>
                    </div>
                  </div>
                </n-card>
              </div>

              <n-alert v-if="CurrencyCheck" type="error" class="mt-4">
                {{ $t("insufficientbalance") }}
              </n-alert>
            </div>
          </n-card>

          <!-- Step 3: Royalty Setting -->
          <n-card v-if="current === 3" :title="$t('royaltysetting')">
            <template #header-extra>
              <icon-park-outline-setting class="text-xl text-purple-500" />
            </template>

            <div class="space-y-4">
              <n-card
                v-for="currency in selectedCurrencies"
                :key="currency"
                class=""
              >
                <div class="flex items-center gap-3 mb-4">
                  <img
                    :src="`/images/country/${
                      currency === 'THB'
                        ? 'th'
                        : currency === 'KRW'
                        ? 'kr'
                        : 'us'
                    }.webp`"
                    :alt="currency"
                    class="w-8 rounded"
                  />
                  <h3 class="text-xl font-semibold">
                    {{ currency }} {{ $t("royaltysetting") }}
                  </h3>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Hold Percentage -->
                  <div>
                    <n-form-item :label="`${$t('holdpercentage')} (%)`">
                      <n-select
                        v-if="currency === 'THB'"
                        v-model:value="hold_percentage"
                        :options="
                          genPtSelect(hold_percent).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('hold', 'THB')"
                      />
                      <n-select
                        v-else-if="currency === 'KRW'"
                        v-model:value="hold_percentage_krw"
                        :options="
                          genPtSelect(hold_percent_krw).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('hold', 'KRW')"
                      />
                      <n-select
                        v-else
                        v-model:value="hold_percentage_usd"
                        :options="
                          genPtSelect(hold_percent_usd).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('hold', 'USD')"
                      />
                    </n-form-item>
                  </div>

                  <!-- Our Percentage -->
                  <div>
                    <n-form-item :label="`${$t('ourpercentage')} (%)`">
                      <n-select
                        v-if="currency === 'THB'"
                        v-model:value="our_percentage"
                        :options="
                          genPtSelect(hold_percent).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('our', 'THB')"
                      />
                      <n-select
                        v-else-if="currency === 'KRW'"
                        v-model:value="our_percentage_krw"
                        :options="
                          genPtSelect(hold_percent_krw).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('our', 'KRW')"
                      />
                      <n-select
                        v-else
                        v-model:value="our_percentage_usd"
                        :options="
                          genPtSelect(hold_percent_usd).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('our', 'USD')"
                      />
                    </n-form-item>
                  </div>
                </div>
              </n-card>
            </div>
          </n-card>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-4">
          <n-card class="sticky top-4">
            <template #header>
              <div class="flex items-center gap-2">
                <icon-park-outline-info class="text-lg text-blue-500" />
                <span>{{ $t("summary") }}</span>
              </div>
            </template>

            <div class="space-y-4">
              <!-- Account Summary -->
              <div
                v-if="current >= 1"
                class="p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <h4 class="font-semibold mb-3 flex items-center gap-2">
                  <icon-park-outline-user class="text-blue-500" />
                  {{ $t("accountsummary") }}
                </h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span>{{ $t("username") }}</span>
                    <span class="font-medium">{{
                      formModel.username || "-"
                    }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>{{ $t("positiontype") }}</span>
                    <n-tag :type="agent ? 'success' : 'warning'" size="small">
                      {{ agent ? $t("agent") : $t("reseller") }}
                    </n-tag>
                  </div>
                  <div v-if="agent" class="flex justify-between">
                    <span>{{ $t("bettype") }}</span>
                    <n-tag
                      :type="typeAgent === 'SEAMLESS' ? 'error' : 'info'"
                      size="small"
                    >
                      {{ typeAgent }}
                    </n-tag>
                  </div>
                </div>
              </div>

              <!-- Wallet Summary -->
              <div
                v-if="current >= 2 && selectedCurrencies.length"
                class="p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <h4 class="font-semibold mb-3 flex items-center gap-2">
                  <icon-park-outline-wallet class="text-green-500" />
                  {{ $t("walletsummary") }}
                </h4>
                <div class="space-y-2 text-sm">
                  <div
                    v-for="currency in selectedCurrencies"
                    :key="currency"
                    class="flex justify-between"
                  >
                    <span>{{ currency }}</span>
                    <span class="font-medium">
                      {{ walletAmounts[currency]?.toLocaleString() || 0 }}
                      {{
                        currency === "THB"
                          ? "฿"
                          : currency === "KRW"
                          ? "₩"
                          : "$"
                      }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Royalty Summary -->
              <div
                v-if="current >= 3"
                class="p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <h4 class="font-semibold mb-3 flex items-center gap-2">
                  <icon-park-outline-setting class="text-purple-500" />
                  {{ $t("royaltysummary") }}
                </h4>
                <div class="space-y-3 text-sm">
                  <div v-for="currency in selectedCurrencies" :key="currency">
                    <div class="font-medium mb-1">{{ currency }}</div>
                    <div class="pl-3 space-y-1">
                      <div class="flex justify-between">
                        <span>{{ $t("holdpercentage") }}</span>
                        <span
                          >{{
                            currency === "THB"
                              ? hold_percentage
                              : currency === "KRW"
                              ? hold_percentage_krw
                              : hold_percentage_usd
                          }}%</span
                        >
                      </div>
                      <div class="flex justify-between">
                        <span>{{ $t("ourpercentage") }}</span>
                        <span
                          >{{
                            currency === "THB"
                              ? our_percentage
                              : currency === "KRW"
                              ? our_percentage_krw
                              : our_percentage_usd
                          }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </n-card>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="mt-4 flex justify-center">
        <n-card class="w-full max-w-2xl">
          <div class="flex justify-between items-center">
            <n-button
              v-if="current > 1"
              size="large"
              @click="prev"
              class="px-8"
            >
              <template #icon>
                <icon-park-outline-left />
              </template>
              {{ $t("previous") }}
            </n-button>
            <div v-else></div>

            <div class="flex gap-3">
              <n-button size="large" @click="reset" class="px-6">
                <template #icon>
                  <icon-park-outline-refresh />
                </template>
                {{ $t("reset") }}
              </n-button>

              <n-button
                v-if="current < 3"
                type="primary"
                size="large"
                @click="next"
                class="px-8"
              >
                {{ $t("next") }}
                <template #icon>
                  <icon-park-outline-right />
                </template>
              </n-button>

              <n-button
                v-else
                type="success"
                size="large"
                @click="Submit"
                :loading="loading"
                class="px-8"
              >
                <template #icon>
                  <icon-park-outline-check />
                </template>
                {{ $t("save") }}
              </n-button>
            </div>
          </div>
        </n-card>
      </div>
    </div>
  </div>
</template>
