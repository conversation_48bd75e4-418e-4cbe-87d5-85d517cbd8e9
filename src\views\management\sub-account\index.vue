<script setup lang="tsx">
// eslint-disable-next-line ts/ban-ts-comment
// @ts-nocheck
import http from "@/service/axios";
import moment from "moment-timezone";
import type { DataTableColumns, FormInst } from "naive-ui";
import { NButton, NSpace, NTag, useDialog, useMessage } from "naive-ui";
const { t } = useI18n();
const formRef = ref<FormInst | null>();
const loading = ref(false);
const message = useMessage();
const dialog = useDialog();
const showModal = ref(false);
const showModalpassword = ref(false);
const itemsedit = ref(null);
const total: any = ref(1);
const perPage = ref(10);
const Page = ref(1);
const optionsStatusEdit = computed(() => [
  { text: t("active"), value: 1 },
  { text: t("inactive"), value: 2 },
]);
const management = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
  {
    value: 2,
    label: t("edit"),
  },
]);

const lottomanagement = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
  {
    value: 2,
    label: t("edit"),
  },
]);

const report = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
]);
const player = computed(() => [
  {
    value: 0,
    label: t("off"),
  },
  {
    value: 1,
    label: t("view"),
  },
  {
    value: 2,
    label: t("edit"),
  },
]);
const columns: DataTableColumns<Entity.User> = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return index + 1;
    },
  },
  {
    title: t("loginname"),
    align: "center",
    key: "username",
  },
  {
    title: t("name"),
    align: "center",
    key: "name",
  },
  {
    title: t("phone"),
    align: "center",
    key: "tel",
  },
  {
    title: t("status"),
    align: "center",
    key: "status",
    render: (row) => {
      if (row.status == 1) {
        return <NTag type="success">{t("active")}</NTag>;
      } else {
        return <NTag type="error">{t("inactive")}</NTag>;
      }
    },
  },
  {
    title: t("management"),
    align: "center",
    key: "management",
    render: (row) => {
      const permissions = JSON.parse(row.permissions);
      const statusText =
        permissions.management === 0
          ? t("off")
          : permissions.management === 1
          ? t("view")
          : permissions.management === 2
          ? t("edit")
          : t("unknow");

      const statusType =
        permissions.management === 0
          ? "default"
          : permissions.management === 1
          ? "info"
          : permissions.management === 2
          ? "warning"
          : "error";

      return <NTag type={statusType}>{statusText}</NTag>;
    },
  },
  {
    title: t("settingmanagement"),
    align: "center",
    key: "setting",
    render: (row) => {
      const permissions = JSON.parse(row.permissions);
      const statusText =
        permissions.setting === 0
          ? t("off")
          : permissions.setting === 1
          ? t("view")
          : permissions.setting === 2
          ? t("edit")
          : t("unknow");

      const statusType =
        permissions.setting === 0
          ? "default"
          : permissions.setting === 1
          ? "info"
          : permissions.setting === 2
          ? "warning"
          : "error";

      return <NTag type={statusType}>{statusText}</NTag>;
    },
  },
  {
    title: t("report"),
    align: "center",
    key: "report",
    render: (row) => {
      const permissions = JSON.parse(row.permissions);
      const statusText =
        permissions.report === 0
          ? t("off")
          : permissions.report === 1
          ? t("view")
          : t("unknow");

      const statusType =
        permissions.report === 0
          ? "default"
          : permissions.report === 1
          ? "info"
          : "Unknown";

      return <NTag type={statusType}>{statusText}</NTag>;
    },
  },
  {
    title: t("player"),
    align: "center",
    key: "player",
    render: (row) => {
      const permissions = JSON.parse(row.permissions);
      const statusText =
        permissions.player === 0
          ? t("off")
          : permissions.player === 1
          ? t("view")
          : permissions.player === 2
          ? t("edit")
          : t("unknow");

      const statusType =
        permissions.player === 0
          ? "default"
          : permissions.player === 1
          ? "info"
          : permissions.player === 2
          ? "warning"
          : "error";

      return <NTag type={statusType}>{statusText}</NTag>;
    },
  },
  {
    title: t("lastlogindate"),
    align: "center",
    key: "datelogin",
    render: (row) => {
      return row.last_login
        ? moment(row.last_login)
            .tz("Asia/Bangkok")
            .format("DD/MM/YYYY HH:mm:ss")
        : "-";
    },
  },
  {
    title: t("lastloginip"),
    align: "center",
    key: "dateloginIP",
    render: (row) => {
      return row.last_ip ? row.last_ip : "-";
    },
  },
  {
    title: t("edit"),
    align: "center",
    key: "actions",
    render: (row) => {
      return h(
        NButton,
        {
          type: "primary",
          size: "small",
          secondary: true,
          onClick: () => handleEditTable(row),
        },
        { default: () => <icon-park-outline-write /> }
      );
    },
  },
  {
    title: t("password"),
    align: "center",
    key: "actions",
    render: (row) => {
      return h(
        NButton,
        {
          type: "warning",
          size: "small",
          secondary: true,
          onClick: () => EditPassword(row),
        },
        { default: () => <icon-park-outline-key-one /> }
      );
    },
  },

  {
    title: t("delete"),
    align: "center",
    key: "actions",
    render: (row) => {
      return h(
        NButton,
        {
          type: "error",
          size: "small",
          secondary: true,
          onClick: () => DeleteData(row),
        },
        { default: () => <icon-park-outline-delete-one /> }
      );
    },
  },
]);
function handleEditTable(item: any) {
  if (item) {
    const permissions = JSON.parse(item.permissions);
    item.management = permissions.management;
    item.player = permissions.player;
    item.report = permissions.report;
    item.setting = permissions.setting;
    itemsedit.value = item;
  } else {
    itemsedit.value = null;
  }
  showModal.value = true;
}

function EditPassword(item: any) {
  if (item) {
    const permissions = JSON.parse(item.permissions);
    item.management = permissions.management;
    item.player = permissions.player;
    item.report = permissions.report;
    item.setting = permissions.setting;
    itemsedit.value = item;
  } else {
    itemsedit.value = null;
  }
  showModalpassword.value = true;
}

function DeleteData(item: any) {
  if (item) {
    dialog.warning({
      title: t("confirmdelete"),
      content: t("areyousure"),
      positiveText: t("confirm"),
      negativeText: t("cancel"),
      draggable: true,
      onPositiveClick: () => {
        const obj = {
          id: item.id,
        };
        http.post("v1/SubAccount/Delete", obj).then((response) => {
          if (response.data.status) {
            message.success(response.data.mes);
            GetSubAgent();
          } else {
            message.error(response.data.mes);
          }
        });
      },
      onNegativeClick: () => {
        message.error("ยกเลิก");
      },
    });
    itemsedit.value = item;
  } else {
    itemsedit.value = null;
  }
}

const rules = computed(() => ({
  username: [{ required: true, message: t("validusername"), trigger: "blur" }],
  name: [{ required: true, message: t("validname"), trigger: "blur" }],
  password: [{ required: true, message: t("validpassword"), trigger: "blur" }],
}));
const items = ref([]);
onMounted(() => {
  GetSubAgent();
});
function GetSubAgent() {
  loading.value = true;
  const params = {
    perPage: perPage.value,
    page: Page.value,
  };
  http.get("v1/Management/GetSubAccount", { params }).then((response) => {
    items.value = response.data.data;
    loading.value = false;
  });
}

function changePage(page: number, size: number) {
  window.$message.success(`${t("gotopage")}: ${page}/${size}`);
  perPage.value = size;
  Page.value = page;
  GetSubAgent();
}

function Submit(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      loading.value = true;
      const obj = {
        id: itemsedit.value.id,
        status: itemsedit.value.status,
        name: itemsedit.value.name,
        tel: itemsedit.value.tel,
        permissions: {
          management: itemsedit.value.management,
          player: itemsedit.value.player,
          report: itemsedit.value.report,
          setting: itemsedit.value.setting,
        },
        password: itemsedit.value.password ? itemsedit.value.password : null,
      };
      http.post("v1/SubAccount/Update", obj).then((response) => {
        if (response.data.status) {
          message.success(response.data.mes);
          GetSubAgent();
          showModal.value = false;
          showModalpassword.value = false;
          loading.value = false;
        } else {
          message.error(response.data.mes);
          loading.value = false;
        }
      });
    } else {
      console.error(errors);
      message.error("Invalid");
      loading.value = false;
    }
  });
}
</script>

<template>
  <div>
    <n-card>
      <NSpace vertical size="large">
        <div class="flex-start gap-4">
          <h4 class="font-medium text-[1rem]">
            {{ $t("subaccount") }}
          </h4>
        </div>
        <n-data-table
          :scroll-x="1500"
          :columns="columns"
          :data="items"
          :loading="loading"
        />
        <Pagination
          :count="total"
          @change="changePage"
          class="overflow-auto float-right"
        />
      </NSpace>
    </n-card>
    <n-modal
      v-model:show="showModal"
      class="w-350px sm:w-560px max-h-[80vh] overflow-y-auto"
      size="small"
      preset="card"
      :segmented="{
        content: true,
        footer: true,
      }"
    >
      <template #header>
        <icon-park-outline-edit-name /><span class="ml-1">{{
          $t("edit")
        }}</span>
      </template>
      <n-scrollbar>
        <n-form
          ref="formRef"
          :show-feedback="false"
          :rules="rules"
          :model="itemsedit"
        >
          <NSpace vertical>
            <div>
              <n-input
                v-model:value="itemsedit.username"
                type="text"
                disabled
              />
            </div>

            <div>
              <n-form-item :label="$t('name')" path="name">
                <n-input
                  v-model:value="itemsedit.name"
                  type="text"
                  path="name"
                />
              </n-form-item>
            </div>
            <div>
              <label>{{ $t("phone") }}</label>
              <n-input
                v-model:value="itemsedit.tel"
                type="text"
                :placeholder="$t('phone')"
              />
            </div>

            <div>
              <label>{{ $t("status") }}</label>
              <n-select
                v-model:value="itemsedit.status"
                filterable
                label-field="text"
                value-field="value"
                :options="optionsStatusEdit"
              />
            </div>

            <div
              class="mt-5 grid grid-cols-2 sm:grid-cols-4 justify-center gap-5 ml-8"
            >
              <div>
                <n-form-item :label="$t('management')">
                  <n-radio-group v-model:value="itemsedit.management">
                    <NSpace vertical>
                      <n-radio
                        v-for="item in management"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </NSpace>
                  </n-radio-group>
                </n-form-item>
              </div>
              <div>
                <n-form-item :label="$t('settingmanagement')">
                  <n-radio-group v-model:value="itemsedit.setting">
                    <NSpace vertical>
                      <n-radio
                        v-for="item in lottomanagement"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </NSpace>
                  </n-radio-group>
                </n-form-item>
              </div>
              <div>
                <n-form-item :label="$t('report')">
                  <n-radio-group v-model:value="itemsedit.report">
                    <NSpace vertical>
                      <n-radio
                        v-for="item in report"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </NSpace>
                  </n-radio-group>
                </n-form-item>
              </div>
              <div>
                <n-form-item :label="$t('player')">
                  <n-radio-group v-model:value="itemsedit.player">
                    <NSpace vertical>
                      <n-radio
                        v-for="item in player"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </NSpace>
                  </n-radio-group>
                </n-form-item>
              </div>
            </div>

            <n-flex justify="end" class="mt-5">
              <NButton
                type="default"
                class="mr-2"
                @click="GetSubAgent(), (showModal = false), GetSubAgent()"
              >
                <template #icon />
                {{ $t("cancel") }}
              </NButton>
              <NButton :loading="loading" type="primary" @click="Submit">
                <template #icon />
                {{ $t("save") }}
              </NButton>
            </n-flex>
          </NSpace>
        </n-form>
      </n-scrollbar>
    </n-modal>

    <!-- Edit Password -->
    <n-modal
      v-model:show="showModalpassword"
      size="small"
      class="sm:w-400px max-h-[80vh] overflow-y-auto"
      preset="card"
      :segmented="{
        content: true,
        footer: true,
      }"
    >
      <template #header>
        {{ $t("editpassword") }}
      </template>
      <n-scrollbar>
        <n-form
          ref="formRef"
          :show-feedback="true"
          :rules="rules"
          :model="itemsedit"
        >
          <NSpace vertical>
            <div>
              <n-form-item :label="$t('password')" path="password">
                <n-input
                  v-model:value="itemsedit.password"
                  :placeholder="$t('newpassword')"
                  type="text"
                  path="password"
                />
              </n-form-item>
            </div>

            <n-flex justify="end">
              <NButton
                type="default"
                class="mr-2"
                @click="showModalpassword = false"
              >
                <template #icon />
                {{ $t("cancel") }}
              </NButton>
              <NButton :loading="loading" type="primary" @click="Submit">
                <template #icon />
                {{ $t("save") }}
              </NButton>
            </n-flex>
          </NSpace>
        </n-form>
      </n-scrollbar>
    </n-modal>
  </div>
</template>
