import{x as F,hq as M,hr as f,hs as g,C as I,fu as i,ft as T,ht as V,A as N,d as q,q as t,fJ as D,gp as J,a1 as K,N as U,hu as G,hv as Q,hw as X,hx as Y,fz as Z,hy as ee,V as oe,z as E,fv as re,U as $,gv as ne,fA as c,W as se,r as te}from"./index-pY9FjpQW.js";function le(r){const{lineHeight:e,borderRadius:d,fontWeightStrong:b,baseColor:l,dividerColor:C,actionColor:P,textColor1:h,textColor2:s,closeColorHover:u,closeColorPressed:v,closeIconColor:p,closeIconColorHover:m,closeIconColorPressed:n,infoColor:o,successColor:x,warningColor:z,errorColor:y,fontSize:S}=r;return Object.assign(Object.assign({},M),{fontSize:S,lineHeight:e,titleFontWeight:b,borderRadius:d,border:`1px solid ${C}`,color:P,titleTextColor:h,iconColor:s,contentTextColor:s,closeBorderRadius:d,closeColorHover:u,closeColorPressed:v,closeIconColor:p,closeIconColorHover:m,closeIconColorPressed:n,borderInfo:`1px solid ${f(l,g(o,{alpha:.25}))}`,colorInfo:f(l,g(o,{alpha:.08})),titleTextColorInfo:h,iconColorInfo:o,contentTextColorInfo:s,closeColorHoverInfo:u,closeColorPressedInfo:v,closeIconColorInfo:p,closeIconColorHoverInfo:m,closeIconColorPressedInfo:n,borderSuccess:`1px solid ${f(l,g(x,{alpha:.25}))}`,colorSuccess:f(l,g(x,{alpha:.08})),titleTextColorSuccess:h,iconColorSuccess:x,contentTextColorSuccess:s,closeColorHoverSuccess:u,closeColorPressedSuccess:v,closeIconColorSuccess:p,closeIconColorHoverSuccess:m,closeIconColorPressedSuccess:n,borderWarning:`1px solid ${f(l,g(z,{alpha:.33}))}`,colorWarning:f(l,g(z,{alpha:.08})),titleTextColorWarning:h,iconColorWarning:z,contentTextColorWarning:s,closeColorHoverWarning:u,closeColorPressedWarning:v,closeIconColorWarning:p,closeIconColorHoverWarning:m,closeIconColorPressedWarning:n,borderError:`1px solid ${f(l,g(y,{alpha:.25}))}`,colorError:f(l,g(y,{alpha:.08})),titleTextColorError:h,iconColorError:y,contentTextColorError:s,closeColorHoverError:u,closeColorPressedError:v,closeIconColorError:p,closeIconColorHoverError:m,closeIconColorPressedError:n})}const ie={common:F,self:le},ae=I("alert",`
 line-height: var(--n-line-height);
 border-radius: var(--n-border-radius);
 position: relative;
 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-color);
 text-align: start;
 word-break: break-word;
`,[i("border",`
 border-radius: inherit;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 transition: border-color .3s var(--n-bezier);
 border: var(--n-border);
 pointer-events: none;
 `),T("closable",[I("alert-body",[i("title",`
 padding-right: 24px;
 `)])]),i("icon",{color:"var(--n-icon-color)"}),I("alert-body",{padding:"var(--n-padding)"},[i("title",{color:"var(--n-title-text-color)"}),i("content",{color:"var(--n-content-text-color)"})]),V({originalTransition:"transform .3s var(--n-bezier)",enterToProps:{transform:"scale(1)"},leaveToProps:{transform:"scale(0.9)"}}),i("icon",`
 position: absolute;
 left: 0;
 top: 0;
 align-items: center;
 justify-content: center;
 display: flex;
 width: var(--n-icon-size);
 height: var(--n-icon-size);
 font-size: var(--n-icon-size);
 margin: var(--n-icon-margin);
 `),i("close",`
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 position: absolute;
 right: 0;
 top: 0;
 margin: var(--n-close-margin);
 `),T("show-icon",[I("alert-body",{paddingLeft:"calc(var(--n-icon-margin-left) + var(--n-icon-size) + var(--n-icon-margin-right))"})]),T("right-adjust",[I("alert-body",{paddingRight:"calc(var(--n-close-size) + var(--n-padding) + 2px)"})]),I("alert-body",`
 border-radius: var(--n-border-radius);
 transition: border-color .3s var(--n-bezier);
 `,[i("title",`
 transition: color .3s var(--n-bezier);
 font-size: 16px;
 line-height: 19px;
 font-weight: var(--n-title-font-weight);
 `,[N("& +",[i("content",{marginTop:"9px"})])]),i("content",{transition:"color .3s var(--n-bezier)",fontSize:"var(--n-font-size)"})]),i("icon",{transition:"color .3s var(--n-bezier)"})]),ce=Object.assign(Object.assign({},E.props),{title:String,showIcon:{type:Boolean,default:!0},type:{type:String,default:"default"},bordered:{type:Boolean,default:!0},closable:Boolean,onClose:Function,onAfterLeave:Function,onAfterHide:Function}),he=q({name:"Alert",inheritAttrs:!1,props:ce,slots:Object,setup(r){const{mergedClsPrefixRef:e,mergedBorderedRef:d,inlineThemeDisabled:b,mergedRtlRef:l}=oe(r),C=E("Alert","-alert",ae,ie,r,e),P=re("Alert",l,e),h=$(()=>{const{common:{cubicBezierEaseInOut:n},self:o}=C.value,{fontSize:x,borderRadius:z,titleFontWeight:y,lineHeight:S,iconSize:H,iconMargin:R,iconMarginRtl:_,closeIconSize:W,closeBorderRadius:w,closeSize:A,closeMargin:B,closeMarginRtl:j,padding:k}=o,{type:a}=r,{left:L,right:O}=ne(R);return{"--n-bezier":n,"--n-color":o[c("color",a)],"--n-close-icon-size":W,"--n-close-border-radius":w,"--n-close-color-hover":o[c("closeColorHover",a)],"--n-close-color-pressed":o[c("closeColorPressed",a)],"--n-close-icon-color":o[c("closeIconColor",a)],"--n-close-icon-color-hover":o[c("closeIconColorHover",a)],"--n-close-icon-color-pressed":o[c("closeIconColorPressed",a)],"--n-icon-color":o[c("iconColor",a)],"--n-border":o[c("border",a)],"--n-title-text-color":o[c("titleTextColor",a)],"--n-content-text-color":o[c("contentTextColor",a)],"--n-line-height":S,"--n-border-radius":z,"--n-font-size":x,"--n-title-font-weight":y,"--n-icon-size":H,"--n-icon-margin":R,"--n-icon-margin-rtl":_,"--n-close-size":A,"--n-close-margin":B,"--n-close-margin-rtl":j,"--n-padding":k,"--n-icon-margin-left":L,"--n-icon-margin-right":O}}),s=b?se("alert",$(()=>r.type[0]),h,r):void 0,u=te(!0),v=()=>{const{onAfterLeave:n,onAfterHide:o}=r;n&&n(),o&&o()};return{rtlEnabled:P,mergedClsPrefix:e,mergedBordered:d,visible:u,handleCloseClick:()=>{var n;Promise.resolve((n=r.onClose)===null||n===void 0?void 0:n.call(r)).then(o=>{o!==!1&&(u.value=!1)})},handleAfterLeave:()=>{v()},mergedTheme:C,cssVars:b?void 0:h,themeClass:s?.themeClass,onRender:s?.onRender}},render(){var r;return(r=this.onRender)===null||r===void 0||r.call(this),t(ee,{onAfterLeave:this.handleAfterLeave},{default:()=>{const{mergedClsPrefix:e,$slots:d}=this,b={class:[`${e}-alert`,this.themeClass,this.closable&&`${e}-alert--closable`,this.showIcon&&`${e}-alert--show-icon`,!this.title&&this.closable&&`${e}-alert--right-adjust`,this.rtlEnabled&&`${e}-alert--rtl`],style:this.cssVars,role:"alert"};return this.visible?t("div",Object.assign({},D(this.$attrs,b)),this.closable&&t(J,{clsPrefix:e,class:`${e}-alert__close`,onClick:this.handleCloseClick}),this.bordered&&t("div",{class:`${e}-alert__border`}),this.showIcon&&t("div",{class:`${e}-alert__icon`,"aria-hidden":"true"},K(d.icon,()=>[t(U,{clsPrefix:e},{default:()=>{switch(this.type){case"success":return t(Y,null);case"info":return t(X,null);case"warning":return t(Q,null);case"error":return t(G,null);default:return null}}})])),t("div",{class:[`${e}-alert-body`,this.mergedBordered&&`${e}-alert-body--bordered`]},Z(d.header,l=>{const C=l||this.title;return C?t("div",{class:`${e}-alert-body__title`},C):null}),d.default&&t("div",{class:`${e}-alert-body__content`},d))):null}})}});export{he as _};
