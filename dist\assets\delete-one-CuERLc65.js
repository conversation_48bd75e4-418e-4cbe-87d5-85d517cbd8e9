import{a7 as o,m as t,o as r,a as n,fU as l}from"./index-pY9FjpQW.js";const a={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function s(i,e){return r(),t("svg",a,e[0]||(e[0]=[n("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[n("circle",{cx:"24",cy:"11",r:"7"}),n("path",{d:"M4 41c0-8.837 8.059-16 18-16m9 17l10-10l-4-4l-10 10v4z"})],-1)]))}const h=o({name:"icon-park-outline-edit-name",render:s}),d={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function c(i,e){return r(),t("svg",d,e[0]||(e[0]=[l('<g fill="none" stroke="currentColor" stroke-width="4"><path stroke-linejoin="round" d="m15 12l1.2-7h15.6l1.2 7"></path><path stroke-linecap="round" d="M6 12h36"></path><path stroke-linecap="round" stroke-linejoin="round" d="m37 12l-2 31H13l-2-31z" clip-rule="evenodd"></path><path stroke-linecap="round" d="M19 35h10"></path></g>',1)]))}const u=o({name:"icon-park-outline-delete-one",render:c});export{u as _,h as a};
