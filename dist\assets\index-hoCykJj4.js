import{_ as ie,a as de}from"./delete-one-CuERLc65.js";import{_ as pe,a as me}from"./Pagination.vue_vue_type_script_setup_true_lang-tK6i-N0U.js";import{_ as H}from"./write-lGVnfd1y.js";import{d as fe,ac as ve,r as m,ad as ge,gd as ce,U as c,b as a,ah as x,q as z,B as _,fQ as B,a3 as _e,m as U,o as f,w as o,ab as ye,f as n,af as g,a as i,t as v,ge as be,fT as we,fZ as ke,G as D,a8 as j,c as M,fD as $e,g as T,i as F,fR as xe,gf as Se}from"./index-pY9FjpQW.js";import{m as Ue}from"./index-DZ56Tu_n.js";import{_ as Te}from"./Form-_sVFK3VR.js";import{_ as Ce}from"./FormItem-B4P0FvHJ.js";import{_ as Ne,a as Pe}from"./RadioGroup-DnXU-fFU.js";import"./Checkbox-CwEpY3xE.js";import"./download-C2161hUv.js";const he={class:"flex-start gap-4"},De={class:"font-bold text-[1rem]"},je={class:"ml-1"},Me={class:"mt-5 grid grid-cols-2 sm:grid-cols-4 justify-center gap-5 ml-8"};function S(C){return typeof C=="function"||Object.prototype.toString.call(C)==="[object Object]"&&!Se(C)}const Ye=fe({__name:"index",setup(C){const{t:l}=ve(),O=m(),p=m(!1),y=ge(),L=ce(),b=m(!1),w=m(!1),u=m(null),Q=m(1),G=m(10),J=m(1),Z=c(()=>[{text:l("active"),value:1},{text:l("inactive"),value:2}]),K=c(()=>[{value:0,label:l("off")},{value:1,label:l("view")},{value:2,label:l("edit")}]),W=c(()=>[{value:0,label:l("off")},{value:1,label:l("view")},{value:2,label:l("edit")}]),X=c(()=>[{value:0,label:l("off")},{value:1,label:l("view")}]),ee=c(()=>[{value:0,label:l("off")},{value:1,label:l("view")},{value:2,label:l("edit")}]),te=c(()=>[{title:l("no."),align:"center",key:"index",render:(t,e)=>e+1},{title:l("loginname"),align:"center",key:"username"},{title:l("name"),align:"center",key:"name"},{title:l("phone"),align:"center",key:"tel"},{title:l("status"),align:"center",key:"status",render:t=>{if(t.status==1){let e;return a(x,{type:"success"},S(e=l("active"))?e:{default:()=>[e]})}else{let e;return a(x,{type:"error"},S(e=l("inactive"))?e:{default:()=>[e]})}}},{title:l("management"),align:"center",key:"management",render:t=>{const e=JSON.parse(t.permissions),r=e.management===0?l("off"):e.management===1?l("view"):e.management===2?l("edit"):l("unknow"),d=e.management===0?"default":e.management===1?"info":e.management===2?"warning":"error";return a(x,{type:d},S(r)?r:{default:()=>[r]})}},{title:l("settingmanagement"),align:"center",key:"setting",render:t=>{const e=JSON.parse(t.permissions),r=e.setting===0?l("off"):e.setting===1?l("view"):e.setting===2?l("edit"):l("unknow"),d=e.setting===0?"default":e.setting===1?"info":e.setting===2?"warning":"error";return a(x,{type:d},S(r)?r:{default:()=>[r]})}},{title:l("report"),align:"center",key:"report",render:t=>{const e=JSON.parse(t.permissions),r=e.report===0?l("off"):e.report===1?l("view"):l("unknow"),d=e.report===0?"default":e.report===1?"info":"Unknown";return a(x,{type:d},S(r)?r:{default:()=>[r]})}},{title:l("player"),align:"center",key:"player",render:t=>{const e=JSON.parse(t.permissions),r=e.player===0?l("off"):e.player===1?l("view"):e.player===2?l("edit"):l("unknow"),d=e.player===0?"default":e.player===1?"info":e.player===2?"warning":"error";return a(x,{type:d},S(r)?r:{default:()=>[r]})}},{title:l("lastlogindate"),align:"center",key:"datelogin",render:t=>t.last_login?Ue(t.last_login).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss"):"-"},{title:l("lastloginip"),align:"center",key:"dateloginIP",render:t=>t.last_ip?t.last_ip:"-"},{title:l("edit"),align:"center",key:"actions",render:t=>z(_,{type:"warning",size:"small",onClick:()=>le(t)},{default:()=>a(H,null,null)})},{title:l("password"),align:"center",key:"actions",render:t=>z(_,{type:"warning",size:"small",onClick:()=>ae(t)},{default:()=>a(H,null,null)})},{title:l("delete"),align:"center",key:"actions",render:t=>z(_,{type:"error",size:"small",onClick:()=>ne(t)},{default:()=>a(ie,null,null)})}]);function le(t){if(t){const e=JSON.parse(t.permissions);t.management=e.management,t.player=e.player,t.report=e.report,t.setting=e.setting,u.value=t}else u.value=null;b.value=!0}function ae(t){if(t){const e=JSON.parse(t.permissions);t.management=e.management,t.player=e.player,t.report=e.report,t.setting=e.setting,u.value=t}else u.value=null;w.value=!0}function ne(t){t?(L.warning({title:l("confirmdelete"),content:l("areyousure"),positiveText:l("confirm"),negativeText:l("cancel"),draggable:!0,onPositiveClick:()=>{const e={id:t.id};B.post("PG/SubAccount/Delete",e).then(r=>{r.data.status?(y.success(r.data.mes),k()):y.error(r.data.mes)})},onNegativeClick:()=>{y.error("ยกเลิก")}}),u.value=t):u.value=null}const A=c(()=>({username:[{required:!0,message:l("validusername"),trigger:"blur"}],name:[{required:!0,message:l("validname"),trigger:"blur"}],password:[{required:!0,message:l("validpassword"),trigger:"blur"}]})),R=m([]);_e(()=>{k()});function k(){p.value=!0;const t={perPage:G.value,page:J.value};B.get("PG/Management/GetSubAccount",{params:t}).then(e=>{R.value=e.data.data,p.value=!1})}function se(t,e){window.$message.success(`${l("gotopage")}: ${t}/${e}`),G.value=e,J.value=t,k()}function q(t){t.preventDefault(),O.value?.validate(e=>{if(e)console.error(e),y.error("Invalid"),p.value=!1;else{p.value=!0;const r={id:u.value.id,status:u.value.status,name:u.value.name,tel:u.value.tel,permissions:{management:u.value.management,player:u.value.player,report:u.value.report,setting:u.value.setting},password:u.value.password?u.value.password:null};B.post("PG/SubAccount/Update",r).then(d=>{d.data.status?(y.success(d.data.mes),k(),b.value=!1,w.value=!1,p.value=!1):(y.error(d.data.mes),p.value=!1)})}})}return(t,e)=>{const r=pe,d=me,oe=ye,ue=de,N=we,$=Ce,re=ke,P=Pe,h=Ne,E=$e,V=Te,Y=be,I=xe;return f(),U("div",null,[a(n(g),{vertical:"",size:"large"},{default:o(()=>[a(oe,null,{default:o(()=>[a(n(g),{vertical:"",size:"large"},{default:o(()=>[i("div",he,[i("h4",De,v(t.$t("subaccount")),1)]),a(r,{"scroll-x":1200,columns:n(te),data:n(R),loading:n(p)},null,8,["columns","data","loading"]),a(d,{count:n(Q),onChange:se},null,8,["count"])]),_:1})]),_:1})]),_:1}),a(I,{show:n(b),"onUpdate:show":e[9]||(e[9]=s=>F(b)?b.value=s:null),class:"w-350px sm:w-560px  max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:o(()=>[a(ue),i("span",je,v(t.$t("edit")),1)]),default:o(()=>[a(Y,null,{default:o(()=>[a(V,{ref_key:"formRef",ref:O,"show-feedback":!1,rules:n(A),model:n(u)},{default:o(()=>[a(n(g),{vertical:""},{default:o(()=>[i("div",null,[a(N,{value:n(u).username,"onUpdate:value":e[0]||(e[0]=s=>n(u).username=s),type:"text",disabled:""},null,8,["value"])]),i("div",null,[a($,{label:t.$t("name"),path:"name"},{default:o(()=>[a(N,{value:n(u).name,"onUpdate:value":e[1]||(e[1]=s=>n(u).name=s),type:"text",path:"name"},null,8,["value"])]),_:1},8,["label"])]),i("div",null,[i("label",null,v(t.$t("phone")),1),a(N,{value:n(u).tel,"onUpdate:value":e[2]||(e[2]=s=>n(u).tel=s),type:"text",placeholder:t.$t("phone")},null,8,["value","placeholder"])]),i("div",null,[i("label",null,v(t.$t("status")),1),a(re,{value:n(u).status,"onUpdate:value":e[3]||(e[3]=s=>n(u).status=s),filterable:"","label-field":"text","value-field":"value",options:n(Z)},null,8,["value","options"])]),i("div",Me,[i("div",null,[a($,{label:t.$t("management")},{default:o(()=>[a(h,{value:n(u).management,"onUpdate:value":e[4]||(e[4]=s=>n(u).management=s)},{default:o(()=>[a(n(g),{vertical:""},{default:o(()=>[(f(!0),U(D,null,j(n(K),s=>(f(),M(P,{key:s.value,value:s.value,label:s.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])]),i("div",null,[a($,{label:t.$t("settingmanagement")},{default:o(()=>[a(h,{value:n(u).setting,"onUpdate:value":e[5]||(e[5]=s=>n(u).setting=s)},{default:o(()=>[a(n(g),{vertical:""},{default:o(()=>[(f(!0),U(D,null,j(n(W),s=>(f(),M(P,{key:s.value,value:s.value,label:s.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])]),i("div",null,[a($,{label:t.$t("report")},{default:o(()=>[a(h,{value:n(u).report,"onUpdate:value":e[6]||(e[6]=s=>n(u).report=s)},{default:o(()=>[a(n(g),{vertical:""},{default:o(()=>[(f(!0),U(D,null,j(n(X),s=>(f(),M(P,{key:s.value,value:s.value,label:s.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])]),i("div",null,[a($,{label:t.$t("player")},{default:o(()=>[a(h,{value:n(u).player,"onUpdate:value":e[7]||(e[7]=s=>n(u).player=s)},{default:o(()=>[a(n(g),{vertical:""},{default:o(()=>[(f(!0),U(D,null,j(n(ee),s=>(f(),M(P,{key:s.value,value:s.value,label:s.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])])]),a(E,{justify:"end",class:"mt-5"},{default:o(()=>[a(n(_),{type:"error",class:"mr-2",onClick:e[8]||(e[8]=s=>(k(),b.value=!1,k()))},{icon:o(()=>e[13]||(e[13]=[])),default:o(()=>[T(" "+v(t.$t("cancel")),1)]),_:1}),a(n(_),{loading:n(p),type:"success",onClick:q},{icon:o(()=>e[14]||(e[14]=[])),default:o(()=>[T(" "+v(t.$t("save")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["rules","model"])]),_:1})]),_:1},8,["show"]),a(I,{show:n(w),"onUpdate:show":e[12]||(e[12]=s=>F(w)?w.value=s:null),size:"small",class:"sm:w-400px  max-h-[80vh] overflow-y-auto",preset:"card",segmented:{content:!0,footer:!0}},{header:o(()=>[T(v(t.$t("editpassword")),1)]),default:o(()=>[a(Y,null,{default:o(()=>[a(V,{ref_key:"formRef",ref:O,"show-feedback":!0,rules:n(A),model:n(u)},{default:o(()=>[a(n(g),{vertical:""},{default:o(()=>[i("div",null,[a($,{label:t.$t("password"),path:"password"},{default:o(()=>[a(N,{value:n(u).password,"onUpdate:value":e[10]||(e[10]=s=>n(u).password=s),placeholder:t.$t("newpassword"),type:"text",path:"password"},null,8,["value","placeholder"])]),_:1},8,["label"])]),a(E,{justify:"end",class:"mt-5"},{default:o(()=>[a(n(_),{type:"error",class:"mr-2",onClick:e[11]||(e[11]=s=>w.value=!1)},{icon:o(()=>e[15]||(e[15]=[])),default:o(()=>[T(" "+v(t.$t("cancel")),1)]),_:1}),a(n(_),{loading:n(p),type:"success",onClick:q},{icon:o(()=>e[16]||(e[16]=[])),default:o(()=>[T(" "+v(t.$t("save")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["rules","model"])]),_:1})]),_:1},8,["show"])])}}});export{Ye as default};
