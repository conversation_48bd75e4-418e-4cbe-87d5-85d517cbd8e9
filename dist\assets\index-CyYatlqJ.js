import{a7 as L,m as v,o as p,a as l,d as he,ac as ke,ad as be,fB as $e,r as _,a3 as xe,fQ as B,c as E,n as H,b as t,f as a,w as o,ab as Ce,t as r,B as Q,ae as Pe,g as u,G as X,a8 as ee,af as te,fZ as Ue,i as g,ge as Se,fT as je,fD as ze,fR as Be}from"./index-pY9FjpQW.js";import{a as Me,_ as Re}from"./delete-one-CuERLc65.js";import{_ as Ae}from"./write-lGVnfd1y.js";import{_ as Ie}from"./copy-BxkeU8Ds.js";import{m as Ke}from"./index-DZ56Tu_n.js";import{_ as We}from"./Form-_sVFK3VR.js";import{_ as Ge}from"./FormItem-B4P0FvHJ.js";import{_ as <PERSON>}from"./Table-DoHSPnrC.js";const Ne={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function De(M,m){return p(),v("svg",Ne,m[0]||(m[0]=[l("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[l("path",{d:"M22.868 24.298a9.87 9.87 0 0 1 2.63 9.588a9.93 9.93 0 0 1-7.065 7.028a9.99 9.99 0 0 1-9.64-2.615a9.863 9.863 0 0 1 .122-13.878c3.839-3.82 10.046-3.873 13.951-.121z"}),l("path",{"stroke-linecap":"round",d:"M23 24L40 7"}),l("path",{d:"m30.305 16.9l5.429 5.4l6.333-6.3l-5.428-5.4z"})],-1)]))}const Te=L({name:"icon-park-outline-key",render:De}),qe={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ee(M,m){return p(),v("svg",qe,m[0]||(m[0]=[l("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[l("path",{d:"M4 24c0 11.046 8.954 20 20 20v0c11.046 0 20-8.954 20-20S35.046 4 24 4"}),l("path",{d:"M36 24c0-6.627-5.373-12-12-12s-12 5.373-12 12s5.373 12 12 12v0"})],-1)]))}const He=L({name:"icon-park-outline-loading",render:Ee}),Ve={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ye(M,m){return p(),v("svg",Ve,m[0]||(m[0]=[l("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[l("path",{d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"}),l("path",{"stroke-linecap":"round",d:"M29.657 18.343L18.343 29.657m0-11.314l11.314 11.314"})],-1)]))}const Ze=L({name:"icon-park-outline-close-one",render:Ye}),Fe={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Oe(M,m){return p(),v("svg",Fe,m[0]||(m[0]=[l("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[l("path",{d:"M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"}),l("path",{"stroke-linecap":"round",d:"m16 24l6 6l12-12"})],-1)]))}const Je=L({name:"icon-park-outline-check-one",render:Oe}),Qe={class:"flex justify-between items-center"},Xe={class:"text-center"},et={class:"ml-1"},tt={class:"grid sm:grid-cols-2 lg:grid-cols-3 gap-3 items-center"},st={class:"text-start"},lt={class:"flex flex-wrap items-center gap-1"},ot={class:"flex items-center gap-1"},nt={key:0,class:"text-green"},at={key:1,class:"text-red"},rt={class:"flex flex-wrap justify-between items-center mt-5"},it={class:"flex flex-wrap items-center gap-1"},ut={class:"text-center"},dt=["src"],pt={class:"ml-2"},ct={class:"grid sm:grid-cols-2 lg:grid-cols-3 gap-3 items-center mt-5"},_t={class:"flex flex-wrap items-center gap-1"},mt={class:"flex flex-wrap items-center gap-1"},ft={key:0,class:"mt-5"},vt={class:"my-2 flex items-center gap-3 lg:w-1/2"},wt={class:"truncate"},yt={class:"my-2 flex items-center gap-3 lg:w-1/2"},gt={class:"ml-1"},ht={class:"my-2 flex items-center gap-3 lg:w-1/2"},kt={class:"truncate"},bt={class:"ml-1"},$t={class:"my-2 flex items-center gap-3 lg:w-1/2"},xt={class:"truncate"},Ct={class:"ml-1"},Pt={class:"my-2 flex items-center gap-3 lg:w-1/2"},Ut={class:"ml-1"},St={class:"ml-1"},jt={class:"ml-1"},zt={class:"ml-1"},Bt={class:"flex gap-3"},Mt={key:0},Rt={class:"text-center"},At={class:"text-center"},It={class:"text-center"},Kt={class:"text-center mb-5"},Wt={class:"ml-1"},Yt=he({__name:"index",setup(M){const{t:m}=ke(),f=be(),{userData:h}=$e(),N=()=>!((h?.position_type==4&&h?.upline_position_type==3||h?.position_type==4&&h?.upline_position_type==2||h?.position_type==4&&h?.upline_position_type==1)&&JSON.parse(h?.permissions).management!==2);_([]);const V=async s=>{try{await navigator.clipboard.writeText(s),f.success(s)}catch(e){console.error(e),f.error("Failed to copy")}},R=_([]),i=_(null);xe(()=>{$()});const b=_([]),$=async()=>{const{data:s}=await B.get("PG/Profile/index");if(i.value=s,i.value.whitelist_ip.length&&(R.value=s.whitelist_ip[0]),b.value=[],s.position_type==3){const e={currency:"THB",images:"/images/country/th.webp",hold_percent:s.hold_percent};b.value.push(e)}else{if(s.reseller_thb){const e={currency:"THB",images:"/images/country/th.webp",hold_percent:s.hold_percent};b.value.push(e)}if(s.reseller_krw){const e={currency:"KRW",images:"/images/country/kr.webp",hold_percent:s.hold_percent_krw};b.value.push(e)}if(s.reseller_usd){const e={currency:"USD",images:"/images/country/us.webp",hold_percent:s.hold_percent_usd};b.value.push(e)}}},x=_(!1),C=_(!1),P=_(!1),z=_(!1),U=_(null),S=_(null),w=_(null),y=_([]),d=_({oldpassword:null,newpassword:null,confirmpassword:null}),D=_({oldpassword:[{required:!0,message:"Please input old password",trigger:"blur"}],newpassword:[{required:!0,message:"Please input new password",trigger:"blur"}],confirmpassword:[{required:!0,message:"Please input password",trigger:"blur"}],whitlistip:[{required:!0,message:"Please input Whitelist IP",trigger:"blur"}],callbackUrl:[{required:!0,message:"Please input Callback URL",trigger:"blur"}]}),Y=s=>Ke(s).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm"),se=()=>{d.value={oldpassword:null,newpassword:null,confirmpassword:null},x.value=!0},le=()=>{if(!d.value.oldpassword||!d.value.newpassword||!d.value.confirmpassword)return f.error("กรุณากรอกรหัสผ่านให้ครบถ้วน");if(d.value.newpassword!=d.value.confirmpassword)return f.error("รหัสผ่านไม่ตรงกัน");const s={old_pass:d.value.oldpassword,new_pass:d.value.confirmpassword,password:d.value.newpassword};B.post("PG/changepass",s).then(e=>{e.data.success?(f.success(e.data.mes),$(),x.value=!1):f.error(e.data.mes)})},oe=()=>{U.value=null,z.value=!1,B.post("PG/Store/SecretKey").then(s=>{s.data.success?(f.success("สร้าง Secret Key สำเร็จ"),U.value=s.data.mes,z.value=!0,$()):f.error("เกิดข้อผิดพลาดไม่สามารถสร้าง Secret Key ได้")})},ne=()=>{S.value=i.value.callback_url,P.value=!0},ae=()=>{const s={CallbackUrl:S.value};B.post("PG/Update/callbackurl",s).then(e=>{e.data.success?(f.success("บันทึกข้อมูลสำเร็จ"),$(),P.value=!1):f.error("เกิดข้อผิดพลาด!")})},Z=s=>{s?(y.value=i.value.whitelist_ip,C.value=!0):(C.value=!1,y.value=[],w.value=null,$())},re=()=>{w.value&&!y.value.includes(w.value)&&y.value.unshift(w.value)},ie=s=>{i.value.whitelist_ip.splice(s,1)},ue=()=>{const s={whitelist_ip:y.value};B.post("PG/Update/change_whitelist_ip",s).then(e=>{e.data.success?(f.success("บันทึกข้อมูลสำเร็จ"),$(),C.value=!1,y.value=[],w.value=null):f.error("เกิดข้อผิดพลาด!")})};return(s,e)=>{const F=Me,c=Q,O=Pe,de=Je,pe=Ze,J=Ie,ce=He,A=Ae,_e=Ue,me=te,fe=Ce,T=te,j=je,I=Ge,k=Q,K=ze,q=We,W=Se,G=Be,ve=Re,we=Le,ye=Te;return p(),v("div",null,[a(i)?(p(),E(T,{key:0,vertical:"",size:"large"},{default:o(()=>[t(fe,null,{default:o(()=>[l("div",Qe,[l("h4",Xe,r(s.$t("basicinfomation")),1),l("div",null,[t(c,{onClick:e[0]||(e[0]=n=>se()),type:"warning",size:"small"},{default:o(()=>[t(F),l("span",et,r(s.$t("common.edit")),1)]),_:1})])]),t(O),l("div",tt,[l("p",st,r(s.$t("username"))+" : "+r(a(i).username),1),l("div",lt,[l("p",null,r(s.$t("accounttype"))+" :",1),t(c,{class:"cursor-default",type:a(i).position_type==1?"error":a(i).position_type==2?"warning":"success",ghost:"",round:"",size:"small"},{default:o(()=>[u(r(a(i).position_type==1?"CP":a(i).position_type==2?"RL":"AG"),1)]),_:1},8,["type"]),u(" "+r(s.$t(a(i).position_type==1?"company":a(i).position_type==2?"reseller":"agent")),1)]),l("div",ot,[l("p",null,r(s.$t("status"))+" :",1),a(i).status==1?(p(),v("div",nt,[t(de)])):(p(),v("div",at,[t(pe)])),u(" "+r(s.$t(a(i).status==1?"active":"inactive")),1)])]),l("div",rt,[l("div",it,[l("p",ut,r(s.$t("currency"))+" :",1),l("div",null,[(p(!0),v(X,null,ee(a(b),n=>(p(),E(c,{key:n,class:"ma-1 cursor-default"},{default:o(()=>[l("img",{src:n.images,width:"25"},null,8,dt),l("p",pt,r(n.currency)+" "+r(n.hold_percent)+"%",1)]),_:2},1024))),128))])])]),l("div",ct,[l("div",_t,[l("p",null,r(s.$t("createtime"))+" :",1),u(" "+r(Y(a(i).created_at)),1)]),l("div",mt,[l("p",null,r(s.$t("lastlogin"))+" :",1),u(" "+r(Y(a(i).last_login)),1)])]),a(i).position_type==3?(p(),v("div",ft,[l("div",vt,[t(c,{class:"w-40 cursor-default"},{default:o(()=>[u(r(s.$t("bettype")),1)]),_:1}),l("span",wt,r(a(i).bet_type==1?"Seamless":"Tranfer"),1)]),l("div",yt,[t(c,{class:"w-40 cursor-default"},{default:o(()=>e[24]||(e[24]=[u(" Api Endpoint ")])),_:1}),e[25]||(e[25]=l("span",{class:"truncate"},"https://api.pgf-theks69.com",-1)),t(c,{type:"primary",size:"tiny",onClick:e[1]||(e[1]=n=>V("https://api.pgf-theks69.com")),class:"w-auto ml-auto"},{default:o(()=>[t(J),l("span",gt,r(s.$t("copy")),1)]),_:1})]),l("div",ht,[t(c,{class:"w-40 cursor-default"},{default:o(()=>e[26]||(e[26]=[u(" Secret Key ")])),_:1}),l("span",kt,r(a(i).secret_key),1),t(c,{onClick:e[2]||(e[2]=n=>oe()),type:"primary",size:"tiny",class:"w-auto ml-auto",disabled:!N()},{default:o(()=>[t(ce),l("span",bt,r(s.$t("generateapikey")),1)]),_:1},8,["disabled"])]),l("div",$t,[t(c,{class:"w-40 cursor-default"},{default:o(()=>e[27]||(e[27]=[u(" Callback URL ")])),_:1}),l("span",xt,r(a(i).callback_url),1),t(c,{type:"primary",size:"tiny",onClick:e[3]||(e[3]=n=>ne()),class:"w-auto ml-auto",disabled:!N()},{default:o(()=>[t(A),l("span",Ct,r(s.$t("edit")),1)]),_:1},8,["disabled"])]),l("div",Pt,[t(c,{class:"w-40 cursor-default"},{default:o(()=>e[28]||(e[28]=[u(" Whitelist IP ")])),_:1}),t(me,{vertical:""},{default:o(()=>[(p(),E(_e,{placeholder:"Whitelist IP",class:"sm:w-60",value:a(R),"onUpdate:value":e[4]||(e[4]=n=>g(R)?R.value=n:null),options:a(i).whitelist_ip.map(n=>({label:n,value:n})),key:n=>n},null,8,["value","options"]))]),_:1}),t(c,{type:"primary",size:"tiny",onClick:e[5]||(e[5]=n=>Z(!0)),class:"w-auto ml-auto",disabled:!N()},{default:o(()=>[t(A),l("span",Ut,r(s.$t("edit")),1)]),_:1},8,["disabled"])])])):H("",!0)]),_:1})]),_:1})):H("",!0),t(G,{show:a(x),"onUpdate:show":e[11]||(e[11]=n=>g(x)?x.value=n:null),class:"w-350px sm:w-560px  max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:o(()=>[t(F),l("span",St,r(s.$t("editmyprofile")),1)]),default:o(()=>[t(W,null,{default:o(()=>[t(q,{ref:"formRef","show-feedback":!1,"label-placement":"left",rules:a(D),model:a(d)},{default:o(()=>[t(T,{vertical:""},{default:o(()=>[l("div",null,[t(I,{label:s.$t("oldpassword"),path:"oldpassword"},{default:o(()=>[t(j,{value:a(d).oldpassword,"onUpdate:value":e[6]||(e[6]=n=>a(d).oldpassword=n),type:"text",placeholder:s.$t("oldpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),l("div",null,[t(I,{label:s.$t("newpassword"),path:"newpassword"},{default:o(()=>[t(j,{value:a(d).newpassword,"onUpdate:value":e[7]||(e[7]=n=>a(d).newpassword=n),type:"text",placeholder:s.$t("newpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),l("div",null,[t(I,{label:s.$t("confirmpassword"),path:"confirmpassword"},{default:o(()=>[t(j,{value:a(d).confirmpassword,"onUpdate:value":e[8]||(e[8]=n=>a(d).confirmpassword=n),type:"text",placeholder:s.$t("confirmpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),t(K,{justify:"end",class:"mt-5"},{default:o(()=>[t(k,{type:"error",class:"mr-2",onClick:e[9]||(e[9]=n=>x.value=!1)},{icon:o(()=>e[29]||(e[29]=[])),default:o(()=>[u(" "+r(s.$t("cancel")),1)]),_:1}),t(k,{type:"success",onClick:e[10]||(e[10]=n=>le())},{icon:o(()=>e[30]||(e[30]=[])),default:o(()=>[u(" "+r(s.$t("save")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"])]),_:1})]),_:1},8,["show"]),t(G,{show:a(P),"onUpdate:show":e[15]||(e[15]=n=>g(P)?P.value=n:null),class:"w-350px sm:w-560px  max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:o(()=>[t(A),l("span",jt,r(s.$t("editcallbackurl")),1)]),default:o(()=>[t(W,null,{default:o(()=>[t(q,{ref:"formRef","show-feedback":!1,"label-placement":"left",rules:a(D),model:a(S)},{default:o(()=>[t(T,{vertical:""},{default:o(()=>[l("div",null,[t(I,{label:"Callback URL",path:"callbackUrl"},{default:o(()=>[t(j,{value:a(S),"onUpdate:value":e[12]||(e[12]=n=>g(S)?S.value=n:null),type:"text",placeholder:"Callback URL"},null,8,["value"])]),_:1})]),t(K,{justify:"end",class:"mt-5"},{default:o(()=>[t(k,{type:"error",class:"mr-2",onClick:e[13]||(e[13]=n=>P.value=!1)},{icon:o(()=>e[31]||(e[31]=[])),default:o(()=>[u(" "+r(s.$t("cancel")),1)]),_:1}),t(k,{type:"success",onClick:e[14]||(e[14]=n=>ae())},{icon:o(()=>e[32]||(e[32]=[])),default:o(()=>[u(" "+r(s.$t("save")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"])]),_:1})]),_:1},8,["show"]),t(G,{show:a(C),"onUpdate:show":e[20]||(e[20]=n=>g(C)?C.value=n:null),class:"w-350px sm:w-560px  max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:o(()=>[t(A),l("span",zt,r(s.$t("editwhitelistip")),1)]),default:o(()=>[t(W,null,{default:o(()=>[t(q,{ref:"formRef","show-feedback":!1,rules:a(D),model:a(w)},{default:o(()=>[l("div",null,[l("div",Bt,[t(j,{value:a(w),"onUpdate:value":e[16]||(e[16]=n=>g(w)?w.value=n:null),type:"text",placeholder:"xxx.xxx.xxx.xx"},null,8,["value"]),t(c,{type:"primary",onClick:e[17]||(e[17]=n=>re())},{default:o(()=>[u(r(s.$t("add")),1)]),_:1})]),t(we,{class:"mt-5",size:"small"},{default:o(()=>[a(y).length?(p(),v("tbody",Mt,[(p(!0),v(X,null,ee(a(y),(n,ge)=>(p(),v("tr",{key:n},[l("td",Rt,r(n),1),l("td",At,[t(c,{type:"error",onClick:Gt=>ie(ge)},{default:o(()=>[t(ve)]),_:2},1032,["onClick"])])]))),128))])):H("",!0)]),_:1}),t(K,{justify:"end",class:"mt-5"},{default:o(()=>[t(k,{type:"error",class:"mr-2",onClick:e[18]||(e[18]=n=>Z(!1))},{icon:o(()=>e[33]||(e[33]=[])),default:o(()=>[u(" "+r(s.$t("cancel")),1)]),_:1}),t(k,{type:"success",onClick:e[19]||(e[19]=n=>ue())},{icon:o(()=>e[34]||(e[34]=[])),default:o(()=>[u(" "+r(s.$t("save")),1)]),_:1})]),_:1})])]),_:1},8,["rules","model"])]),_:1})]),_:1},8,["show"]),t(G,{show:a(z),"onUpdate:show":e[23]||(e[23]=n=>g(z)?z.value=n:null),class:"w-350px sm:w-500px  max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0}},{header:o(()=>[t(ye),e[35]||(e[35]=l("span",{class:"ml-1"},"Secret Key",-1))]),default:o(()=>[t(W,null,{default:o(()=>[l("div",null,[l("p",It,r(s.$t("secretkey1")),1),l("p",Kt,r(s.$t("secretkey2")),1),t(j,{value:a(U),"onUpdate:value":e[21]||(e[21]=n=>g(U)?U.value=n:null),placeholder:"",type:"text",disabled:""},null,8,["value"]),t(O),t(K,{justify:"center",class:"mt-5"},{default:o(()=>[t(k,{type:"warning",onClick:e[22]||(e[22]=n=>V(a(U)))},{icon:o(()=>e[36]||(e[36]=[])),default:o(()=>[t(J),l("span",Wt,r(s.$t("copy")),1)]),_:1})]),_:1})])]),_:1})]),_:1},8,["show"])])}}});export{Yt as default};
