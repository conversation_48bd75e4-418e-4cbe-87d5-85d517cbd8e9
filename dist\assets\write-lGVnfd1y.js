import{a7 as n,m as t,o as r,a as e}from"./index-pY9FjpQW.js";const i={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function a(l,o){return r(),t("svg",i,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M5.325 43.5h8.485l31.113-31.113l-8.486-8.485L5.325 35.015z"}),e("path",{"stroke-linecap":"round",d:"m27.952 12.387l8.485 8.485"})],-1)]))}const c=n({name:"icon-park-outline-write",render:a});export{c as _};
