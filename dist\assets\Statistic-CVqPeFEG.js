import{fE as Z,U as S,r as C,R as K,J as q,fF as ee,fG as O,y as te,d as I,q as h,S as ne,fH as se,fI as E,fJ as D,fK as ie,V as X,fL as z,a3 as Q,a6 as ae,K as j,fn as re,fM as oe,fN as le,fO as k,a0 as ue,M as fe,a4 as de,C as F,fu as M,fz as B,z as U,fP as ce,fv as pe,W as me}from"./index-pY9FjpQW.js";import{t as W}from"./toNumber-C1SyHx2r.js";function ve(e){if(typeof e=="number")return{"":e.toString()};const t={};return e.split(/ +/).forEach(s=>{if(s==="")return;const[n,i]=s.split(":");i===void 0?t[""]=n:t[n]=i}),t}function $(e,t){var s;if(e==null)return;const n=ve(e);if(t===void 0)return n[""];if(typeof t=="string")return(s=n[t])!==null&&s!==void 0?s:n[""];if(Array.isArray(t)){for(let i=t.length-1;i>=0;--i){const r=t[i];if(r in n)return n[r]}return n[""]}else{let i,r=-1;return Object.keys(n).forEach(a=>{const o=Number(a);!Number.isNaN(o)&&t>=o&&o>=r&&(r=o,i=n[a])}),i}}const he={xs:0,s:640,m:1024,l:1280,xl:1536,"2xl":1920};function be(e){return`(min-width: ${e}px)`}const G={};function ge(e=he){if(!Z)return S(()=>[]);if(typeof window.matchMedia!="function")return S(()=>[]);const t=C({}),s=Object.keys(e),n=(i,r)=>{i.matches?t.value[r]=!0:t.value[r]=!1};return s.forEach(i=>{const r=e[i];let a,o;G[r]===void 0?(a=window.matchMedia(be(r)),a.addEventListener?a.addEventListener("change",d=>{o.forEach(f=>{f(d,i)})}):a.addListener&&a.addListener(d=>{o.forEach(f=>{f(d,i)})}),o=new Set,G[r]={mql:a,cbs:o}):(a=G[r].mql,o=G[r].cbs),o.add(n),a.matches&&o.forEach(d=>{d(a,i)})}),K(()=>{s.forEach(i=>{const{cbs:r}=G[e[i]];r.has(n)&&r.delete(n)})}),S(()=>{const{value:i}=t;return s.filter(r=>i[r])})}function Se(e){var t;const s=(t=e.dirs)===null||t===void 0?void 0:t.find(({dir:n})=>n===q);return!!(s&&s.value===!1)}var xe=1/0,Re=17976931348623157e292;function _e(e){if(!e)return e===0?e:0;if(e=W(e),e===xe||e===-1/0){var t=e<0?-1:1;return t*Re}return e===e?e:0}function ye(e){var t=_e(e),s=t%1;return t===t?s?t-s:t:0}var we=ee.isFinite,Ne=Math.min;function $e(e){var t=Math[e];return function(s,n){if(s=W(s),n=n==null?0:Ne(ye(n),292),n&&we(s)){var i=(O(s)+"e").split("e"),r=t(i[0]+"e"+(+i[1]+n));return i=(O(r)+"e").split("e"),+(i[0]+"e"+(+i[1]-n))}return t(s)}}var Ce=$e("round");const L=1,H=te("n-grid"),J=1,Ee={span:{type:[Number,String],default:J},offset:{type:[Number,String],default:0},suffix:Boolean,privateOffset:Number,privateSpan:Number,privateColStart:Number,privateShow:{type:Boolean,default:!0}},Pe=I({__GRID_ITEM__:!0,name:"GridItem",alias:["Gi"],props:Ee,setup(){const{isSsrRef:e,xGapRef:t,itemStyleRef:s,overflowRef:n,layoutShiftDisabledRef:i}=ne(H),r=se();return{overflow:n,itemStyle:s,layoutShiftDisabled:i,mergedXGap:S(()=>E(t.value||0)),deriveStyle:()=>{e.value;const{privateSpan:a=J,privateShow:o=!0,privateColStart:d=void 0,privateOffset:f=0}=r.vnode.props,{value:b}=t,x=E(b||0);return{display:o?"":"none",gridColumn:`${d??`span ${a}`} / span ${a}`,marginLeft:f?`calc((100% - (${a} - 1) * ${x}) / ${a} * ${f} + ${x} * ${f})`:""}}}},render(){var e,t;if(this.layoutShiftDisabled){const{span:s,offset:n,mergedXGap:i}=this;return h("div",{style:{gridColumn:`span ${s} / span ${s}`,marginLeft:n?`calc((100% - (${s} - 1) * ${i}) / ${s} * ${n} + ${i} * ${n})`:""}},this.$slots)}return h("div",{style:[this.itemStyle,this.deriveStyle()]},(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e,{overflow:this.overflow}))}}),Ge={xs:0,s:640,m:1024,l:1280,xl:1536,xxl:1920},Y=24,V="__ssr__",ze={layoutShiftDisabled:Boolean,responsive:{type:[String,Boolean],default:"self"},cols:{type:[Number,String],default:Y},itemResponsive:Boolean,collapsed:Boolean,collapsedRows:{type:Number,default:1},itemStyle:[Object,String],xGap:{type:[Number,String],default:0},yGap:{type:[Number,String],default:0}},Oe=I({name:"Grid",inheritAttrs:!1,props:ze,setup(e){const{mergedClsPrefixRef:t,mergedBreakpointsRef:s}=X(e),n=/^\d+$/,i=C(void 0),r=ge(s?.value||Ge),a=z(()=>!!(e.itemResponsive||!n.test(e.cols.toString())||!n.test(e.xGap.toString())||!n.test(e.yGap.toString()))),o=S(()=>{if(a.value)return e.responsive==="self"?i.value:r.value}),d=z(()=>{var c;return(c=Number($(e.cols.toString(),o.value)))!==null&&c!==void 0?c:Y}),f=z(()=>$(e.xGap.toString(),o.value)),b=z(()=>$(e.yGap.toString(),o.value)),x=c=>{i.value=c.contentRect.width},u=c=>{ue(x,c)},m=C(!1),v=S(()=>{if(e.responsive==="self")return u}),p=C(!1),g=C();return Q(()=>{const{value:c}=g;c&&c.hasAttribute(V)&&(c.removeAttribute(V),p.value=!0)}),ae(H,{layoutShiftDisabledRef:j(e,"layoutShiftDisabled"),isSsrRef:p,itemStyleRef:j(e,"itemStyle"),xGapRef:f,overflowRef:m}),{isSsr:!re,contentEl:g,mergedClsPrefix:t,style:S(()=>e.layoutShiftDisabled?{width:"100%",display:"grid",gridTemplateColumns:`repeat(${e.cols}, minmax(0, 1fr))`,columnGap:E(e.xGap),rowGap:E(e.yGap)}:{width:"100%",display:"grid",gridTemplateColumns:`repeat(${d.value}, minmax(0, 1fr))`,columnGap:E(f.value),rowGap:E(b.value)}),isResponsive:a,responsiveQuery:o,responsiveCols:d,handleResize:v,overflow:m}},render(){if(this.layoutShiftDisabled)return h("div",D({ref:"contentEl",class:`${this.mergedClsPrefix}-grid`,style:this.style},this.$attrs),this.$slots);const e=()=>{var t,s,n,i,r,a,o;this.overflow=!1;const d=oe(le(this)),f=[],{collapsed:b,collapsedRows:x,responsiveCols:u,responsiveQuery:m}=this;d.forEach(l=>{var _,y,R,w,T;if(((_=l?.type)===null||_===void 0?void 0:_.__GRID_ITEM__)!==!0)return;if(Se(l)){const N=k(l);N.props?N.props.privateShow=!1:N.props={privateShow:!1},f.push({child:N,rawChildSpan:0});return}l.dirs=((y=l.dirs)===null||y===void 0?void 0:y.filter(({dir:N})=>N!==q))||null,((R=l.dirs)===null||R===void 0?void 0:R.length)===0&&(l.dirs=null);const A=k(l),P=Number((T=$((w=A.props)===null||w===void 0?void 0:w.span,m))!==null&&T!==void 0?T:L);P!==0&&f.push({child:A,rawChildSpan:P})});let v=0;const p=(t=f[f.length-1])===null||t===void 0?void 0:t.child;if(p?.props){const l=(s=p.props)===null||s===void 0?void 0:s.suffix;l!==void 0&&l!==!1&&(v=Number((i=$((n=p.props)===null||n===void 0?void 0:n.span,m))!==null&&i!==void 0?i:L),p.props.privateSpan=v,p.props.privateColStart=u+1-v,p.props.privateShow=(r=p.props.privateShow)!==null&&r!==void 0?r:!0)}let g=0,c=!1;for(const{child:l,rawChildSpan:_}of f){if(c&&(this.overflow=!0),!c){const y=Number((o=$((a=l.props)===null||a===void 0?void 0:a.offset,m))!==null&&o!==void 0?o:0),R=Math.min(_+y,u);if(l.props?(l.props.privateSpan=R,l.props.privateOffset=y):l.props={privateSpan:R,privateOffset:y},b){const w=g%u;R+w>u&&(g+=u-w),R+g+v>x*u?c=!0:g+=R}}c&&(l.props?l.props.privateShow!==!0&&(l.props.privateShow=!1):l.props={privateShow:!1})}return h("div",D({ref:"contentEl",class:`${this.mergedClsPrefix}-grid`,style:this.style,[V]:this.isSsr||void 0},this.$attrs),f.map(({child:l})=>l))};return this.isResponsive&&this.responsive==="self"?h(ie,{onResize:this.handleResize},{default:e}):e()}}),Fe=e=>1-Math.pow(1-e,5);function Me(e){const{from:t,to:s,duration:n,onUpdate:i,onFinish:r}=e,a=performance.now(),o=()=>{const d=performance.now(),f=Math.min(d-a,n),b=t+(s-t)*Fe(f/n);if(f===n){r();return}i(b),requestAnimationFrame(o)};o()}const Be={to:{type:Number,default:0},precision:{type:Number,default:0},showSeparator:Boolean,locale:String,from:{type:Number,default:0},active:{type:Boolean,default:!0},duration:{type:Number,default:2e3},onFinish:Function},De=I({name:"NumberAnimation",props:Be,setup(e){const{localeRef:t}=fe("name"),{duration:s}=e,n=C(e.from),i=S(()=>{const{locale:u}=e;return u!==void 0?u:t.value});let r=!1;const a=u=>{n.value=u},o=()=>{var u;n.value=e.to,r=!1,(u=e.onFinish)===null||u===void 0||u.call(e)},d=(u=e.from,m=e.to)=>{r=!0,n.value=e.from,u!==m&&Me({from:u,to:m,duration:s,onUpdate:a,onFinish:o})},f=S(()=>{var u;const v=Ce(n.value,e.precision).toFixed(e.precision).split("."),p=new Intl.NumberFormat(i.value),g=(u=p.formatToParts(.5).find(_=>_.type==="decimal"))===null||u===void 0?void 0:u.value,c=e.showSeparator?p.format(Number(v[0])):v[0],l=v[1];return{integer:c,decimal:l,decimalSeparator:g}});function b(){r||d()}return Q(()=>{de(()=>{e.active&&d()})}),Object.assign({formattedValue:f},{play:b})},render(){const{formattedValue:{integer:e,decimal:t,decimalSeparator:s}}=this;return[e,t?s:null,t]}}),Ie=F("statistic",[M("label",`
 font-weight: var(--n-label-font-weight);
 transition: .3s color var(--n-bezier);
 font-size: var(--n-label-font-size);
 color: var(--n-label-text-color);
 `),F("statistic-value",`
 margin-top: 4px;
 font-weight: var(--n-value-font-weight);
 `,[M("prefix",`
 margin: 0 4px 0 0;
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-prefix-text-color);
 `,[F("icon",{verticalAlign:"-0.125em"})]),M("content",`
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-text-color);
 `),M("suffix",`
 margin: 0 0 0 4px;
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-suffix-text-color);
 `,[F("icon",{verticalAlign:"-0.125em"})])])]),Te=Object.assign(Object.assign({},U.props),{tabularNums:Boolean,label:String,value:[String,Number]}),je=I({name:"Statistic",props:Te,slots:Object,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:s,mergedRtlRef:n}=X(e),i=U("Statistic","-statistic",Ie,ce,e,t),r=pe("Statistic",n,t),a=S(()=>{const{self:{labelFontWeight:d,valueFontSize:f,valueFontWeight:b,valuePrefixTextColor:x,labelTextColor:u,valueSuffixTextColor:m,valueTextColor:v,labelFontSize:p},common:{cubicBezierEaseInOut:g}}=i.value;return{"--n-bezier":g,"--n-label-font-size":p,"--n-label-font-weight":d,"--n-label-text-color":u,"--n-value-font-weight":b,"--n-value-font-size":f,"--n-value-prefix-text-color":x,"--n-value-suffix-text-color":m,"--n-value-text-color":v}}),o=s?me("statistic",void 0,a,e):void 0;return{rtlEnabled:r,mergedClsPrefix:t,cssVars:s?void 0:a,themeClass:o?.themeClass,onRender:o?.onRender}},render(){var e;const{mergedClsPrefix:t,$slots:{default:s,label:n,prefix:i,suffix:r}}=this;return(e=this.onRender)===null||e===void 0||e.call(this),h("div",{class:[`${t}-statistic`,this.themeClass,this.rtlEnabled&&`${t}-statistic--rtl`],style:this.cssVars},B(n,a=>h("div",{class:`${t}-statistic__label`},this.label||a)),h("div",{class:`${t}-statistic-value`,style:{fontVariantNumeric:this.tabularNums?"tabular-nums":""}},B(i,a=>a&&h("span",{class:`${t}-statistic-value__prefix`},a)),this.value!==void 0?h("span",{class:`${t}-statistic-value__content`},this.value):B(s,a=>a&&h("span",{class:`${t}-statistic-value__content`},a)),B(r,a=>a&&h("span",{class:`${t}-statistic-value__suffix`},a))))}});export{Pe as _,je as a,De as b,Oe as c};
