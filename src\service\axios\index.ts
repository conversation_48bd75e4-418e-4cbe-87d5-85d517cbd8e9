import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import axios from 'axios'
import { router } from '@/router'
import { useAuthStore } from '@/store'


// สร้าง axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: window.location.origin === import.meta.env.VITE_LOCAL_HOST
    ? import.meta.env.VITE_LOCAL_BASE_URL
    : window.location.origin === import.meta.env.VITE_PRO_HOST
      ? import.meta.env.VITE_PRO_BASE_URL
      : window.location.origin === import.meta.env.VITE_PGPRO_HOST ?
        import.meta.env.VITE_PGPRO_BASE_URL : import.meta.env.VITE_UAT_BASE_URL,
  timeout: 120000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// เพิ่ม interceptors (ถ้าต้องการ)
axiosInstance.interceptors.request.use(
  (config) => {

    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      const token = JSON.parse(accessToken);
      config.headers.Authorization = `Bearer ${token.value}`
    }
    // คุณสามารถใส่ token หรือแก้ไข config ได้ที่นี่
    return config
  },
  error => Promise.reject(error),
)

axiosInstance.interceptors.response.use(
  response => response,
  async (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      try {
        await authStore.refreshAccessToken()
        // Retry original request
        return axiosInstance.request(error.config)
      } catch (refreshError) {
        // Redirect to login if refresh fails
        authStore.logout()
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  },
)

// สร้าง object http สำหรับการใช้งาน
const http = {
  get<T = any, R = AxiosResponse<T>>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.get(url, config)
  },
  post<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.post(url, data, config)
  },
  put<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.put(url, data, config)
  },
  delete<R = AxiosResponse>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.delete(url, config)
  },
}

export default http
