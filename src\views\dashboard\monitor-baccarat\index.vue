<template>
  <div>
    <n-grid cols="1 700:2 1024:4 " :x-gap="12" :y-gap="12">
      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic label="จำนวนเงินทั้งหมด">
              <n-number-animation :from="0" :to="12039" show-separator />
            </n-statistic>
            <n-icon color="#ffb549" size="42">
              <icon-park-outline-funds />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic label="ชนะเดือนนี้">
              <n-number-animation :from="0" :to="12039" show-separator />
            </n-statistic>
            <n-icon color="#18a058" size="42">
              <icon-park-outline-positive-dynamics />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic label="แพ้เดือนนี้">
              <n-number-animation :from="0" :to="12039" show-separator />
            </n-statistic>
            <n-icon color="#d03050" size="42">
              <icon-park-outline-negative-dynamics />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>
      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic label="กำไรสุทธิเดือนนี้">
              <n-number-animation :from="0" :to="12039" show-separator />
            </n-statistic>
            <n-icon color="#7c3aed" size="42">
              <icon-park-outline-imbalance />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

      <n-gi span="8">
        <n-card>
          <div
            class="flex flex-wrap sm:flex-nowrap justify-center items-center gap-3"
          >
            <p class="text-center text-lg font-bold">ยอดชนะ-แพ้รายปี</p>
            <n-date-picker
              type="year"
              placeholder="เลือกปี"
              v-model:value="selectYear"
              class="w-28 z-10"
            />
          </div>
          <Chart />
        </n-card>
      </n-gi>

      <n-gi span="8">
        <n-card>
          <div
            class="flex flex-wrap sm:flex-nowrap justify-center items-center gap-3"
          >
            <p class="text-center text-lg font-bold">รายได้ทั้งหมด</p>
            <n-date-picker
              type="year"
              placeholder="เลือกปี"
              v-model:value="selectYear"
              class="w-28 z-10"
            />
          </div>

          <Chart2 />
        </n-card>
      </n-gi>
    </n-grid>
  </div>
</template>
<script setup lang="ts">
import Chart from "../monitor-baccarat/components/chart.vue";
import Chart2 from "../monitor-baccarat/components/chart2.vue";
import { useAppStore } from "@/store";
const appStore = useAppStore();
const primaryColor = computed(() => appStore.primaryColor);
watch(
  () => appStore.primaryColor,
  (newColor) => {
    primaryColor.value = newColor;
  }
);
const selectYear = ref(new Date());
</script>
<style scoped></style>
