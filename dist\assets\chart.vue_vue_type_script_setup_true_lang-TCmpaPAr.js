import{d as o,r as s,aj as a,m as t,o as r}from"./index-pY9FjpQW.js";const l={ref:"lineRef",class:"h-400px"},p=o({__name:"chart",setup(i){const e=s({tooltip:{trigger:"axis"},grid:{left:"2%",right:"2%",bottom:"0%",top:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisTick:{show:!1},axisLine:{show:!1}},yAxis:{type:"value",splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}},series:[{color:"#37a2da",name:"Email",type:"line",smooth:!0,stack:"Total",areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:.25,color:"#37a2da"},{offset:1,color:"#fff"}]}},emphasis:{focus:"series"},data:[120,132,101,134,90,230,210]},{color:"#9fe6b8",name:"Union Ads",type:"line",smooth:!0,stack:"Total",areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:.25,color:"#9fe6b8"},{offset:1,color:"#fff"}]}},emphasis:{focus:"series"},data:[220,182,191,234,290,330,310]}]});return a("lineRef",e),(f,n)=>(r(),t("div",l,null,512))}});export{p as _};
