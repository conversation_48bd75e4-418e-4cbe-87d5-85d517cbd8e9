import{a7 as n,m as t,o as r,a as e}from"./index-pY9FjpQW.js";const a={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function s(i,o){return r(),t("svg",a,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M24 36c11.046 0 20-12 20-12s-8.954-12-20-12S4 24 4 24s8.954 12 20 12Z"}),e("path",{d:"M24 29a5 5 0 1 0 0-10a5 5 0 0 0 0 10Z"})],-1)]))}const c=n({name:"icon-park-outline-preview-open",render:s});export{c as _};
