<template>
  <div>
    <n-space vertical size="large">
      <!-- Header Section -->
      <n-card>
        <n-space justify="space-between" align="center">
          <n-space align="center">
            <n-avatar size="large" color="#1a8a93">
              <icon-park-outline-transaction />
            </n-avatar>
            <div>
              <n-h2 style="margin: 0">{{ $t("orderIntentManagement") }}</n-h2>
              <n-text depth="3"
                >Manage buy orders from text and slip sources</n-text
              >
            </div>
          </n-space>
          <n-space>
            <n-button @click="loadOrderIntents" :loading="loading">
              <template #icon>
                <icon-park-outline-refresh />
              </template>
              {{ $t("refresh") }}
            </n-button>
            <n-button type="primary" @click="showAddModal = true">
              <template #icon>
                <icon-park-outline-plus />
              </template>
              {{ $t("addNewOrder") }}
            </n-button>
          </n-space>
        </n-space>
      </n-card>

      <!-- Statistics Cards -->
      <n-grid cols="1 s:2 m:4" responsive="screen" :x-gap="16">
        <n-gi>
          <n-card>
            <n-statistic :value="statistics.total" class="text-center">
              <template #label>
                <n-space align="center" justify="center">
                  <icon-park-outline-transaction class="text-blue-500" />
                  <span>{{ $t("totalOrders") }}</span>
                </n-space>
              </template>
            </n-statistic>
          </n-card>
        </n-gi>
        <n-gi>
          <n-card>
            <n-statistic :value="statistics.textOrders" class="text-center">
              <template #label>
                <n-space align="center" justify="center">
                  <icon-park-outline-message class="text-green-500" />
                  <span>{{ $t("textOrders") }}</span>
                </n-space>
              </template>
            </n-statistic>
          </n-card>
        </n-gi>
        <n-gi>
          <n-card>
            <n-statistic :value="statistics.slipOrders" class="text-center">
              <template #label>
                <n-space align="center" justify="center">
                  <icon-park-outline-game class="text-orange-500" />
                  <span>{{ $t("slipOrders") }}</span>
                </n-space>
              </template>
            </n-statistic>
          </n-card>
        </n-gi>
        <n-gi>
          <n-card>
            <n-statistic
              :value="statistics.totalUSDT"
              :precision="2"
              class="text-center"
            >
              <template #label>
                <n-space align="center" justify="center">
                  <img
                    src="/images/country/us.webp"
                    alt="USDT"
                    class="w-4 rounded"
                  />
                  <span>{{ $t("totalUSDT") }}</span>
                </n-space>
              </template>
              <template #suffix>
                <span class="text-sm">USDT</span>
              </template>
            </n-statistic>
          </n-card>
        </n-gi>
      </n-grid>

      <!-- Filters -->
      <n-card>
        <template #header>
          <n-space align="center">
            <icon-park-outline-filter class="text-xl text-purple-500" />
            <span>{{ $t("filters") }}</span>
          </n-space>
        </template>

        <n-grid cols="1 s:2 m:4" responsive="screen" :x-gap="16" :y-gap="16">
          <n-gi>
            <n-form-item :label="$t('source')">
              <n-select
                v-model:value="filters.source"
                :options="sourceOptions"
                clearable
                :placeholder="$t('selectSource')"
                @update:value="applyFilters"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('dateRange')">
              <n-date-picker
                v-model:value="filters.dateRange"
                type="daterange"
                clearable
                :placeholder="[$t('startDate'), $t('endDate')]"
                @update:value="applyFilters"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('username')">
              <n-input
                v-model:value="filters.username"
                clearable
                :placeholder="$t('searchByUsername')"
                @input="debounceSearch"
              >
                <template #prefix>
                  <icon-park-outline-search />
                </template>
              </n-input>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('amountRange')">
              <n-input-group>
                <n-input-number
                  v-model:value="filters.minAmount"
                  :placeholder="$t('minAmount')"
                  :min="0"
                  style="width: 50%"
                  @update:value="applyFilters"
                />
                <n-input-number
                  v-model:value="filters.maxAmount"
                  :placeholder="$t('maxAmount')"
                  :min="0"
                  style="width: 50%"
                  @update:value="applyFilters"
                />
              </n-input-group>
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-card>

      <!-- Order Intents Table -->
      <n-card>
        <template #header>
          <n-space align="center" justify="space-between">
            <n-space align="center">
              <icon-park-outline-list class="text-xl text-blue-500" />
              <span>{{ $t("orderIntents") }}</span>
            </n-space>
            <n-space>
              <n-button @click="exportData" :loading="exporting">
                <template #icon>
                  <icon-park-outline-download />
                </template>
                {{ $t("export") }}
              </n-button>
            </n-space>
          </n-space>
        </template>

        <n-data-table
          :columns="columns"
          :data="orderIntents"
          :loading="loading"
          :pagination="pagination"
          :row-key="(row) => row._id"
          :scroll-x="1200"
          size="large"
          striped
        />
      </n-card>
    </n-space>

    <!-- Add/Edit Order Modal -->
    <n-modal
      v-model:show="showAddModal"
      preset="dialog"
      style="width: 600px"
      :title="editingOrder ? $t('editOrder') : $t('addNewOrder')"
    >
      <n-form
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="top"
      >
        <n-grid cols="1 s:2" responsive="screen" :x-gap="16">
          <n-gi>
            <n-form-item :label="$t('source')" path="source">
              <n-select
                v-model:value="formModel.source"
                :options="sourceOptions"
                size="large"
                :placeholder="$t('selectSource')"
                @update:value="onSourceChange"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('direction')" path="direction">
              <n-select
                v-model:value="formModel.direction"
                :options="directionOptions"
                size="large"
                :placeholder="$t('selectDirection')"
              />
            </n-form-item>
          </n-gi>
        </n-grid>

        <n-form-item :label="$t('userInfo')" path="username">
          <n-input-group>
            <n-input
              v-model:value="formModel.username"
              :placeholder="$t('username')"
              style="width: 33.33%"
            >
              <template #prefix>
                <span>@</span>
              </template>
            </n-input>
            <n-input
              v-model:value="formModel.firstName"
              :placeholder="$t('firstName')"
              style="width: 33.33%"
            />
            <n-input
              v-model:value="formModel.lastName"
              :placeholder="$t('lastName')"
              style="width: 33.33%"
            />
          </n-input-group>
        </n-form-item>

        <n-form-item :label="$t('chatInfo')" path="chatId">
          <n-input-group>
            <n-input
              v-model:value="formModel.chatId"
              :placeholder="$t('chatId')"
              style="width: 50%"
            >
              <template #prefix>
                <icon-park-outline-message />
              </template>
            </n-input>
            <n-input-number
              v-model:value="formModel.messageId"
              :placeholder="$t('messageId')"
              style="width: 50%"
            />
          </n-input-group>
        </n-form-item>

        <n-form-item :label="$t('rawText')" path="rawText">
          <n-input
            v-model:value="formModel.rawText"
            type="textarea"
            :placeholder="$t('enterRawText')"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </n-form-item>

        <n-grid cols="1 s:3" responsive="screen" :x-gap="16">
          <n-gi>
            <n-form-item :label="$t('fiatTHB')" path="fiatTHB">
              <n-input-number
                v-model:value="formModel.fiatTHB"
                :min="0"
                :step="1"
                size="large"
                style="width: 100%"
                :placeholder="$t('enterAmount')"
                @update:value="calculateUSDT"
              >
                <template #prefix>
                  <img
                    src="/images/country/th.webp"
                    alt="THB"
                    class="w-4 rounded"
                  />
                </template>
                <template #suffix>
                  <span>฿</span>
                </template>
              </n-input-number>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('rateTHBPerUSDT')" path="rateTHBPerUSDT">
              <n-input-number
                v-model:value="formModel.rateTHBPerUSDT"
                :min="1"
                :max="100"
                :step="0.01"
                :precision="2"
                size="large"
                style="width: 100%"
                :placeholder="$t('enterRate')"
                @update:value="calculateUSDT"
              >
                <template #suffix>
                  <span>฿/USDT</span>
                </template>
              </n-input-number>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('usdtAmount')" path="usdtAmount">
              <n-input-number
                v-model:value="formModel.usdtAmount"
                :min="0"
                :step="0.000001"
                :precision="6"
                size="large"
                style="width: 100%"
                readonly
              >
                <template #prefix>
                  <img
                    src="/images/country/us.webp"
                    alt="USDT"
                    class="w-4 rounded"
                  />
                </template>
                <template #suffix>
                  <span>USDT</span>
                </template>
              </n-input-number>
            </n-form-item>
          </n-gi>
        </n-grid>

        <!-- Source Meta (แสดงเมื่อ source = "slip") -->
        <n-collapse v-if="formModel.source === 'slip'">
          <n-collapse-item :title="$t('slipDetails')" name="slip-details">
            <n-grid cols="1 s:2" responsive="screen" :x-gap="16">
              <n-gi>
                <n-form-item :label="$t('bankName')">
                  <n-input
                    v-model:value="formModel.sourceMeta.bankName"
                    :placeholder="$t('enterBankName')"
                  >
                    <template #prefix>
                      <icon-park-outline-bank />
                    </template>
                  </n-input>
                </n-form-item>
              </n-gi>
              <n-gi>
                <n-form-item :label="$t('refNo')">
                  <n-input
                    v-model:value="formModel.sourceMeta.refNo"
                    :placeholder="$t('enterRefNo')"
                  />
                </n-form-item>
              </n-gi>
              <n-gi>
                <n-form-item :label="$t('meslipCode')">
                  <n-input-number
                    v-model:value="formModel.sourceMeta.meslipCode"
                    :placeholder="$t('enterMeslipCode')"
                    style="width: 100%"
                  />
                </n-form-item>
              </n-gi>
              <n-gi>
                <n-form-item :label="$t('meslipDesc')">
                  <n-input
                    v-model:value="formModel.sourceMeta.meslipDesc"
                    :placeholder="$t('enterMeslipDesc')"
                  />
                </n-form-item>
              </n-gi>
            </n-grid>
            <n-form-item :label="$t('meslipDateText')">
              <n-input
                v-model:value="formModel.sourceMeta.meslipDateText"
                :placeholder="$t('enterMeslipDateText')"
              >
                <template #prefix>
                  <icon-park-outline-time />
                </template>
              </n-input>
            </n-form-item>
          </n-collapse-item>
        </n-collapse>

        <!-- Rate Information -->
        <n-grid cols="1 s:2" responsive="screen" :x-gap="16">
          <n-gi>
            <n-form-item :label="$t('rateSource')" path="rateSource">
              <n-input
                v-model:value="formModel.rateSource"
                :placeholder="$t('enterRateSource')"
                readonly
              >
                <template #prefix>
                  <icon-park-outline-setting />
                </template>
              </n-input>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item :label="$t('rateFetchedAt')">
              <n-date-picker
                v-model:value="formModel.rateFetchedAt"
                type="datetime"
                :placeholder="$t('selectDateTime')"
                style="width: 100%"
              />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showAddModal = false">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ editingOrder ? $t("update") : $t("create") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Order Details Modal -->
    <n-modal
      v-model:show="showDetailsModal"
      preset="card"
      style="width: 800px"
      :title="$t('orderDetails')"
    >
      <div v-if="selectedOrder">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item :label="$t('orderId')">
            <n-text code>{{ selectedOrder._id }}</n-text>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('source')">
            <n-tag
              :type="selectedOrder.source === 'text' ? 'success' : 'warning'"
            >
              {{ selectedOrder.source }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('direction')">
            <n-tag type="info">{{ selectedOrder.direction }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('username')">
            <n-space align="center">
              <n-avatar size="small">
                <icon-park-outline-user />
              </n-avatar>
              <span>@{{ selectedOrder.username }}</span>
            </n-space>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('fullName')">
            {{ selectedOrder.firstName }} {{ selectedOrder.lastName }}
          </n-descriptions-item>
          <n-descriptions-item :label="$t('chatId')">
            <n-text code>{{ selectedOrder.chatId }}</n-text>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('messageId')">
            {{ selectedOrder.messageId }}
          </n-descriptions-item>
          <n-descriptions-item :label="$t('rawText')">
            <n-text code>{{ selectedOrder.rawText }}</n-text>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('fiatTHB')">
            <n-space align="center">
              <img
                src="/images/country/th.webp"
                alt="THB"
                class="w-4 rounded"
              />
              <n-number-animation
                :from="0"
                :to="selectedOrder.fiatTHB"
                show-separator
              />
              <span>฿</span>
            </n-space>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('rateTHBPerUSDT')">
            <n-space align="center">
              <span>{{ selectedOrder.rateTHBPerUSDT }}</span>
              <span>฿/USDT</span>
            </n-space>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('usdtAmount')">
            <n-space align="center">
              <img
                src="/images/country/us.webp"
                alt="USDT"
                class="w-4 rounded"
              />
              <n-number-animation
                :from="0"
                :to="selectedOrder.usdtAmount"
                :precision="6"
                show-separator
              />
              <span>USDT</span>
            </n-space>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('rateSource')">
            <n-tag size="small">{{ selectedOrder.rateSource }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item :label="$t('rateFetchedAt')">
            {{ formatDateTime(selectedOrder.rateFetchedAt) }}
          </n-descriptions-item>
          <n-descriptions-item :label="$t('createdAt')">
            {{ formatDateTime(selectedOrder.createdAt) }}
          </n-descriptions-item>
        </n-descriptions>

        <!-- Slip Details (ถ้ามี) -->
        <div
          v-if="selectedOrder.source === 'slip' && selectedOrder.sourceMeta"
          class="mt-6"
        >
          <n-divider>
            <n-space align="center">
              <icon-park-outline-game />
              <span>{{ $t("slipDetails") }}</span>
            </n-space>
          </n-divider>
          <n-descriptions :column="2" bordered>
            <n-descriptions-item :label="$t('bankName')">
              <n-space align="center">
                <icon-park-outline-bank />
                <span>{{ selectedOrder.sourceMeta.bankName }}</span>
              </n-space>
            </n-descriptions-item>
            <n-descriptions-item :label="$t('refNo')">
              <n-text code>{{ selectedOrder.sourceMeta.refNo }}</n-text>
            </n-descriptions-item>
            <n-descriptions-item :label="$t('meslipCode')">
              <n-tag
                :type="
                  selectedOrder.sourceMeta.meslipCode === 101
                    ? 'success'
                    : 'error'
                "
              >
                {{ selectedOrder.sourceMeta.meslipCode }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item :label="$t('meslipDesc')">
              {{ selectedOrder.sourceMeta.meslipDesc }}
            </n-descriptions-item>
            <n-descriptions-item :label="$t('meslipDateText')" :span="2">
              {{ selectedOrder.sourceMeta.meslipDateText }}
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="tsx">
import http from "@/service/axios";

const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();

// Reactive data
const loading = ref(false);
const exporting = ref(false);
const submitting = ref(false);
const showAddModal = ref(false);
const showDetailsModal = ref(false);
const editingOrder = ref(null);
const selectedOrder = ref(null);
const formRef = ref();

// Data
const orderIntents = ref([
  {
    _id: "1",
    source: "text",
    username: "user1",
    firstName: "John",
    lastName: "Doe",
    rawText: "Hello world",
    direction: "buy",
    fiatTHB: 1000,
    rateTHBPerUSDT: 35.5,
    usdtAmount: 28.13,
    sourceMeta: null,
    rateSource: "admin-manual",
    rateFetchedAt: "2022-01-01T00:00:00.000Z",
    createdAt: "2022-01-01T00:00:00.000Z",
  },
]);

// Statistics
const statistics = computed(() => ({
  total: orderIntents.value.length,
  textOrders: orderIntents.value.filter((item) => item.source === "text")
    .length,
  slipOrders: orderIntents.value.filter((item) => item.source === "slip")
    .length,
  totalUSDT: orderIntents.value.reduce(
    (sum, item) => sum + (item.usdtAmount || 0),
    0
  ),
}));

// Filters
const filters = reactive({
  source: null,
  dateRange: null,
  username: "",
  minAmount: null,
  maxAmount: null,
});

// Form model
const formModel = reactive({
  chatId: "",
  messageId: null,
  userId: "",
  username: "",
  firstName: "",
  lastName: "",
  rawText: "",
  direction: "buy",
  fiatTHB: null,
  rateTHBPerUSDT: 35.5,
  usdtAmount: null,
  source: "text",
  sourceMeta: {
    depositIntentId: null,
    bankName: "",
    refNo: "",
    meslipCode: null,
    meslipDesc: "",
    meslipDateText: "",
  },
  rateSource: "admin-manual",
  rateFetchedAt: Date.now(),
});

// Options
const sourceOptions = [
  { label: t("textSource"), value: "text" },
  { label: t("slipSource"), value: "slip" },
];

const directionOptions = [{ label: t("buy"), value: "buy" }];

// Form rules
const rules = {
  source: {
    required: true,
    message: t("pleaseSelectSource"),
    trigger: ["blur", "change"],
  },
  direction: {
    required: true,
    message: t("pleaseSelectDirection"),
    trigger: ["blur", "change"],
  },
  username: {
    required: true,
    message: t("pleaseEnterUsername"),
    trigger: ["blur", "input"],
  },
  chatId: {
    required: true,
    message: t("pleaseEnterChatId"),
    trigger: ["blur", "input"],
  },
  fiatTHB: {
    type: "number",
    required: true,
    message: t("pleaseEnterAmount"),
    trigger: ["blur", "change"],
  },
  rateTHBPerUSDT: {
    type: "number",
    required: true,
    message: t("pleaseEnterRate"),
    trigger: ["blur", "change"],
  },
};

// Table columns
const columns = computed(() => [
  {
    title: t("source"),
    key: "source",
    render: (row) => (
      <n-tag type={row.source === "text" ? "success" : "warning"} size="small">
        {row.source}
      </n-tag>
    ),
  },
  {
    title: t("username"),
    key: "username",
    render: (row) => <span>@{row.username}</span>,
  },
  {
    title: t("fullName"),
    key: "fullName",
    render: (row) => `${row.firstName || ""} ${row.lastName || ""}`.trim(),
  },
  {
    title: t("fiatTHB"),
    key: "fiatTHB",
    render: (row) => (
      <n-space align="center">
        <img src="/images/country/th.webp" alt="THB" class="w-4 rounded" />
        <span>{row.fiatTHB?.toLocaleString() || 0} ฿</span>
      </n-space>
    ),
  },
  {
    title: t("usdtAmount"),
    key: "usdtAmount",
    render: (row) => (
      <n-space align="center">
        <img src="/images/country/us.webp" alt="USDT" class="w-4 rounded" />
        <span>{row.usdtAmount?.toFixed(6) || 0} USDT</span>
      </n-space>
    ),
  },
  {
    title: t("rate"),
    key: "rateTHBPerUSDT",
    render: (row) => `${row.rateTHBPerUSDT} ฿/USDT`,
  },
  {
    title: t("createdAt"),
    key: "createdAt",
    render: (row) => formatDateTime(row.createdAt),
  },
  {
    title: t("actions"),
    key: "actions",
    align: "center",
    render: (row) => (
      <n-space justify="center">
        <n-button
          size="small"
          type="default"
          secondary
          onClick={() => viewDetails(row)}
        >
          <icon-park-outline-eyes />
        </n-button>
        <n-button
          size="small"
          type="primary"
          secondary
          onClick={() => editOrder(row)}
        >
          <icon-park-outline-edit />
        </n-button>
        <n-button
          size="small"
          type="error"
          secondary
          onClick={() => deleteOrder(row)}
        >
          <icon-park-outline-delete />
        </n-button>
      </n-space>
    ),
  },
]);

// Pagination
const pagination = reactive({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// Methods
const loadOrderIntents = async () => {
  loading.value = true;
  try {
    const response = await http.get("v1/order-intents");
    if (response.data.status) {
      orderIntents.value = response.data.data || [];
      applyFilters();
    } else {
      message.error(response.data.message || t("loadDataFailed"));
    }
  } catch (error) {
    console.error("Load order intents error:", error);
    message.error(t("loadDataFailed"));
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  let filtered = [...orderIntents.value];

  // Filter by source
  if (filters.source) {
    filtered = filtered.filter((item) => item.source === filters.source);
  }

  // Filter by date range
  if (filters.dateRange && filters.dateRange.length === 2) {
    const [startDate, endDate] = filters.dateRange;
    filtered = filtered.filter((item) => {
      const itemDate = new Date(item.createdAt);
      return itemDate >= startDate && itemDate <= endDate;
    });
  }

  // Filter by username
  if (filters.username) {
    const searchTerm = filters.username.toLowerCase();
    filtered = filtered.filter(
      (item) =>
        item.username?.toLowerCase().includes(searchTerm) ||
        item.firstName?.toLowerCase().includes(searchTerm) ||
        item.lastName?.toLowerCase().includes(searchTerm)
    );
  }

  // Filter by amount range
  if (filters.minAmount !== null) {
    filtered = filtered.filter(
      (item) => (item.fiatTHB || 0) >= filters.minAmount
    );
  }
  if (filters.maxAmount !== null) {
    filtered = filtered.filter(
      (item) => (item.fiatTHB || 0) <= filters.maxAmount
    );
  }
};

const debounceSearch = (() => {
  let timeout: NodeJS.Timeout;
  return () => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      applyFilters();
    }, 300);
  };
})();

const onSourceChange = (value: string) => {
  if (value === "slip") {
    // Initialize slip meta if not exists
    if (!formModel.sourceMeta) {
      formModel.sourceMeta = {
        depositIntentId: null,
        bankName: "",
        refNo: "",
        meslipCode: null,
        meslipDesc: "",
        meslipDateText: "",
      };
    }
  }
};

const calculateUSDT = () => {
  if (formModel.fiatTHB && formModel.rateTHBPerUSDT) {
    formModel.usdtAmount = formModel.fiatTHB / formModel.rateTHBPerUSDT;
  } else {
    formModel.usdtAmount = null;
  }
};

const resetForm = () => {
  Object.assign(formModel, {
    chatId: "",
    messageId: null,
    userId: "",
    username: "",
    firstName: "",
    lastName: "",
    rawText: "",
    direction: "buy",
    fiatTHB: null,
    rateTHBPerUSDT: 35.5,
    usdtAmount: null,
    source: "text",
    sourceMeta: {
      depositIntentId: null,
      bankName: "",
      refNo: "",
      meslipCode: null,
      meslipDesc: "",
      meslipDateText: "",
    },
    rateSource: "admin-manual",
    rateFetchedAt: Date.now(),
  });
  editingOrder.value = null;
};

const handleSubmit = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      submitting.value = true;
      try {
        const payload = { ...formModel };

        // Clean up sourceMeta if source is not slip
        if (payload.source !== "slip") {
          delete payload.sourceMeta;
        }

        let response;
        if (editingOrder.value) {
          response = await http.put(
            `v1/order-intents/${editingOrder.value._id}`,
            payload
          );
        } else {
          response = await http.post("v1/order-intents", payload);
        }

        if (response.data.status) {
          message.success(
            editingOrder.value ? t("updateSuccess") : t("createSuccess")
          );
          showAddModal.value = false;
          resetForm();
          loadOrderIntents();
        } else {
          message.error(response.data.message || t("operationFailed"));
        }
      } catch (error) {
        console.error("Submit error:", error);
        message.error(t("operationFailed"));
      } finally {
        submitting.value = false;
      }
    }
  });
};

const viewDetails = (row: any) => {
  selectedOrder.value = row;
  showDetailsModal.value = true;
};

const editOrder = (row: any) => {
  editingOrder.value = row;
  Object.assign(formModel, {
    ...row,
    sourceMeta: row.sourceMeta || {
      depositIntentId: null,
      bankName: "",
      refNo: "",
      meslipCode: null,
      meslipDesc: "",
      meslipDateText: "",
    },
  });
  showAddModal.value = true;
};

const deleteOrder = (row: any) => {
  dialog.warning({
    title: t("confirmDelete"),
    content: t("deleteOrderConfirm"),
    positiveText: t("confirm"),
    negativeText: t("cancel"),
    onPositiveClick: async () => {
      try {
        const response = await http.delete(`v1/order-intents/${row._id}`);
        if (response.data.status) {
          message.success(t("deleteSuccess"));
          loadOrderIntents();
        } else {
          message.error(response.data.message || t("deleteFailed"));
        }
      } catch (error) {
        console.error("Delete error:", error);
        message.error(t("deleteFailed"));
      }
    },
  });
};

const exportData = async () => {};

const formatDateTime = (timestamp: number | string) => {
  if (!timestamp) return "-";
  const date = new Date(timestamp);
  return date.toLocaleString("th-TH", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// Lifecycle
onMounted(() => {
  loadOrderIntents();
});
</script>
