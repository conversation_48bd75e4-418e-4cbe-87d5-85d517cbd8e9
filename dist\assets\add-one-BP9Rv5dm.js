import{a7 as n,m as t,o as r,a as e}from"./index-pY9FjpQW.js";const a={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function s(i,o){return r(),t("svg",a,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"}),e("path",{"stroke-linecap":"round",d:"M24 16v16m-8-8h16"})],-1)]))}const c=n({name:"icon-park-outline-add-one",render:s});export{c as _};
