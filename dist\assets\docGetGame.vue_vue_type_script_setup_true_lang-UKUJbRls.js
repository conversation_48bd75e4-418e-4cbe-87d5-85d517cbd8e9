import{_ as y}from"./copy-BxkeU8Ds.js";import{d as b,r as w,ac as $,ad as c,m as T,o as k,b as o,ab as B,w as e,a as t,g as d,B as C,t as l,ae as I,$ as z}from"./index-pY9FjpQW.js";import{_ as G}from"./Table-DoHSPnrC.js";const q={class:"border border-orange rounded-lg p-2 mt-4"},P={class:"font-bold"},R={class:"border rounded-lg p-2 mt-4"},j={class:"mt-4 overflow-x-auto"},N={class:"w-1/4"},O={class:"w-1/4"},S={class:"w-1/4"},V={class:"w-1/4"},A={class:"mt-4"},D={class:"bg-black/70 rounded-lg px-4 pt-4"},L={class:"absolute right-0 mr-10"},U=b({__name:"docGetGame",setup(E){const r=w(null),{t:a}=$(),p=c(),m=()=>{const s=r.value?.innerText;s&&navigator.clipboard.writeText(s).then(()=>{p.success(a("copysuccess"))})};return(s,n)=>{const u=C,i=I,g=G,_=y,f=z,v=B;return k(),T("div",null,[o(v,null,{default:e(()=>[n[18]||(n[18]=t("div",{class:"font-bold"},"Get Game List",-1)),t("div",null,[o(u,{type:"success",round:"",size:"small",class:"mt-3 cursor-default"},{default:e(()=>[d(l(s.$t("update")),1)]),_:1}),d(" "+l(s.$t("lastupdate")),1)]),t("div",q,[o(u,{class:"cursor-default",type:"warning",round:"",size:"tiny"},{default:e(()=>n[0]||(n[0]=[d("POST")])),_:1}),n[1]||(n[1]=t("span",null," {{ API_URL }}/seamless/games",-1))]),o(i),t("div",null,[t("div",P,l(s.$t("security")),1),t("div",R,[t("div",null,[o(u,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:e(()=>n[2]||(n[2]=[d("Authorization")])),_:1})]),n[4]||(n[4]=t("span",null," apiKey : Base64({{ agent_username }}:{{ secret_key }})",-1)),n[5]||(n[5]=t("hr",{class:"my-2"},null,-1)),t("div",null,[o(u,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:e(()=>n[3]||(n[3]=[d("Content Type")])),_:1})]),n[6]||(n[6]=t("span",null," Type: application/json",-1))])]),o(i),t("div",null,[n[16]||(n[16]=t("div",{class:"font-bold"},"Parameter Description",-1)),t("div",j,[o(g,null,{default:e(()=>[t("thead",null,[t("tr",null,[t("th",N,l(s.$t("property")),1),t("th",O,l(s.$t("type")),1),t("th",S,l(s.$t("required")),1),t("th",V,l(s.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[7]||(n[7]=t("td",null,"gameId",-1)),n[8]||(n[8]=t("td",null,"number",-1)),n[9]||(n[9]=t("td",null,"Required",-1)),t("td",null,l(s.$t("doc.ggl1")),1)]),t("tr",null,[n[10]||(n[10]=t("td",null,"productId",-1)),n[11]||(n[11]=t("td",null,"string",-1)),n[12]||(n[12]=t("td",null,"Required",-1)),t("td",null,l(s.$t("doc.ggl2")),1)]),t("tr",null,[n[13]||(n[13]=t("td",null,"language",-1)),n[14]||(n[14]=t("td",null,"string",-1)),n[15]||(n[15]=t("td",null,"Optional",-1)),t("td",null,l(s.$t("doc.ggl3")),1)])])]),_:1})])]),t("div",A,[n[17]||(n[17]=t("div",{class:"font-bold mb-4"},"Request Body",-1)),t("div",D,[t("div",L,[o(f,{trigger:"hover"},{trigger:e(()=>[o(u,{class:"text-white",onClick:m},{default:e(()=>[o(_)]),_:1})]),default:e(()=>[d(" "+l(s.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:r,class:"font-normal text-white overflow-x-auto"},`{
    "gameId": 0,
    "productId": "PGSOFT",
    "language": "th"
}
    `,512)])])]),_:1})])}}});export{U as _};
