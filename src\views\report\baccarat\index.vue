<script setup lang="tsx">
// @ts-nocheck
import { dateThTH, thTH, dateEnUS, enUS } from "naive-ui";
import type { DataTableColumns, FormInst } from "naive-ui";
import { useBoolean } from "@/hooks";
import {
  NButton,
  NPopconfirm,
  NSwitch,
  NTag,
  NModal,
  useMessage,
  useDialog,
} from "naive-ui";
import http from "@/service/axios";
import moment from "moment-timezone";
import { download } from "naive-ui/es/_utils";
const { t, locale } = useI18n();
const localeDate = ref(locale.value === "thTH" ? thTH : enUS);
const dateLocale = ref(locale.value === "thTH" ? dateThTH : dateEnUS);
import gameData from "@/assets/gameData.json";
import { log } from "node:console";

watch(
  () => locale.value,
  (newValue) => {
    if (newValue === "thTH") {
      dateLocale.value = dateThTH;
      localeDate.value = thTH;
    } else if (newValue === "enUS") {
      dateLocale.value = dateEnUS;
      localeDate.value = enUS;
    }
  }
);
const showModal = ref(false);
const iframeUrl = ref("");
const {
  bool: loading,
  setTrue: startLoading,
  setFalse: endLoading,
} = useBoolean(false);
const { bool: visible, setTrue: openModal } = useBoolean(false);
const UserData = JSON.parse(localStorage.getItem("userData"));
const position_type = ref(null);
const initialModel = {
  condition_1: "",
  condition_2: [
    moment().startOf("day").toDate(),
    moment().endOf("day").toDate(),
  ],
  condition_3: "",
};

const model = ref({ ...initialModel });
const formRef = ref<FormInst | null>();
const columns: DataTableColumns<Entity.User> = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("agent"),
    align: "center",
    key: "loginName",
    render: (row) => {
      return (
        <n-button
          tertiary
          round
          type={
            row.position_type === 1
              ? "warning"
              : row.position_type === 2 || row.position_type === 4
              ? "success"
              : "info"
          }
          onClick={() => handleEditTable(row)}
          style={{ cursor: "pointer" }}
        >
          {row.loginName}
        </n-button>
      );
    },
  },
  {
    title: t("position"),
    align: "center",
    key: "position_type",
    render: (row) => {
      return (
        <n-button
          size="small"
          class="cursor-default"
          ghost
          type={
            row.position_type === 1
              ? "warning"
              : row.position_type === 2 || row.position_type === 4
              ? "success"
              : "info"
          }
        >
          {row.position_type === 1
            ? t("reseller")
            : row.position_type === 2 || row.position_type === 4
            ? t("agent")
            : t("member")}
        </n-button>
      );
    },
  },
  {
    title: t("roundamount"),
    align: "center",
    key: "noTicket",
    render: (row) => {
      return Commas(row.noTicket);
    },
  },
  {
    title: t("betamount"),
    align: "center",
    key: "betAmount",
    render: (row) => {
      return Commas(row.betAmount);
    },
  },
  {
    title: t("won"),
    align: "center",
    key: "payoutAmount",
    render: (row) => {
      return Commas(row.payoutAmount);
    },
  },
  {
    title: t("winloss100"),
    align: "center",
    key: "memberWL",
    render: (row) => {
      const style = {
        color:
          row.memberWL100 < 0
            ? "#d03050"
            : row.memberWL100 > 0
            ? "#18a058"
            : "",
      };
      return <div style={style}>{Commas(row.memberWL100)}</div>;
    },
  },
  {
    title: t("member"),
    title(column) {
      return (
        <div class="bg-[#2080f0]/50 rounded-lg text-white">{t("member")}</div>
      );
    },
    align: "center",
    key: "member",
    children: [
      {
        title: t("winloss"),
        align: "center",
        key: "member-win-loss",
        render: (row, index) => {
          const style = {
            color:
              row.memberWL < 0 ? "#d03050" : row.memberWL > 0 ? "#18a058" : "",
          };

          return <div style={style}>{Commas(row.memberWL)}</div>;
        },
      },
      {
        title: t("comm"),
        align: "center",
        key: "member-comm",
        render: (row, index) => {
          return <div>0.00</div>;
        },
      },
      {
        title: t("total"),
        align: "center",
        key: "member-total",
        render: (row, index) => {
          const style = {
            color:
              row.memberTotal < 0
                ? "#d03050"
                : row.memberTotal > 0
                ? "#18a058"
                : "",
          };

          return <div style={style}>{Commas(row.memberTotal)}</div>;
        },
      },
    ],
  },
  {
    title(column) {
      if (
        UserData.value.position_type == 1 ||
        (UserData.value.position_type == 4 &&
          UserData.value.upline_position_type == 1)
      ) {
        return (
          <div
            class={[
              "rounded-lg text-white",
              Stap.value == 0
                ? "bg-[#d03050]/50"
                : Stap.value == 1
                ? "bg-[#f0a020]/50"
                : Stap.value == 2
                ? "bg-[#18a058]/50"
                : "bg-[#18a058]/50",
            ]}
          >
            {Stap.value == 0 ? StapTab.value[0].name : name.value}
          </div>
        );
      }
      if (
        UserData.value.position_type == 2 ||
        (UserData.value.position_type == 4 &&
          UserData.value.upline_position_type == 3)
      ) {
        return (
          <div
            class={[
              "rounded-lg text-white",
              Stap.value == 0
                ? "bg-[#f0a020]/50"
                : Stap.value == 1
                ? "bg-[#f0a020]/50"
                : Stap.value == 2
                ? "bg-[#18a058]/50"
                : "bg-[#18a058]/50",
            ]}
          >
            {Stap.value == 0 ? StapTab.value[0].name : name.value}
          </div>
        );
      } else {
        return (
          <div
            class={[
              "rounded-lg text-white",
              Stap.value == 1
                ? "bg-[#f0a020]/50"
                : Stap.value == 2
                ? "bg-[#18a058]/50"
                : "bg-[#18a058]/50",
            ]}
          >
            {Stap.value == 0 ? StapTab.value[0].name : name.value}
          </div>
        );
      }
    },
    align: "center",
    key: "Company",
    children: [
      {
        title: t("winloss"),
        align: "center",
        key: "company-win-loss",
        render: (row, index) => {
          const style = {
            color:
              row.companyWL < 0
                ? "#d03050"
                : row.companyWL > 0
                ? "#18a058"
                : "",
          };

          return <div style={style}>{Commas(row.companyWL)}</div>;
        },
      },
      {
        title: t("comm"),
        align: "center",
        key: "company-comm",
        render: (row, index) => {
          return <div>0.00</div>;
        },
      },
      {
        title: t("total"),
        align: "center",
        key: "company-total",
        render: (row, index) => {
          const style = {
            color:
              row.companyTotal < 0
                ? "#d03050"
                : row.companyTotal > 0
                ? "#18a058"
                : "",
          };

          return <div style={style}>{Commas(row.companyTotal)}</div>;
        },
      },
    ],
  },
  {
    title: t("provider"),
    title(column) {
      return (
        <div class="bg-[#000000]/50 rounded-lg text-white">{t("provider")}</div>
      );
    },
    align: "center",
    key: "provider",
    children: [
      {
        title: t("winloss"),
        align: "center",
        key: "provider-win-loss",
        render: (row, index) => {
          const style = {
            color:
              row.providerWL < 0
                ? "#d03050"
                : row.providerWL > 0
                ? "#18a058"
                : "",
          };

          return <div style={style}>{Commas(row.providerWL)}</div>;
        },
      },
      {
        title: t("comm"),
        align: "center",
        key: "provider-comm",
        render: (row, index) => {
          return <div>0.00</div>;
        },
      },
      {
        title: t("total"),
        align: "center",
        key: "provider-total",
        render: (row, index) => {
          const style = {
            color:
              row.providerTotal < 0
                ? "#d03050"
                : row.providerTotal > 0
                ? "#18a058"
                : "",
          };

          return <div style={style}>{Commas(row.providerTotal)}</div>;
        },
      },
    ],
  },
]);

const columns1: DataTableColumns<Entity.User> = [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("player"),
    align: "center",
    key: "loginName",
    render: (row) => {
      return (
        <NTag onClick={() => handleUser(row)} style={{ cursor: "pointer" }}>
          {row.loginName}
        </NTag>
      );
    },
  },
  {
    title: t("roundamount"),
    align: "center",
    key: "noTicket",
    render: (row) => {
      return Commas(row.noTicket);
    },
  },
  {
    title: t("betamount"),
    align: "center",
    key: "betAmount",
    render: (row) => {
      return Commas(row.betAmount);
    },
  },
  {
    title: t("won"),
    align: "center",
    key: "payoutAmount",
    render: (row) => {
      return Commas(row.payoutAmount);
    },
  },
  {
    title: t("winloss100"),
    align: "center",
    key: "memberWL",
    render: (row) => {
      return Commas(row.memberWL);
    },
  },
];
const columns2: DataTableColumns<Entity.User> = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("information"),
    align: "center",
    key: "information",
    render: (row) => {
      return (
        <div class="text-start">
          {/* <div>
            <span class="font-bold">{t("betid")}</span>: {row.patternId}
          </div> */}
          <div>
            <span class="font-bold">{t("gameCode")}</span>: {row.gameCode}
          </div>
          <div>
            <span class="font-bold">{t("roundid")}</span>: {row.roundId}
          </div>
          <div>
            <div class="flex">
              <span class="font-bold">{t("currency")}</span>:
              <img
                class="mx-2 rounded-sm"
                src={
                  row.currency === "THB"
                    ? "/images/country/th.webp"
                    : row.currency === "USD"
                    ? "/images/country/us.webp"
                    : "/images/country/kr.webp"
                }
                alt="Language Icon"
                width="30"
                height="10"
              />
              {row.currency}
            </div>
          </div>
          <div>
            <span class="font-bold">{t("betdate")}</span>:{" "}
            {moment(row.created_at)
              .tz("Asia/Bangkok")
              .format("DD/MM/YYYY HH:mm:ss")}
          </div>
        </div>
      );
    },
  },
  // {
  //   title: t("gameCode"),
  //   align: "center",
  //   key: "gameCode",
  // },

  {
    title: t("creditbefore"),
    align: "center",
    key: "before_balance",
    render: (row) => {
      return Commas(row.before_balance);
    },
  },
  {
    title: t("creditafter"),
    align: "center",
    key: "after_balance",
    render: (row) => {
      return Commas(row.after_balance);
    },
  },
  {
    title: t("amount"),
    align: "center",
    key: "betAmount",
    render: (row) => {
      return Commas(row.betAmount);
    },
  },
  {
    title: t("member"),
    title(column) {
      return (
        <div class="bg-[#2080f0]/50 rounded-lg text-white">{t("member")}</div>
      );
    },
    align: "center",
    key: "gameCode",
    children: [
      {
        title: t("winloss"),
        align: "center",
        key: "member_wl",
        render: (row, index) => {
          const style = {
            color:
              row.member_wl < 0
                ? "#d03050"
                : row.member_wl > 0
                ? "#18a058"
                : "",
          };
          return (
            <div class="mx-10">
              <p class="text-end" style={style}>
                {Commas(row.member_wl)}
              </p>
              <div class="flex justify-between">
                pt
                <p class="">100%</p>
              </div>
            </div>
          );
        },
      },

      // {
      //   title: t("total"),
      //   align: "center",
      //   key: "",
      //   render: (row, index) => {
      //     const style = {
      //       color: row.member_wl < 0 ? "#d03050" : row.member_wl >= 0 ? "#18a058" : "",
      //     };
      //     return (
      //       <div>
      //         <p class="text-end" style={style}>{row.member_wl}</p>
      //         <div class="flex justify-between">
      //           pt
      //           <p class="ml-5">100%</p>
      //         </div>
      //       </div>
      //     );
      //   },
      // },
    ],
  },
  {
    title: t("agent"),
    title(column) {
      return (
        <div class="bg-[#18a058]/50 rounded-lg text-white">
          {StapTab.value[StapTab.value.length - 2]?.name}
        </div>
      );
    },
    align: "center",
    key: "",
    children: [
      {
        title: t("winloss"),
        align: "center",
        key: "agent_wl",
        render: (row, index) => {
          const Agen = Commas(
            -(
              (row.member_wl * (row.ag_holdpercent + row.reseller_ourpercent)) /
              100
            )
          );
          const style = {
            color: Agen < 0 ? "#d03050" : Agen > 0 ? "#18a058" : "",
          };
          return (
            <div class="mx-10">
              <p class="text-end" style={style}>
                {Agen}
              </p>
              <div class="flex justify-between">
                pt
                <p class="">{row.ag_holdpercent + row.reseller_ourpercent}%</p>
              </div>
            </div>
          );
        },
      },
      // {
      //   title: t("comm"),
      //   align: "center",
      //   key: "",
      //   render: (row, index) => {
      //     return <div>0.00</div>;
      //   },
      // },
      // {
      //   title: t("total"),
      //   align: "center",
      //   key: "",
      //   render: (row, index) => {
      //     return (
      //       <div>
      //         <p class="text-end">-20%</p>
      //         <div class="flex justify-between">
      //           pt
      //           <p class="ml-5">100%</p>
      //         </div>
      //       </div>
      //     );
      //   },
      // },
    ],
  },
  {
    title: t("provider"),
    title(column) {
      return (
        <div class="bg-[#000000]/50 rounded-lg text-white">{t("provider")}</div>
      );
    },
    align: "center",
    key: "gameCode",
    children: [
      {
        title: t("winloss"),
        align: "center",
        key: "provider_wl",
        render: (row, index) => {
          const Pro = Commas(-((row.member_wl * row.ag_ourpercent) / 100));
          const style = {
            color: Pro < 0 ? "#d03050" : Pro > 0 ? "#18a058" : "",
          };
          return (
            <div class="mx-10">
              <p class="text-end" style={style}>
                {Pro}
              </p>
              <div class="flex justify-between">
                pt
                <p class="">{row.ag_ourpercent}%</p>
              </div>
            </div>
          );
        },
      },
      // {
      //   title: t("comm"),
      //   align: "center",
      //   key: "",
      //   render: (row, index) => {
      //     return <div>0.00</div>;
      //   },
      // },
      // {
      //   title: t("total"),
      //   align: "center",
      //   key: "",
      //   render: (row, index) => {
      //     return (
      //       <div>
      //         <p class="text-end">-20%</p>
      //         <div class="flex justify-between">
      //           pt
      //           <p class="ml-5">100%</p>
      //         </div>
      //       </div>
      //     );
      //   },
      // },
    ],
  },
  {
    title: t("result"),
    align: "center",
    key: "status_result",
    render: (row) => {
      return (
        <div>
          <n-button
            type={
              row.member_wl == 0
                ? "warning"
                : row.member_wl > 0
                ? "success"
                : "error"
            }
            size="small"
            class="cursor-default text-white"
          >
            {row.member_wl > 0
              ? t("win")
              : row.member_wl < 0
              ? t("lose")
              : t("draw")}
          </n-button>
        </div>
      );
    },
  },
  // {
  //   title: t("bonus"),
  //   align: "center",
  //   key: "Bonus",
  //   render: (row) => {
  //     return 0;
  //   },
  // },
  // {
  //   title: t("winloss"),
  //   align: "center",
  //   key: "member_wl",
  //   render: (row) => {
  //     return Commas(row.member_wl);
  //   },
  // },
  {
    title: t("detail"),
    align: "center",
    key: "action",
    render: (row: any) => {
      return (
        <div>
          <NIcon size={18} onClick={() => show_report(row)}>
            <icon-park-outline-preview-open class="icon-action cursor-pointer" />
          </NIcon>
          {UserData.value.support === 1 && (
            <NIcon
              class="ml-2"
              color="#ffd07e"
              size={18}
              onClick={() => send_report(row)}
            >
              <icon-park-outline-send class="icon-action cursor-pointer" />
            </NIcon>
          )}
        </div>
      );
    },
  },
]);
const createSummary = () => {
  const getColor = (value) =>
    value < 0 ? "#d03050" : value > 0 ? "#18a058" : "";

  return {
    loginName: {
      value: h(
        NButton,

        {
          style: {
            cursor: "default",
          },
          secondary: true,
          type: "default",
          size: "small",
          onClick: () => {
            console.log("Button clicked");
          },
        },
        () => t("sum")
      ),
    },
    information: {
      value: h(
        NButton,

        {
          style: {
            cursor: "default",
          },
          secondary: true,
          type: "default",
          size: "small",
          onClick: () => {
            console.log("Button clicked");
          },
        },
        () => t("sum")
      ),
    },

    noTicket: {
      value: h("span", Commas(totalData.value.total_noTicket)),
    },
    betAmount: {
      value: h("span", Commas(totalData.value.total_validTurn)),
    },
    payoutAmount: {
      value: h("span", Commas(totalData.value.total_payoutAmount)),
    },
    memberWL: {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_memberWL100) } },
        Commas(totalData.value.total_memberWL100)
      ),
    },
    "member-win-loss": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_memberWL) } },
        Commas(totalData.value.total_memberWL)
      ),
    },
    "member-comm": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_memberComm) } },
        Commas(totalData.value.total_memberComm)
      ),
    },
    "member-total": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_memberTotal) } },
        Commas(totalData.value.total_memberTotal)
      ),
    },
    "company-win-loss": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_companyWL) } },
        Commas(totalData.value.total_companyWL)
      ),
    },
    "company-comm": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_companyComm) } },
        Commas(totalData.value.total_companyComm)
      ),
    },
    "company-total": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_companyTotal) } },
        Commas(totalData.value.total_companyTotal)
      ),
    },
    "provider-win-loss": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_providerWL) } },
        Commas(totalData.value.total_providerWL)
      ),
    },
    "provider-comm": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_providerComm) } },
        Commas(totalData.value.total_providerComm)
      ),
    },
    "provider-total": {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_providerTotal) } },
        Commas(totalData.value.total_providerTotal)
      ),
    },
    member_wl: {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_member_wl) } },
        Commas(totalData.value.total_member_wl)
      ),
    },
    agent_wl: {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_agent_wl) } },
        Commas(totalData.value.total_agent_wl)
      ),
    },
    provider_wl: {
      value: h(
        "span",
        { style: { color: getColor(totalData.value.total_provider_wl) } },
        Commas(totalData.value.total_provider_wl)
      ),
    },
  };
};

const dialog = useDialog();
const message = useMessage();
async function send_report(row: any) {
  dialog.warning({
    title: "ยืนยันการส่งรายงาน",
    content: "คุณแน่ใจหรือไม่ว่าต้องการส่งรายงานนี้?",
    positiveText: "ยืนยัน",
    negativeText: "ยกเลิก",
    onPositiveClick: async () => {
      try {
        const resData = await http.post("v1/save/report", row);
        if (resData.data.status) {
          message.success(resData.data.mes || "ส่งรายงานสำเร็จ!");
        }
      } catch (error) {
        console.error(error);
        message.error("เกิดข้อผิดพลาดในการส่งรายงาน");
      }
    },
    onNegativeClick: () => {
      message.info("ยกเลิกการส่งรายงาน");
    },
  });
}
function show_report(row: any) {
  const game = gameData.find((g) => g.gameCode === row.gameCode);

  let apiUrl =
    "https%3A%2F%2Fapi.pgf-theks69.com%2Fweb-api%2Foperator-proxy%2Fv1%2FHistory%2FGetBetHistory";

  if (
    [
      1648578, 1727711, 1815268, 1747549, 1786529, 1372643, 53, 1850016,
      1804577, 83, 1312883, 1879752, 1827457, 1760238, 1881268, 1666445,
      1799745, 1702123, 1635221, 1755623, 1834850, 1935269,
    ].includes(game?.gameId)
  ) {
    apiUrl =
      "api.pgf-theks69.com%2Fweb-api%2Foperator-proxy%2Fv1%2FHistory%2FGetBetHistory";
  }

  const url = `https://public.pgf-theks69.com/history/${game.gameId}/${game.gameId}.html?sid=${row.txnId}&psid=${row.roundId}&t=${row.agent_id}&api=${apiUrl}&lang=th`;

  iframeUrl.value = url;
  showModal.value = true;
}

const Commas = (x: any) => {
  if (!x || x == "-0") {
    return 0;
  }
  if (x % 1 !== 0) {
    let roundedNumber = Math.round(x * 100) / 100;
    return roundedNumber.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
const list: any = ref([]);
onMounted(() => {
  getData();
  StapTab.value.push({
    name: UserData.value.username,
    position:
      UserData.value.position_type == 1 ||
      (UserData.value.position_type == 4 &&
        UserData.value.upline_position_type == 1)
        ? "Company"
        : UserData.value.position_type == 2 ||
          (UserData.value.position_type == 4 &&
            UserData.value.upline_position_type == 2)
        ? "Reseller"
        : "Agent",
  });
});
const handleUser = (item) => {
  Stap.value = 2;
  username.value = item._id;
  model.value.condition_1 = "";
  viewDetail(item.loginName, item._id, Stap.value);
  // getData()
};
const name = ref([]);
const positiontype = ref([]);
const positionshow = ref([]);
const handleEditTable = (item) => {
  Stap.value = item.position_type;
  agent_id.value = item._id;
  name.value = item.loginName;
  positiontype.value = item.position_type;
  if (item.position_type == 3) {
    agent_id.value = item.user_id;
  }
  if (item.position_type == 4) {
    agent_id.value = item.agent_id;
  }
  // // getData()
  model.value.condition_1 = "";
  Page.value = 1;
  viewDetail(item.loginName, item.agent_id, Stap.value);
};
const Stap = ref(0);
const agent_id = ref(null);
const username = ref(null);
const valueDate = ref(null);

const search = () => {
  Page.value = 1;
  Stap.value = 0;
  StapTab.value = [
    { name: StapTab.value[0].name, position: StapTab.value[0].position },
  ];
  getData();
};

function changePage(page: number, size: number) {
  perPage.value = size;
  Page.value = page;
  getData();
}
const StapTab = ref([]);
const selectedTab = ref("today");
const handleResetSearch = (row: Entity.User) => {
  model.value = { ...initialModel };
  FilterBet.value = null;
  FilterRound.value = null;
  selectedTab.value = "today";
  Page.value = 1;
  getData();
};
const perPage = ref(10);
const Page = ref(1);
const total = ref(1);
const FilterBet = ref(null);
const FilterRound = ref(null);
const totalData = ref({
  _id: null,
  total_noTicket: 0,
  total_validTurn: 0,
  total_payoutAmount: 0,
  total_memberWL100: 0,
  total_memberWL: 0,
  total_memberComm: 0,
  total_memberTotal: 0,
  total_companyWL: 0,
  total_companyComm: 0,
  total_companyTotal: 0,
  total_providerWL: 0,
  total_providerComm: 0,
  total_providerTotal: 0,
  total_agent_wl: 0,
  total_amount: 0,
  total_member_wl: 0,
  total_provider_wl: 0,
});

const getData = async () => {
  startLoading();
  try {
    const params = {
      perPage: perPage.value,
      page: Page.value,
      Stap: Stap.value,
      agent_id: agent_id.value,
      username: username.value,
      startDate: null,
      endDate: null,
      loginName: model.value.condition_1,
      gameCode: null,
      Round: null,
    };
    if (position_type.value == 2) {
      if (FilterBet.value) {
        params.gameCode = FilterBet.value;
      }
      if (FilterRound.value) {
        params.Round = FilterRound.value;
      }
    }
    if (model.value.condition_2) {
      params.startDate = moment(model.value.condition_2[0]).format(
        "YYYY-MM-DD"
      );
      params.endDate = moment(model.value.condition_2[1]).format("YYYY-MM-DD");
    }
    let res = await http.get("v1/Report/PayerWinLose", { params });

    position_type.value = res.data.position_type;
    // Page.value = Math.ceil(res.data.totalPages / perPage.value);
    // console.log(total.value)
    total.value = res.data.totalPages;
    // console.log(res.data.data);
    list.value = res.data.data;
    totalData.value = res.data.total;
  } catch (error) {
    console.log(error);
  } finally {
    endLoading();
  }
};

const searchDate = (dateFilter) => {
  if (dateFilter == "previous") {
    const Data1 = moment().startOf("day").subtract(1, "days").toDate();
    const Data2 = moment().startOf("day").subtract(1, "days").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "today") {
    const Data1 = moment().startOf("day").toDate();
    const Data2 = moment().startOf("day").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "yesterday") {
    const Data1 = moment().startOf("day").subtract(1, "days").toDate();
    const Data2 = moment().startOf("day").subtract(1, "days").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "this-week") {
    const Data1 = moment().startOf("week").toDate();
    const Data2 = moment().endOf("week").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "last-week") {
    const Data1 = moment().subtract(1, "weeks").startOf("week").toDate();
    const Data2 = moment().subtract(1, "weeks").endOf("week").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "this-month") {
    const Data1 = moment().startOf("month").toDate();
    const Data2 = moment().endOf("month").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "last-month") {
    const Data1 = moment().subtract(1, "months").startOf("month").toDate();
    const Data2 = moment().subtract(1, "months").endOf("month").toDate();
    model.value.condition_2 = [Data1, Data2];
  } else if (dateFilter == "next") {
    const Data1 = moment().startOf("day").add(1, "days").toDate();
    const Data2 = moment().startOf("day").add(1, "days").toDate();
    model.value.condition_2 = [Data1, Data2];
  }
  getData();
};
const downloadWl = () => {
  const params = {
    Stap: Stap.value,
    agent_id: agent_id.value,
    username: username.value,
    startDate: null,
    endDate: null,
    loginName: model.value.condition_1,
    gameCode: null,
    Round: null,
  };
  if (position_type.value == 2) {
    if (FilterBet.value) {
      params.gameCode = FilterBet.value;
    }
    if (FilterRound.value) {
      params.Round = FilterRound.value;
    }
  }
  if (model.value.condition_2) {
    params.startDate = moment(model.value.condition_2[0]).format("YYYY-MM-DD");
    params.endDate = moment(model.value.condition_2[1]).format("YYYY-MM-DD");
  }
  http
    .post("v1/download/report/wl", params, { responseType: "blob" })
    .then((response) => {
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "report";
      link.click();
      // this.showOver = false
    })
    .catch((error) => {
      console.log(error);
      // this.SwalError(error.response.data.message)
      // this.showOver = false
    });
};
const selectTab = async (index: any, item: any) => {
  if (index != StapTab.value.length - 1 && StapTab.value.length != 1) {
    if (index == 0) {
      Stap.value = 0;
      agent_id.value = null;
      username.value = null;
      name.value = item.name;
      StapTab.value = StapTab.value.slice(0, index + 1);
      Page.value = 1;
      getData();
    } else {
      const ag = StapTab.value[index];
      StapTab.value = StapTab.value.slice(0, index + 1);
      Stap.value = ag.position;
      agent_id.value = ag._id;
      name.value = item.name;
      Page.value = 1;
      getData();
    }
  }
};
const viewDetail = async (username: any, _id: any, position: any) => {
  StapTab.value.push({ name: username, _id: _id, position: position });
  getData();
};
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <n-form
          ref="formRef"
          :model="model"
          label-placement="left"
          class="float-right"
          :show-feedback="false"
          v-if="position_type !== 2"
        >
          <div class="flex flex-wrap sm:flex-row items-center gap-3">
            <div class="w-full sm:w-auto">
              <n-form-item :label="$t('player')" path="condition_1">
                <n-input
                  v-model:value="model.condition_1"
                  :placeholder="$t('player')"
                />
              </n-form-item>
            </div>
            <div class="w-full sm:w-auto">
              <n-form-item :label="$t('date')" path="condition_2">
                <n-config-provider
                  :locale="localeDate"
                  :date-locale="dateLocale"
                >
                  <n-date-picker
                    v-model:value="model.condition_2"
                    type="daterange"
                    format="dd-MM-yyyy"
                    clearable
                  />
                </n-config-provider>
              </n-form-item>
            </div>
            <div class="flex flex-wrap sm:flex-row items-center gap-3">
              <div class="sm: w-auto">
                <n-button type="primary" @click="search" class="w-full">
                  <template #icon>
                    <icon-park-outline-search />
                  </template>
                  {{ $t("search") }}
                </n-button>
              </div>
              <div class="sm: w-auto">
                <n-button secondary @click="handleResetSearch" class="w-full">
                  <template #icon>
                    <icon-park-outline-redo />
                  </template>
                  {{ $t("reset") }}
                </n-button>
              </div>
            </div>
          </div>
        </n-form>
        <n-form
          class="float-right"
          ref="formRef"
          :model="model"
          label-placement="left"
          :show-feedback="false"
          v-else
        >
          <n-flex>
            <n-form-item :label="$t('gameCode')">
              <n-input
                v-model:value="FilterBet"
                :placeholder="$t('gameCode')"
              />
            </n-form-item>
            <n-form-item :label="$t('roundid')">
              <n-input
                v-model:value="FilterRound"
                :placeholder="$t('roundid')"
              />
            </n-form-item>
            <n-form-item :label="$t('date')" path="condition_2">
              <n-config-provider :locale="localeDate" :date-locale="dateLocale">
                <n-date-picker
                  v-model:value="model.condition_2"
                  type="daterange"
                  format="dd-MM-yyyy"
                  clearable
                />
              </n-config-provider>
            </n-form-item>

            <n-flex>
              <n-button type="primary" @click="search">
                <template #icon>
                  <icon-park-outline-search />
                </template>
                {{ $t("search") }}
              </n-button>

              <n-button secondary @click="handleResetSearch">
                <template #icon>
                  <icon-park-outline-redo />
                </template>
                {{ $t("reset") }}
              </n-button>
            </n-flex>
          </n-flex>
        </n-form>
        <div>
          <n-tabs
            type="card"
            v-model:value="selectedTab"
            default-value="today"
            @update:value="searchDate"
            animated
          >
            <n-tab-pane name="previous" :tab="t('previous')"></n-tab-pane>
            <n-tab-pane name="today" :tab="t('today')"></n-tab-pane>
            <n-tab-pane name="yesterday" :tab="t('yesterday')"></n-tab-pane>
            <n-tab-pane name="this-week" :tab="t('thisweek')"></n-tab-pane>
            <n-tab-pane name="last-week" :tab="t('lastweek')"></n-tab-pane>
            <n-tab-pane name="this-month" :tab="t('thismonth')"></n-tab-pane>
            <n-tab-pane name="last-month" :tab="t('lastmonth')"></n-tab-pane>
            <n-tab-pane name="next" :tab="t('next')"></n-tab-pane>
          </n-tabs>
        </div>
        <div class="flex flex-wrap items-center gap-2">
          <div
            v-for="(item, index) in StapTab"
            :key="index"
            @click="selectTab(index, item)"
            class="flex items-center"
          >
            <n-button
              tertiary
              round
              :type="
                item.position === 'Company'
                  ? 'error'
                  : item.position === 1 || item.position === 'Reseller'
                  ? 'warning'
                  : item.position === 2 ||
                    item.position === 4 ||
                    item.position === 'Agent'
                  ? 'success'
                  : 'info'
              "
              class="inline-flex cursor-pointer"
              >{{ item.name }}</n-button
            >
            <span v-if="index + 1 !== StapTab.length" class="ml-2">
              <icon-park-outline-right-one />
            </span>
          </div>
        </div>
      </n-space>

      <div class="flex-center gap-4 my-3">
        <h4 class="font-bold text-[1rem]">{{ $t("reportall") }}</h4>
        <n-button type="primary" secondary class="ml-a" @click="downloadWl()">
          <template #icon>
            <icon-park-outline-download />
          </template>
          {{ $t("download") }}
        </n-button>
      </div>
      <n-data-table
        striped
        class="text-nowrap"
        :scroll-x="1200"
        :columns="position_type == 1 ? columns : columns2"
        :data="list"
        :loading="loading"
        :summary="createSummary"
      />
      <Pagination
        class="mt-5 overflow-auto float-right"
        :count="total"
        :page="Page"
        @change="changePage"
      />
    </n-card>

    <n-modal v-model:show="showModal" title="Bet History" mask-closable="false">
      <iframe
        :src="iframeUrl"
        style="width: 414px; height: 736px; border: none"
      />
    </n-modal>

    <n-alert v-if="showAlert" title="Info" type="info" closable>test</n-alert>
  </div>
</template>
