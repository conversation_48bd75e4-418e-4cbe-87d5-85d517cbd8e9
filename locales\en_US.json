{"orderIntentManagement": "Order Intent Management", "addNewOrder": "Add New Order", "totalOrders": "Total Orders", "textOrders": "Text Orders", "slipOrders": "Slip Orders", "totalUSDT": "Total USDT", "filters": "Filters", "source": "Source", "selectSource": "Select Source", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "searchByUsername": "Search by <PERSON><PERSON><PERSON>", "amountRange": "Amount Range", "minAmount": "<PERSON>", "maxAmount": "<PERSON>", "orderIntents": "Order Intents", "items": "Items", "export": "Export", "editOrder": "Edit Order", "direction": "Direction", "selectDirection": "Select Direction", "userInfo": "User Info", "firstName": "First Name", "lastName": "Last Name", "chatInfo": "Chat Info", "chatId": "Chat ID", "messageId": "Message ID", "rawText": "Raw Text", "enterRawText": "Enter Raw Text", "fiatTHB": "Fiat THB", "usdtAmount": "USDT Amount", "enterAmount": "Enter Amount", "slipDetails": "Slip <PERSON>", "bankName": "Bank Name", "enterBankName": "Enter Bank Name", "refNo": "Reference No", "enterRefNo": "Enter Reference No", "meslipCode": "Meslip Code", "enterMeslipCode": "Enter Meslip Code", "meslipDesc": "Meslip Description", "enterMeslipDesc": "Enter Meslip Description", "meslipDateText": "Meslip Date Text", "enterMeslipDateText": "Enter Me<PERSON>lip Date Text", "rateSource": "Rate Source", "enterRateSource": "Enter Rate Source", "rateFetchedAt": "Rate Fetched At", "selectDateTime": "Select Date Time", "create": "Create", "orderDetails": "Order Details", "orderId": "Order ID", "fullName": "Full Name", "createdAt": "Created At", "textSource": "Text Source", "slipSource": "Slip Source", "buy": "Buy", "pleaseSelectSource": "Please select source", "pleaseSelectDirection": "Please select direction", "pleaseEnterUsername": "Please enter username", "pleaseEnterChatId": "Please enter chat ID", "pleaseEnterAmount": "Please enter amount", "pleaseEnterRate": "Please enter rate", "rateSettingManagement": "Rate Setting Management", "rateSettingDescription": "Manage THB per USDT exchange rates", "addNewRate": "Add New Rate", "currentRate": "Current Rate", "thbPerUsdt": "THB per USDT", "lastUpdated": "Last Updated", "updatedBy": "Updated By", "addFirstRate": "Add First Rate", "rateHistory": "Rate History", "refresh": "Refresh", "editRate": "Edit Rate", "scope": "<PERSON><PERSON>", "rateTHBPerUSDT": "Rate THB per USDT", "updatedByName": "Updater Name", "updatedByUsername": "Updater Username", "selectScope": "Select Scope", "enterRate": "Enter Exchange Rate", "enterUpdaterName": "Enter Updater Name", "enterUsername": "<PERSON><PERSON> Username", "confirmDelete": "Confirm Delete", "deleteRateConfirmation": "Are you sure you want to delete this exchange rate?", "rate": "Rate", "updatedAt": "Updated At", "actions": "Actions", "scopeRequired": "Please select scope", "rateRequired": "Please enter exchange rate", "updaterNameRequired": "Please enter updater name", "failedToLoadHistory": "Failed to load history", "rateUpdatedSuccessfully": "Rate updated successfully", "rateAddedSuccessfully": "Rate added successfully", "operationFailed": "Operation failed", "rateDeletedSuccessfully": "Rate deleted successfully", "deleteFailed": "Delete failed", "passwordsetting": "Password Setting", "accountinfo": "Account Information", "navigation": "Navigation", "walletinformation": "Wallet Information", "our": "Our", "royaltysummary": "Royalty Summary", "walletsummary": "Wallet Summary", "accountsummary": "Account Summary", "accountinformation": "Account Information", "summary": "Summary", "availablebalance": "Available Balance", "secretKeyGeneratedSuccess": "Secret Key generated successfully", "secretKeyGenerationError": "Error occurred, unable to generate Secret Key", "saveDataSuccess": "Data saved successfully", "errorOccurred": "An error occurred!", "callbackurl": "Callback URL", "addedip": "Added IP", "addiphelp": "Add IP addresses to restrict access to your account.", "noipsadded": "No IPs added yet.", "clearall": "Clear All IPs", "currentips": "Current IPs", "ipAddressPlaceholder": "e.g. ***********", "account-statistics": "Account Statistics", "Current-Whitelist-IPs": "Current Whitelist IPs", "API-Configuration": "API Configuration", "hold": "Hold", "currencysupported": "Currency Supported", "configured": "Configured", "changepassword": "Change Password", "security-settings": "Security Settings", "quick-actions": "Quick Actions", "detail-winrate": "Detailed Win Rate Settings", "overallwinratecontrol": "Overall win rate control", "set-all": "Set All", "quickPresets": "Quick Presets", "conservative": "Conservative", "balanced": "Balanced", "aggressive": "Aggressive", "highRisk": "High Risk", "customAdjustment": "Custom Adjustment (%)", "overallWinRate": "Overall Win Rate", "enabled": "Enabled", "disabled": "Disabled", "paymentmanagement": "Payment", "agentlist": "Members & Agents", "commitsuccess": "Completed successfully", "valid": "Please fill in all information!", "validlv2tolv6": "Please enter LV2 - LV6 total 100", "balaceNot0": "The balance must be greater than 0.", "gotopage": "Go to page", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "sum": "Total", "suspend": "Suspend", "lastmonth": "Last Month", "thismonth": "This Month", "lastweek": "Last Week", "thisweek": "This Week", "yesterday": "Yesterday", "today": "Today", "result": "Result", "draw": "Draw", "win": "Win", "lose": "Lose", "information": "Information", "seamless1": "Please fill out the parameters on the left first, then run the tests!", "seamless2": " Please ensure all your data is correct before proceeding.", "copysuccess": "Copying successfully!", "detail": "Detail", "copyright": "Copyright", "security": "Security", "settinglv1": "LV.1 No Win = No rewards for bets, no free spin rewards", "settinglv2": "LV.2 Small Win = Bet rewards do not exceed the bet amount, free spin rewards do not exceed 1x the feature price", "settinglv3": "LV.3 Normal = Bet rewards range from more than 1x to 10x the bet amount, free spin rewards do not exceed 2x the feature price", "settinglv4": "LV.4 Big Win = Bet rewards range from more than 10x to 30x the bet amount, free spin rewards do not exceed 3x the feature price", "settinglv5": "LV.5 Mega Big Win = Bet rewards range from more than 30x to 40x the bet amount, free spin rewards do not exceed 4x the feature price", "settinglv6": "LV.6 Super Mega Big Win = Bet rewards exceed 40x the bet amount, free spin rewards do not exceed 5x the feature price", "credit": "Credit", "setting": "Setting", "settingDemo": "Demo Setting", "date": "Date", "betdate": "Bet Date", "gameCode": "Game Code", "betid": "Bet ID", "roundid": "Round ID", "creditbefore": "Credit Before", "creditafter": "Credit After", "bonus": "Bonus", "total": "Total", "comm": "<PERSON><PERSON>", "roundamount": "Round Amount", "betamount": "Bet Amount", "won": "Won", "winloss": "W/L", "winloss100": "W/L 100%", "reportall": "Report List", "updateplayer": "Update Player Detail", "provider": "Provider", "thai": "Thai", "english": "English", "lastupdate": "Published on Sep 1, 2024 - Update on Sep 16, 2024", "property": "Property", "type": "Type", "required": "Required", "description": "Description", "update": "Update", "apidocument": "API Document", "gamesdemo": "Demo", "testresult": "Test results", "validmatchpassword": "Passwords do not match", "validconfirmpassword": "Please input confirmpassword", "ourpercentage": "Our Percentage", "holdpercentage": "Hold Percentage", "amount": "Amount", "min": "Min", "max": "Max", "selectcurrency": "Select Currency", "walletcurrency": "<PERSON><PERSON>", "configureproductroyalty": "Configure Product Royalty", "managewalletcredit": "Manage Wallet Credit", "createagentinfomation": "Create Agent Information", "royaltysetting": "Royalty Settings", "walletsetup": "Wallet Setup", "accountsetup": "Account <PERSON><PERSON>", "positiontype": "Position Type", "next": "Next", "previous": "Previous", "finish": "Finish", "wallettype": "Wallet Type", "position": "Position", "member": "Member", "createdate": "Create Date", "all": "All", "permissions": "Permissions", "editpassword": "Edit Password", "validpassword": "Please input password", "validname": "Please input name", "validusername": "Please input username", "no.": "No.", "loginname": "Login Name", "name": "Name", "phone": "Phone", "management": "Management", "settingmanagement": "Setting Management", "report": "Report", "payment": "Payment", "delete": "Delete", "lastlogindate": "Last Login Date", "lastloginip": "Last Login IP", "subaccount": "Sub Account", "off": "Off", "view": "View", "unknow": "unknow", "on": "ON", "warning": "Warning", "info": "Info", "default": "<PERSON><PERSON><PERSON>", "error": "Error", "download": "Download", "page": "Page", "perpage": "Perpage", "manage": "Action", "id": "ID", "reset": "Reset", "player": "Player", "listplayer": "Player List", "product": "Product", "selectproduct": "Select Product", "selectall": "Select All", "search": "Search", "tos": "Terms of Service", "tosdetail": "Conditions: Please ensure that each testing account has a minimum balance of 5,000 THB (or equivalent in other currencies).", "ststs": "Select the specified test scenarios", "testreport": "Test Report", "runtest": "Run Test", "seamlesstesting": "Seamless Testing", "basicinfomation": "Basic Infomation", "username": "Username", "accounttype": "Account Type", "status": "Status", "active": "Active", "inactive": "Inactive", "mycurrency": "My Currency PT", "currency": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copy", "edit": "Edit", "save": "Save", "play": "Play", "cancel": "Cancel", "close": "Close", "confirm": "Confirm", "confirmsave": "Confirm Transaction", "confirmdelete": "Confirm Delete", "areyousure": "Are you sure?", "createtime": "Create Time", "lastlogin": "Last Login", "bettype": "Bet Type", "generateapikey": "GENERATE API KEY", "company": "Company", "reseller": "Reseller", "agent": "Agent", "add": "Add", "editmyprofile": "Edit My Profile", "editcallbackurl": "Edit Callback URL", "editwhitelistip": "Edit Whitelist IP", "password": "Password", "oldpassword": "Old Password", "newpassword": "New Password", "confirmpassword": "Confirm Password", "secretkey1": "This is the only time that the secret access keys can be viewed or downloaded. You cannot recover them later.", "secretkey2": "However, you can create new access keys at any time.", "doc": {"lg1": "Username in the system", "lg2": "Game type", "lg3": "Game ID", "lg4": "Boolean value to indicate whether the user logged in via mobile", "lg5": "Language code, default is TH", "lg6": "User ID", "lg7": "Status code", "lg8": "Notification message", "lg9": "Additional information such as sessionToken and url", "ggl1": "Game ID", "ggl2": "Game type", "ggl3": "Language code, default is TH", "kp1": "Username in the system", "gbr1": "Game code", "gbr2": "Type of game code", "gbr3": "Round ID", "gbr4": "Transaction number", "gbr5": "Various information", "gbr6": "Error status", "gb1": "User ID", "gb2": "Value used to store time data in milliseconds format", "gb3": "Game ID", "gb4": "Currency related to the transaction", "gb5": "Username", "gb6": "Information used for authentication and session tracking", "gb7": "User ID", "gb8": "Status code", "gb9": "Value used to store time data in milliseconds format", "gb10": "Game ID", "gb11": "<PERSON><PERSON><PERSON><PERSON>", "gb12": "Amount of money", "gb13": "Username", "sb1": "User ID", "sb2": "Value used to store time data in milliseconds format", "sb3": "Game ID", "sb4": "Currency related to the transaction", "sb5": "Username", "sb6": "Various transaction information", "sb7": "Information used for authentication and session tracking", "sb8": "Type of transaction", "sb9": "User ID", "sb10": "Status code", "sb11": "Value used to store time data in milliseconds format", "sb12": "Game ID", "sb13": "<PERSON><PERSON><PERSON><PERSON>", "sb14": "Amount of money", "sb15": "Username"}, "common": {"edit": "Edit", "cancel": "Cancel", "confirm": "Confirm", "close": "Closure", "reload": "Refresh", "choose": "<PERSON><PERSON>", "navigate": "Navigate", "inputPlaceholder": "please enter", "selectPlaceholder": "please choose"}, "app": {"notify": "Notification", "player": "Player", "report": "Report", "loginOut": "Login out", "loginOutContent": "Confirm to log out of current account?", "loginOutTitle": "Sign out", "userCenter": "Personal center", "light": "Light", "dark": "Dark", "system": "System", "backTop": "Back to top", "toggleSider": "Toggle sidebar", "BreadcrumbIcon": "Breadcrumbs icon", "blackAndWhite": "Black and white mode", "bottomCopyright": "Bottom copyright", "breadcrumb": "Bread crumbs", "colorWeak": "Color Weakness Mode", "interfaceDisplay": "Interface display", "logoDisplay": "LOGO display", "messages": "Messages", "multitab": "Display multiple tabs", "notifications": "Notify", "notificationsTips": "Notification", "pageTransition": "Page transition", "reset": "Reset", "resetSettingContent": "Confirm to reset all settings?", "resetSettingMeaasge": "Reset successful", "resetSettingTitle": "Reset settings", "searchPlaceholder": "Search page/path", "setting": "Setting", "systemSetting": "System Settings", "themeColor": "Theme color", "themeSetting": "Theme settings", "todos": "Todos", "toggleFullScreen": "Toggle full screen", "togglContentFullScreen": "Toggle content full screen", "topProgress": "Top progress", "transitionFadeBottom": "Bottom fade", "transitionFadeScale": "Scale fade", "transitionFadeSlide": "Side fade", "transitionNull": "No transition", "transitionSoft": "Soft", "transitionZoomFade": "Expand fade out", "transitionZoomOut": "Zoom out", "watermake": "Watermark", "closeOther": "Close other", "closeAll": "Close all", "closeLeft": "Close left", "closeRight": "Close right", "backHome": "Back to the homepage", "getRouteError": "Failed to obtain route, please try again later.", "layoutSetting": "Layout settings", "leftMenu": "Left menu", "topMenu": "Top menu", "mixMenu": "Mix menu"}, "login": {"signInTitle": "<PERSON><PERSON>", "accountRuleTip": "Please enter account", "passwordRuleTip": "Please enter password", "or": "Or", "rememberMe": "Remember me", "forgotPassword": "Forget the password?", "signIn": "Sign in", "signUp": "Sign up", "noAccountText": "Don't have an account?", "accountPlaceholder": "Enter account", "checkPasswordPlaceholder": "Please enter password again", "checkPasswordRuleTip": "Please confirm password again", "haveAccountText": "Do you have an account?", "passwordPlaceholder": "Enter password", "readAndAgree": "I have read and agree", "registerTitle": "Register", "userAgreement": "User Agreement", "resetPassword": "Reset password", "resetPasswordPlaceholder": "Enter account/mobile phone number", "resetPasswordRuleTip": "Please enter your account/mobile phone number", "resetPasswordTitle": "Reset", "loginsuccess": "Login successful!", "loginerror": "Invalid username or password!", "loginvalid": "Please fill in all information completely!"}, "route": {"appRoot": "Home", "cardList": "Card list", "draggableList": "Draggable list", "commonList": "Common list", "dashboard": "Dashboard", "demo": "DEMO", "fetch": "Request example", "list": "List", "monitor": "Monitoring", "monitor-baccarat": "Monitor Baccarat", "multi": "Multi-level menu", "multi2": "Multi-level menu subpage", "multi2Detail": "Details page of multi-level menu", "multi3": "multi-level menu", "multi4": "Multi-level menu 3-1", "workbench": "Dashboard alert", "management": "Management", "add-sub-account": "Add Sub Account", "sub-account": "Sub Account", "QRCode": "QR code", "about": "About", "clipboard": "Clipboard", "demo403": "403", "demo404": "404", "demo500": "500", "dictionarySetting": "Dictionary settings", "documents": "Document", "documentsVite": "Vite", "documentsVue": "<PERSON><PERSON>", "documentsVueuse": "VueUse (external link)", "documentsNova": "Nova docs", "echarts": "Echarts", "editor": "Editor", "editorMd": "MarkDown editor", "editorRich": "Rich text editor", "error": "Exception page", "icons": "Icon", "justSuper": "Supervisible", "map": "Map", "menuSetting": "<PERSON><PERSON>", "permission": "Permissions", "permissionDemo": "Permissions example", "setting": "System Settings", "userCenter": "Personal Center", "accountSetting": "User settings", "cascader": "Administrative region selection", "dict": "Dictionary example", "report": "Report", "player": "Player", "report-list": "Report List", "player-list": "Player list", "add-agent": "Add Agent", "agent-list": "Members & Agents", "my-profile": "My Profile", "seamlesstesting": "Seamless Testing", "apidocument": "API Document", "payment": "Payment", "crypto-baccarat": "Crypto Baccarat", "crypto-baccarat-player-list": "Player List", "crypto-baccarat-report": "Report", "crypto-baccarat-setting": "Setting", "demo-baccarat": "<PERSON><PERSON>", "player-baccarat": "Player <PERSON><PERSON><PERSON>", "report-baccarat": "Report Baccarat", "setting-baccarat": "Setting Baccarat"}, "http": {"400": "Syntax error in the request", "401": "User unauthorized", "403": "Server refused access", "404": "Requested resource does not exist", "405": "Request method not allowed", "408": "Network request timed out", "500": "Internal server error", "501": "Server not implemented the requested functionality", "502": "Bad gateway", "503": "Service unavailable", "504": "Gateway timeout", "505": "HTTP version not supported for this request", "defaultTip": "Request error"}, "components": {"iconSelector": {"inputPlaceholder": "Select target icon", "searchPlaceholder": "Search icon", "clearIcon": "Clear icon", "selectorTitle": "Icon selection"}, "copyText": {"message": "<PERSON><PERSON>d successfully", "tooltip": "Copy", "unsupportedError": "Your browser does not support Clipboard API", "unpermittedError": "Crrently not permitted to use Clipboard API"}}, "baccarat": {"settings": {"title": "Betting Settings", "confirmReset": "Confirm Reset", "resetWarning": "Are you sure you want to reset all settings to default values?", "confirmSave": "Confirm Save", "saveWarning": "Are you sure you want to save these settings?", "reset": "Settings have been reset successfully", "saved": "Settings have been saved successfully", "saveError": "An error occurred while saving settings"}, "player": {"side": "Player Side", "player": "Player", "high": "Player High (≥10)", "low": "Player Low (<10)", "odd": "Player Odd", "even": "Player Even", "zero": "Player Zero", "double": "Player Double"}, "tie": {"side": "<PERSON>ie <PERSON>", "tie": "Tie", "zero": "<PERSON><PERSON>"}, "banker": {"side": "Banker Side", "banker": "Banker", "high": "Banker High (≥10)", "low": "<PERSON><PERSON> (<10)", "odd": "Banker <PERSON>", "even": "Banker <PERSON>", "zero": "Banker <PERSON>", "double": "Banker <PERSON>"}}, "validation": {"passwordRequired": "Please fill in all password fields", "passwordMismatch": "Passwords do not match", "oldPasswordRequired": "Please input old password", "newPasswordRequired": "Please input new password", "confirmPasswordRequired": "Please input password", "whitelistIPRequired": "Please input Whitelist IP", "callbackURLRequired": "Please input Callback URL"}}