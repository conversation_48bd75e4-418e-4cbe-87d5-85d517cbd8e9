import{C as z,A as d,ft as h,d as $,q as T,V as v,z as c,U as f,fA as a,W as R,fX as W}from"./index-pY9FjpQW.js";const B=z("h",`
 font-size: var(--n-font-size);
 font-weight: var(--n-font-weight);
 margin: var(--n-margin);
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
`,[d("&:first-child",{marginTop:0}),h("prefix-bar",{position:"relative",paddingLeft:"var(--n-prefix-width)"},[h("align-text",{paddingLeft:0},[d("&::before",{left:"calc(-1 * var(--n-prefix-width))"})]),d("&::before",`
 content: "";
 width: var(--n-bar-width);
 border-radius: calc(var(--n-bar-width) / 2);
 transition: background-color .3s var(--n-bezier);
 left: 0;
 top: 0;
 bottom: 0;
 position: absolute;
 `),d("&::before",{backgroundColor:"var(--n-bar-color)"})])]),H=Object.assign(Object.assign({},c.props),{type:{type:String,default:"default"},prefix:String,alignText:Boolean}),l=n=>$({name:`H${n}`,props:H,setup(e){const{mergedClsPrefixRef:i,inlineThemeDisabled:s}=v(e),t=c("Typography","-h",B,W,e,i),o=f(()=>{const{type:g}=e,{common:{cubicBezierEaseInOut:u},self:{headerFontWeight:b,headerTextColor:m,[a("headerPrefixWidth",n)]:p,[a("headerFontSize",n)]:x,[a("headerMargin",n)]:C,[a("headerBarWidth",n)]:y,[a("headerBarColor",g)]:w}}=t.value;return{"--n-bezier":u,"--n-font-size":x,"--n-margin":C,"--n-bar-color":w,"--n-bar-width":y,"--n-font-weight":b,"--n-text-color":m,"--n-prefix-width":p}}),r=s?R(`h${n}`,f(()=>e.type[0]),o,e):void 0;return{mergedClsPrefix:i,cssVars:s?void 0:o,themeClass:r?.themeClass,onRender:r?.onRender}},render(){var e;const{prefix:i,alignText:s,mergedClsPrefix:t,cssVars:o,$slots:r}=this;return(e=this.onRender)===null||e===void 0||e.call(this),T(`h${n}`,{class:[`${t}-h`,`${t}-h${n}`,this.themeClass,{[`${t}-h--prefix-bar`]:i,[`${t}-h--align-text`]:s}],style:o},r)}}),S=l("2"),V=l("3");export{V as N,S as a};
