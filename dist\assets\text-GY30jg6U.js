import{C as v,ft as i,d as $,q as l,V as z,z as u,U as c,fA as B,W as T,fW as _,fX as R}from"./index-pY9FjpQW.js";const S=v("text",`
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
`,[i("strong",`
 font-weight: var(--n-font-weight-strong);
 `),i("italic",{fontStyle:"italic"}),i("underline",{textDecoration:"underline"}),i("code",`
 line-height: 1.4;
 display: inline-block;
 font-family: var(--n-font-famliy-mono);
 transition: 
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 box-sizing: border-box;
 padding: .05em .35em 0 .35em;
 border-radius: var(--n-code-border-radius);
 font-size: .9em;
 color: var(--n-code-text-color);
 background-color: var(--n-code-color);
 border: var(--n-code-border);
 `)]),V=Object.assign(Object.assign({},u.props),{code:Boolean,type:{type:String,default:"default"},delete:Boolean,strong:Boolean,italic:Boolean,underline:Boolean,depth:[String,Number],tag:String,as:{type:String,validator:()=>!0,default:void 0}}),k=$({name:"Text",props:V,setup(e){const{mergedClsPrefixRef:r,inlineThemeDisabled:o}=z(e),n=u("Typography","-text",S,R,e,r),s=c(()=>{const{depth:d,type:a}=e,f=a==="default"?d===void 0?"textColor":`textColor${d}Depth`:B("textColor",a),{common:{fontWeightStrong:h,fontFamilyMono:g,cubicBezierEaseInOut:m},self:{codeTextColor:x,codeBorderRadius:b,codeColor:y,codeBorder:C,[f]:p}}=n.value;return{"--n-bezier":m,"--n-text-color":p,"--n-font-weight-strong":h,"--n-font-famliy-mono":g,"--n-code-border-radius":b,"--n-code-text-color":x,"--n-code-color":y,"--n-code-border":C}}),t=o?T("text",c(()=>`${e.type[0]}${e.depth||""}`),s,e):void 0;return{mergedClsPrefix:r,compitableTag:_(e,["as","tag"]),cssVars:o?void 0:s,themeClass:t?.themeClass,onRender:t?.onRender}},render(){var e,r,o;const{mergedClsPrefix:n}=this;(e=this.onRender)===null||e===void 0||e.call(this);const s=[`${n}-text`,this.themeClass,{[`${n}-text--code`]:this.code,[`${n}-text--delete`]:this.delete,[`${n}-text--strong`]:this.strong,[`${n}-text--italic`]:this.italic,[`${n}-text--underline`]:this.underline}],t=(o=(r=this.$slots).default)===null||o===void 0?void 0:o.call(r);return this.code?l("code",{class:s,style:this.cssVars},this.delete?l("del",null,t):t):this.delete?l("del",{class:s,style:this.cssVars},t):l(this.compitableTag||"span",{class:s,style:this.cssVars},t)}});export{k as _};
