import{f as C,a as M}from"./FormItem-B4P0FvHJ.js";import{C as I,ft as S,A as O,d as P,q as k,V as B,z as _,r as L,hi as R,hj as x,a6 as v}from"./index-pY9FjpQW.js";const V=I("form",[S("inline",`
 width: 100%;
 display: inline-flex;
 align-items: flex-start;
 align-content: space-around;
 `,[I("form-item",{width:"auto",marginRight:"18px"},[O("&:last-child",{marginRight:0})])])]);var F=function(t,u,i,o){function h(a){return a instanceof i?a:new i(function(f){f(a)})}return new(i||(i=Promise))(function(a,f){function y(n){try{e(o.next(n))}catch(l){f(l)}}function r(n){try{e(o.throw(n))}catch(l){f(l)}}function e(n){n.done?a(n.value):h(n.value).then(y,r)}e((o=o.apply(t,u||[])).next())})};const q=Object.assign(Object.assign({},_.props),{inline:Boolean,labelWidth:[Number,String],labelAlign:String,labelPlacement:{type:String,default:"top"},model:{type:Object,default:()=>{}},rules:Object,disabled:Boolean,size:String,showRequireMark:{type:Boolean,default:void 0},requireMarkPlacement:String,showFeedback:{type:Boolean,default:!0},onSubmit:{type:Function,default:t=>{t.preventDefault()}},showLabel:{type:Boolean,default:void 0},validateMessages:Object}),$=P({name:"Form",props:q,setup(t){const{mergedClsPrefixRef:u}=B(t);_("Form","-form",V,R,t,u);const i={},o=L(void 0),h=r=>{const e=o.value;(e===void 0||r>=e)&&(o.value=r)};function a(r){return F(this,arguments,void 0,function*(e,n=()=>!0){return yield new Promise((l,j)=>{const w=[];for(const m of x(i)){const g=i[m];for(const s of g)s.path&&w.push(s.internalValidate(null,n))}Promise.all(w).then(m=>{const g=m.some(d=>!d.valid),s=[],c=[];m.forEach(d=>{var p,b;!((p=d.errors)===null||p===void 0)&&p.length&&s.push(d.errors),!((b=d.warnings)===null||b===void 0)&&b.length&&c.push(d.warnings)}),e&&e(s.length?s:void 0,{warnings:c.length?c:void 0}),g?j(s.length?s:void 0):l({warnings:c.length?c:void 0})})})})}function f(){for(const r of x(i)){const e=i[r];for(const n of e)n.restoreValidation()}}return v(C,{props:t,maxChildLabelWidthRef:o,deriveMaxChildLabelWidth:h}),v(M,{formItems:i}),Object.assign({validate:a,restoreValidation:f},{mergedClsPrefix:u})},render(){const{mergedClsPrefix:t}=this;return k("form",{class:[`${t}-form`,this.inline&&`${t}-form--inline`],onSubmit:this.onSubmit},this.$slots)}});export{$ as _};
