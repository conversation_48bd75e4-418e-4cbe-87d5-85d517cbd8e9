<script lang="ts" setup>
import moment from "moment";
import type { MenuInst, MenuOption } from 'naive-ui'
import { useAppStore, useRouteStore } from '@/store'
import type { DrawerPlacement } from "naive-ui";
import http from "@/service/axios";
import {
  BackTop,
  CollapaseButton,
  FullScreen,
  Logo,
  Notices,
  Search,
  Setting,
  TabBar,
  Menu,
  UserCenter,
} from './components'
const { t } = useI18n();
const routeStore = useRouteStore()
const appStore = useAppStore()
const pageRoute = useRoute()
const router = useRouter()
const route = useRoute();
watch(
  () => route.path,
  () => {
    menu.value = false;
  }
);
const menuInstRef = ref<MenuInst | null>(null)

watch(
  () => pageRoute.path,
  () => {
    menuInstRef.value?.showOption(routeStore.activeMenu as string)
  },
  { immediate: true },
)

const topMenu = ref<MenuOption[]>([])
const activeTopMenu = ref<string>('')
function handleTopMenu(rowMenu: MenuOption[]) {
  topMenu.value = rowMenu.map((i) => {
    const { icon, label, key } = i
    return {
      icon,
      label,
      key,
    }
  })
}

onMounted(() => {
  handleTopMenu(routeStore.menus)

  // 根据当前页面获取选中菜单和对应侧边菜单
  const currentMenuKey = pageRoute.matched[1].path
  handleSideMenu(currentMenuKey)
  activeTopMenu.value = currentMenuKey
})

const sideMenu = ref<MenuOption[]>([])
function handleSideMenu(key: string) {
  const routeMenu = routeStore.menus as MenuOption[]
  const targetMenu = routeMenu.find(i => i.key === key)
  if (targetMenu) {
    sideMenu.value = targetMenu.children ? targetMenu.children : [targetMenu]
  }
}

function updateTopMenu(key: string) {
  handleSideMenu(key)
  activeTopMenu.value = key
  router.push(key)
}
const menu = ref(false)
const placement = ref<DrawerPlacement>("right");
const activate = (place: DrawerPlacement) => {
  menu.value = true;
  placement.value = place;
};
const checkScreenSize = () => {
  if (window.innerWidth > 768) {
    menu.value = false;
  }
};
const currentTime = ref("");
onMounted(() => {
  Credit();
  GetProfile();
  // checkScreenSize();
  window.addEventListener("resize", checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkScreenSize);
});
const profile = ref([]);
const GetProfile = async () => {
  const { data: res } = await http.get("v1/Profile/agent");
  profile.value = res.data;
};
const loading = ref(false)
const recredit = () => {
  Credit();
}
const credit_thb = ref(0);
const credit_krw = ref(0);
const credit_usd = ref(0);
const Credit = async () => {
  loading.value= true
  const { data: res } = await http.get("v1/wallet/credit");
  credit_thb.value = res.credit_thb;
  credit_krw.value = res.credit_krw;
  credit_usd.value = res.credit_usd;
  loading.value= false
};
const Commas = (x: any) => {
  if (!x || x == "-0") {
    return 0;
  }
  if (x % 1 !== 0) {
    let roundedNumber = Math.round(x * 100) / 100;
    return roundedNumber.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
</script>

<template>
  <n-layout
    has-sider
    class="wh-full "
    embedded
  >
    <n-layout-sider
      v-if="!appStore.contentFullScreen"
      class="hidden sm:block"
      bordered
      :collapsed="appStore.collapsed"
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      content-style="display: flex;flex-direction: column;min-height:100%;"
    >
      <Logo v-if="appStore.showLogo" />
      <n-scrollbar class="flex-1 ">
        <n-menu
          ref="menuInstRef"
          :collapsed="appStore.collapsed"
          :indent="20"
          :collapsed-width="64"
          :options="sideMenu"
          :value="routeStore.activeMenu"
        />
      </n-scrollbar>
    </n-layout-sider>
    <n-layout
      class="h-full flex flex-col "
      content-style="display: flex;flex-direction: column;min-height:100%;"
      embedded
      :native-scrollbar="false"
    >
      <n-layout-header bordered position="absolute" class="z-999 hidden sm:block">
        <div v-if="!appStore.contentFullScreen" class="h-60px flex-y-center justify-between">
          <CollapaseButton />
          <n-menu
            ref="menuInstRef"
            mode="horizontal"
            responsive
            :options="topMenu"
            :value="activeTopMenu"
            @update:value="updateTopMenu"
          />
          <div class="flex-y-center gap-1 h-full p-x-xl ">
            <div class="hidden lg:flex justify-center items-center gap-2 text-nowrap mr-10">
              <n-button
                v-if="profile.reseller_thb === 1 || profile.ag_currency === 'THB' "
                round
                size="small"
                class=""
                :loading="loading"
                @click="recredit"
              >
                <img
                  class="mr-2"
                  src="/images/country/th.webp"
                  alt="Language Icon"
                  width="20"
                  height="20"
                />{{ Commas(credit_thb) }} ฿</n-button
              >
              <n-button
                v-if="profile.reseller_krw === 1 || profile.ag_currency === 'KRW'"

                round
                size="small"
                class=""
                  :loading="loading"
                @click="recredit"
              >
                <img
                  class="mr-2"
                  src="/images/country/kr.webp"
                  alt="Language Icon"
                  width="20"
                  height="20"
                />{{ Commas(credit_krw) }} ₩</n-button
              >
              <n-button
                v-if="profile.reseller_usd === 1 || profile.ag_currency === 'USD' "
                round
                size="small"
                class=""
                  :loading="loading"
                @click="recredit"
              >
                <img
                  class="mr-2"
                  src="/images/country/us.webp"
                  alt="Language Icon"
                  width="20"
                  height="20"
                />{{ Commas(credit_usd) }} $</n-button
              >
            </div>
            <!-- <Search />
            <Notices /> -->
            <FullScreen />
            <DarkModeSwitch />
            <Setting />
            <LangsSwitch />
            <div v-if="profile.username">
              <n-button
                tertiary
                :type="profile.position_type === 1 ? 'error' : profile.position_type === 2 ?'warning' :profile.position_type === 3 ? 'success' :'info'"
                round
                size="tiny"
                class="cursor-default"
              >
                {{ profile.username }}
              </n-button>
              <p class="text-xs text-end mr-2">
                {{
                  profile.position_type === 1
                    ? t("company")
                    : profile.position_type === 2
                    ? t("reseller")
                    : profile.position_type === 3
                    ? t("agent")
                    : ""
                }}
              </p>
              
            </div>
          
        <div class="flex items-center">
          <UserCenter />
        </div>
          </div>
        </div>
        <TabBar v-if="appStore.showTabs" class="h-45px" />
      </n-layout-header>

      <n-layout-header
        bordered
        position="absolute"
        class="z-999 block sm:hidden"
      >
        <div
          v-if="!appStore.contentFullScreen"
          class="h-60px flex-y-center justify-between"
        >
          <div class="flex-y-center h-full">
            <n-button
            secondary
              type="primary"
              size="small"
              @click="activate('left')"
              class="ml-4"
            >
              <icon-park-outline-application-menu />
            </n-button>
            <n-drawer
              v-model:show="menu"
               :width="appStore.collapsed === true ? '30%' : '70%'"
              :placement="placement"
              :class="
                appStore.storeColorMode === 'light'
                  ? 'bg-[#ffffff]'
                  : 'bg-[#18181c]'
              "
            >
              <n-drawer-content>
                <div class="text-xl cursor-pointer gap-2">
                  <Logo v-if="appStore.showLogo" />
                  <n-scrollbar>
                    <Menu />
                  </n-scrollbar>
                </div>
              </n-drawer-content>
            </n-drawer>
            <!-- <CollapaseButton /> -->
            <!-- <Breadcrumb /> -->
          </div>
          <div class="flex-y-center gap-1 h-full p-x-xs">
            <!-- <Search /> -->
           
            <!-- <FullScreen /> -->
            <Currency />
            <DarkModeSwitch />
            <!-- <Notices /> -->
            <!-- <Setting /> -->
            <LangsSwitch />
           
            <UserCenter />
          </div>
        </div>
      </n-layout-header>
      <div
        class="flex-1 p-16px flex flex-col"
        :class="{
          'p-t-121px': appStore.showTabs,
          'p-b-56px': appStore.showFooter && !appStore.contentFullScreen,
          'p-t-76px': !appStore.showTabs,
          'p-t-61px': appStore.contentFullScreen,
        }"
      >
        <router-view v-slot="{ Component, route }" class="flex-1">
          <transition
            :name="appStore.transitionAnimation"
            mode="out-in"
          >
            <keep-alive :include="routeStore.cacheRoutes">
              <component
                :is="Component"
                v-if="appStore.loadFlag"
                :key="route.fullPath"
              />
            </keep-alive>
          </transition>
        </router-view>
      </div>
      <n-layout-footer
        v-if="appStore.showFooter && !appStore.contentFullScreen"
        bordered
        position="absolute"
        class="h-40px flex-center"
      >
        <!-- {{ appStore.footerText }} -->
        {{$t('copyright')}} {{ appStore.footerText }}
      </n-layout-footer>
      <BackTop />
    </n-layout>
  </n-layout>
</template>
