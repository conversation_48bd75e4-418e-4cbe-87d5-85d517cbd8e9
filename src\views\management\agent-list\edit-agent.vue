<script setup lang="tsx">
// @ts-nocheck
import http from "@/service/axios";
const router = useRouter();
const message = useMessage();
const route = useRoute();
const agentId = route.params.id;
const current = ref<number>(1);
const currentStatus = ref<string>("process");
const selectedCurrencies = ref<string[]>([]);
const walletAmounts = ref<Record<string, number>>({});
const agent = ref(false);
const positionType = ref("RESELLER");
const typeAgent = ref("SEAMLESS");
const currencyOptions = ref([
  { label: "THB", value: "THB", total: "69,096,900" },
  { label: "KRW", value: "KRW", total: "958,998,000" },
  { label: "USD", value: "USD", total: "999,892,000" },
]);
const statusItem = computed(() => [
  {
    value: 1,
    label: t("active"),
  },
  {
    value: 3,
    label: t("inactive"),
  },
  {
    value: 2,
    label: t("suspend"),
  },
]);
const formRef = ref();
const formModel = ref({
  username: "",
  password: "",
  confirmPassword: "",
  seamlessInput: "",
});
const isMobile = computed(() => screenWidth.value <= 768);
const screenWidth = ref(window.innerWidth);
function updateScreenWidth() {
  screenWidth.value = window.innerWidth;
}
onMounted(() => {
  GetAgent();
  GetCredit();
  window.addEventListener("resize", updateScreenWidth);
});
onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
});
const hold_percent = ref(0);
const hold_percent_krw = ref(0);
const hold_percent_usd = ref(0);
const hold_percentage = ref(0);
const our_percentage = ref(0);
const hold_percentage_krw = ref(0);
const our_percentage_krw = ref(0);
const hold_percentage_usd = ref(0);
const our_percentage_usd = ref(0);
const statusLogin = ref(null);
const GetAgent = async () => {
  const params = {
    id: agentId,
  };
  const { data: res } = await http.get("v1/Account/Getuser", { params });
  formModel.value.username = res.username;
  statusLogin.value = res.status;
  if (res.position_type == 3) {
    selectedCurrencies.value.push(res.ag_currency);
  } else {
    if (res.reseller_thb) {
      selectedCurrencies.value.push("THB");
    }
    if (res.reseller_krw) {
      selectedCurrencies.value.push("KRW");
    }
    if (res.reseller_usd) {
      selectedCurrencies.value.push("USD");
    }
  }
  hold_percent.value = res.hold_percent + res.our_percent;
  hold_percent_krw.value = res.hold_percent_krw + res.our_percent_krw;
  hold_percent_usd.value = res.hold_percent_usd + res.our_percent_usd;

  hold_percentage.value = res.hold_percent;
  hold_percentage_krw.value = res.hold_percent_krw;
  hold_percentage_usd.value = res.hold_percent_usd;

  our_percentage.value = res.our_percent;
  our_percentage_krw.value = res.our_percent_krw;
  our_percentage_usd.value = res.our_percent_usd;
  agent.value = false;
  if (res.position_type == 2) {
    agent.value = false;
    positionType.value = "RESELLER";
  } else {
    agent.value = true;
    positionType.value = "AGENT";
    if (res.bet_type == 1) {
      typeAgent.value = "SEAMLESS";
      formModel.value.seamlessInput = res.callback_url;
    } else {
      typeAgent.value = "TRANSFER";
    }
  }
};
const GetCredit = async () => {
  const params = {
    id: agentId,
  };

  const { data: res } = await http.get("v1/wallet/agent/credit", { params });
  walletAmounts.value.THB = res.credit_thb.toLocaleString();
  walletAmounts.value.KRW = res.credit_krw.toLocaleString();
  walletAmounts.value.USD = res.credit_usd.toLocaleString();
};
const genPtSelect = (max_pt: any) => {
  try {
    const result = [];
    for (let i = 0; i <= max_pt; i += 0.5) {
      result.push({
        state: `${i.toFixed(2)}%`,
        abbr: i,
      });
    }
    return result;
  } catch (e) {
    console.log(e);
    return [];
  }
};
const calPtOur = (pt_type: string, currency: string) => {
  if (currency == "THB") {
    if (pt_type == "our") {
      hold_percentage.value = hold_percent.value - our_percentage.value;
    } else {
      our_percentage.value = hold_percent.value - hold_percentage.value;
    }
  } else if (currency == "KRW") {
    if (pt_type == "our") {
      hold_percentage_krw.value =
        hold_percent_krw.value - our_percentage_krw.value;
    } else {
      our_percentage_krw.value =
        hold_percent_krw.value - hold_percentage_krw.value;
    }
  } else if (currency == "USD") {
    if (pt_type == "our") {
      hold_percentage_usd.value =
        hold_percent_usd.value - our_percentage_usd.value;
    } else {
      our_percentage_usd.value =
        hold_percent_usd.value - hold_percentage_usd.value;
    }
  }
};
const { t } = useI18n();
const rules = computed(() => ({
  // username: [
  //   { required: true, message: 'Please enter username', trigger: 'blur' },
  // ],
  // password: [
  //   { required: true, message: 'Please enter password', trigger: 'blur' },
  // ],
  confirmPassword: [
    {
      required: false,
      message: t("validconfirmpassword"),
      trigger: "blur",
    },
    {
      validator: (rule, value) => value === formModel.value.password,
      message: t("validmatchpassword"),
      trigger: "blur",
    },
  ],
}));

async function next() {
  if (current.value === 1) {
    const valid = await formRef.value?.validate();
    if (!valid) return;
  }
  if (current.value < 3) current.value++;
}

function prev() {
  if (current.value > 1) current.value--;
}

const FinishData = () => {
  router.push("/management/agent-list");
};
function save() {
  if (
    (current.value == 1 &&
      formModel.value.password &&
      !formModel.value.confirmPassword) ||
    (!formModel.value.password && formModel.value.confirmPassword)
  ) {
    return message.error("กรุณากรอกข้อมูลให้ครบถ้วน");
  }
  const obj = {
    agent_id: agentId,
    username: formModel.value.username,
    password: null,
    currency: selectedCurrencies.value,
    hold_percentage: selectedCurrencies.value.includes("THB")
      ? hold_percentage.value
      : 0,
    our_percentage: selectedCurrencies.value.includes("THB")
      ? our_percentage.value
      : 0,
    hold_percentage_krw: selectedCurrencies.value.includes("KRW")
      ? hold_percentage_krw.value
      : 0,
    our_percentage_krw: selectedCurrencies.value.includes("KRW")
      ? our_percentage_krw.value
      : 0,
    hold_percentage_usd: selectedCurrencies.value.includes("USD")
      ? hold_percentage_usd.value
      : 0,
    our_percentage_usd: selectedCurrencies.value.includes("USD")
      ? our_percentage_usd.value
      : 0,
  };
  if (current.value == 1) {
    obj.status = statusLogin.value;
    if (formModel.value.password && formModel.value.confirmPassword) {
      obj.password = formModel.value.password;
    }
  }
  http.post("v1/Account/update/agent", obj).then((response) => {
    if (response.data.status) {
      message.success(response.data.mes);
      router.push("/management/agent-list");
    } else {
      message.error(response.data.mes);
    }
  });
}
</script>

<template>
  <div>
    <div class="max-w-7xl mx-auto">
      <!-- Progress Steps -->
      <n-card class="mb-4">
        <n-steps
          :current="current"
          :status="currentStatus"
          :vertical="isMobile"
          class="px-4"
        >
          <n-step>
            <template #title>
              <div class="flex items-center gap-2">
                <icon-park-outline-user class="text-lg" />
                <span>{{ $t("accountinfo") }}</span>
              </div>
            </template>
            <template #description>
              {{ $t("viewagentinfomation") }}
            </template>
          </n-step>

          <n-step>
            <template #title>
              <div class="flex items-center gap-2">
                <icon-park-outline-lock class="text-lg" />
                <span>{{ $t("passwordsetting") }}</span>
              </div>
            </template>
            <template #description>
              {{ $t("changepassword") }}
            </template>
          </n-step>

          <n-step>
            <template #title>
              <div class="flex items-center gap-2">
                <icon-park-outline-setting class="text-lg" />
                <span>{{ $t("royaltysetting") }}</span>
              </div>
            </template>
            <template #description>
              {{ $t("configureproductroyalty") }}
            </template>
          </n-step>
        </n-steps>
      </n-card>

      <!-- Step Content -->
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
        <!-- Main Content -->
        <div class="lg:col-span-8">
          <!-- Step 1: Account Information (Read Only) -->
          <n-card v-if="current === 1" :title="$t('accountsetup')">
            <template #header-extra>
              <icon-park-outline-user class="text-xl text-blue-500" />
            </template>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Account Information -->
              <div>
                <h3
                  class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300"
                >
                  {{ $t("accountinformation") }}
                </h3>
                <div>
                  <n-form-item :label="$t('username')">
                    <n-input :value="formModel.username" disabled size="large">
                      <template #prefix>
                        <icon-park-outline-user />
                      </template>
                    </n-input>
                  </n-form-item>

                  <n-form-item :label="$t('status')">
                    <n-button-group size="large">
                      <n-button
                        :type="statusLogin === 1 ? 'success' : 'default'"
                        @click="statusLogin = 1"
                      >
                        {{ $t("active") }}
                      </n-button>
                      <n-button
                        :type="statusLogin === 2 ? 'warning' : 'default'"
                        @click="statusLogin = 2"
                      >
                        {{ $t("suspend") }}
                      </n-button>
                      <n-button
                        :type="statusLogin === 3 ? 'error' : 'default'"
                        @click="statusLogin = 3"
                      >
                        {{ $t("inactive") }}
                      </n-button>
                    </n-button-group>
                  </n-form-item>
                </div>
              </div>

              <!-- Position Type (Read Only) -->
              <div>
                <h3
                  class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300"
                >
                  {{ $t("positiontype") }}
                </h3>

                <div class="space-y-4">
                  <n-card
                    :class="[
                      'transition-all duration-200',
                      positionType === 'RESELLER'
                        ? 'ring-2 ring-orange-400 bg-orange-50 dark:bg-orange-900/20'
                        : 'opacity-50',
                    ]"
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-3">
                        <n-avatar color="#f59e0b" size="medium">
                          <icon-park-outline-crown />
                        </n-avatar>
                        <div>
                          <h4 class="font-medium">{{ $t("reseller") }}</h4>
                        </div>
                      </div>
                      <n-radio
                        :checked="positionType === 'RESELLER'"
                        disabled
                      />
                    </div>
                  </n-card>

                  <n-card
                    :class="[
                      'transition-all duration-200',
                      positionType === 'AGENT'
                        ? 'ring-2 ring-green-400 bg-green-50 dark:bg-green-900/20'
                        : 'opacity-50',
                    ]"
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-3">
                        <n-avatar color="#10b981" size="medium">
                          <icon-park-outline-people />
                        </n-avatar>
                        <div>
                          <h4 class="font-medium">{{ $t("agent") }}</h4>
                        </div>
                      </div>
                      <n-radio :checked="positionType === 'AGENT'" disabled />
                    </div>
                  </n-card>

                  <!-- Agent Type (Read Only) -->
                  <div
                    v-if="agent"
                    class="mt-4 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
                  >
                    <h4 class="font-medium mb-3">{{ $t("bettype") }}</h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <n-button
                        :type="typeAgent === 'SEAMLESS' ? 'error' : 'default'"
                        size="large"
                        block
                        disabled
                      >
                        <template #icon>
                          <icon-park-outline-api />
                        </template>
                        Seamless
                      </n-button>
                      <n-button
                        :type="typeAgent === 'TRANSFER' ? 'info' : 'default'"
                        size="large"
                        block
                        disabled
                      >
                        <template #icon>
                          <icon-park-outline-transfer />
                        </template>
                        Transfer
                      </n-button>
                    </div>

                    <div v-if="typeAgent === 'SEAMLESS'" class="mt-4">
                      <n-form-item label="Callback API URL">
                        <n-input
                          :value="formModel.seamlessInput"
                          disabled
                          size="large"
                        >
                          <template #prefix>
                            <icon-park-outline-link />
                          </template>
                        </n-input>
                      </n-form-item>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </n-card>

          <!-- Step 2: Password Setting -->
          <n-card v-if="current === 2" :title="$t('passwordsetting')">
            <template #header-extra>
              <icon-park-outline-lock class="text-xl text-orange-500" />
            </template>

            <div class="max-w-md mx-auto">
              <n-form ref="formRef" :model="formModel" :rules="rules" vertical>
                <n-form-item :label="$t('newpassword')" path="password">
                  <n-input
                    v-model:value="formModel.password"
                    type="password"
                    :placeholder="$t('newpassword')"
                    size="large"
                    show-password-on="click"
                  >
                    <template #prefix>
                      <icon-park-outline-lock />
                    </template>
                  </n-input>
                </n-form-item>

                <n-form-item
                  :label="$t('confirmpassword')"
                  path="confirmPassword"
                >
                  <n-input
                    v-model:value="formModel.confirmPassword"
                    type="password"
                    :placeholder="$t('confirmpassword')"
                    size="large"
                    show-password-on="click"
                  >
                    <template #prefix>
                      <icon-park-outline-lock />
                    </template>
                  </n-input>
                </n-form-item>
              </n-form>
            </div>
          </n-card>

          <!-- Step 3: Royalty Setting -->
          <n-card v-if="current === 3" :title="$t('royaltysetting')">
            <template #header-extra>
              <icon-park-outline-setting class="text-xl text-purple-500" />
            </template>

            <div class="space-y-4">
              <n-card v-for="currency in selectedCurrencies" :key="currency">
                <div class="flex items-center gap-3 mb-4">
                  <img
                    :src="`/images/country/${
                      currency === 'THB'
                        ? 'th'
                        : currency === 'KRW'
                        ? 'kr'
                        : 'us'
                    }.webp`"
                    :alt="currency"
                    class="w-8 rounded"
                  />
                  <h3 class="text-xl font-semibold">
                    {{ currency }}
                  </h3>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Hold Percentage -->
                  <div>
                    <n-form-item :label="`${$t('holdpercentage')} (%)`">
                      <n-select
                        v-if="currency === 'THB'"
                        v-model:value="hold_percentage"
                        :options="
                          genPtSelect(hold_percent).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('hold', 'THB')"
                      />
                      <n-select
                        v-else-if="currency === 'KRW'"
                        v-model:value="hold_percentage_krw"
                        :options="
                          genPtSelect(hold_percent_krw).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('hold', 'KRW')"
                      />
                      <n-select
                        v-else-if="currency === 'USD'"
                        v-model:value="hold_percentage_usd"
                        :options="
                          genPtSelect(hold_percent_usd).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('hold', 'USD')"
                      />
                    </n-form-item>
                  </div>

                  <!-- Our Percentage -->
                  <div>
                    <n-form-item :label="`${$t('ourpercentage')} (%)`">
                      <n-select
                        v-if="currency === 'THB'"
                        v-model:value="our_percentage"
                        :options="
                          genPtSelect(hold_percent).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('our', 'THB')"
                      />
                      <n-select
                        v-else-if="currency === 'KRW'"
                        v-model:value="our_percentage_krw"
                        :options="
                          genPtSelect(hold_percent_krw).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('our', 'KRW')"
                      />
                      <n-select
                        v-else-if="currency === 'USD'"
                        v-model:value="our_percentage_usd"
                        :options="
                          genPtSelect(hold_percent_usd).map((item) => ({
                            label: item.state,
                            value: item.abbr,
                          }))
                        "
                        size="large"
                        @update:value="calPtOur('our', 'USD')"
                      />
                    </n-form-item>
                  </div>
                </div>
              </n-card>
            </div>
          </n-card>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-4">
          <!-- Navigation Card -->
          <n-card :title="$t('navigation')" class="mb-4">
            <template #header-extra>
              <icon-park-outline-navigation class="text-xl" />
            </template>

            <div class="space-y-3">
              <n-button
                :type="current === 1 ? 'primary' : 'default'"
                block
                secondary
                @click="current = 1"
              >
                <template #icon>
                  <icon-park-outline-user />
                </template>
                {{ $t("accountinfo") }}
              </n-button>

              <n-button
                :type="current === 2 ? 'primary' : 'default'"
                block
                secondary
                @click="current = 2"
              >
                <template #icon>
                  <icon-park-outline-lock />
                </template>
                {{ $t("passwordsetting") }}
              </n-button>

              <n-button
                :type="current === 3 ? 'primary' : 'default'"
                block
                secondary
                @click="current = 3"
              >
                <template #icon>
                  <icon-park-outline-setting />
                </template>
                {{ $t("royaltysetting") }}
              </n-button>
            </div>
          </n-card>

          <!-- Wallet Information -->
          <n-card :title="$t('walletinformation')" class="mb-4">
            <template #header-extra>
              <icon-park-outline-wallet class="text-xl text-green-500" />
            </template>

            <div class="space-y-3">
              <div
                v-for="currency in selectedCurrencies"
                :key="currency"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg"
              >
                <div class="flex items-center gap-2">
                  <img
                    :src="`/images/country/${
                      currency === 'THB'
                        ? 'th'
                        : currency === 'KRW'
                        ? 'kr'
                        : 'us'
                    }.webp`"
                    :alt="currency"
                    class="w-6 rounded"
                  />
                  <span class="font-medium">{{ currency }}</span>
                </div>
                <span class="font-bold">
                  {{ walletAmounts[currency] || "0" }}
                  {{
                    currency === "THB" ? "฿" : currency === "KRW" ? "₩" : "$"
                  }}
                </span>
              </div>
            </div>
          </n-card>

          <!-- Action Buttons -->
          <n-card>
            <div class="space-y-3">
              <n-button
                type="primary"
                block
                size="large"
                @click="save"
                :loading="false"
              >
                <template #icon>
                  <icon-park-outline-check />
                </template>
                {{ $t("save") }}
              </n-button>

              <n-button block size="large" @click="FinishData">
                <template #icon>
                  <icon-park-outline-back />
                </template>
                {{ $t("back") }}
              </n-button>
            </div>
          </n-card>
        </div>
      </div>

      <!-- Bottom Navigation (Mobile) -->
      <div
        v-if="isMobile"
        class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t p-4 z-50"
      >
        <div class="flex justify-between gap-3">
          <n-button
            :disabled="current === 1"
            @click="prev"
            size="large"
            class="flex-1"
          >
            <template #icon>
              <icon-park-outline-left />
            </template>
            {{ $t("previous") }}
          </n-button>

          <n-button
            v-if="current < 3"
            type="primary"
            @click="next"
            size="large"
            class="flex-1"
          >
            {{ $t("next") }}
            <template #icon>
              <icon-park-outline-right />
            </template>
          </n-button>

          <n-button
            v-else
            type="success"
            @click="save"
            size="large"
            class="flex-1"
          >
            <template #icon>
              <icon-park-outline-check />
            </template>
            {{ $t("save") }}
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.n-card {
  @apply shadow-sm;
}

.n-steps {
  @apply bg-transparent;
}

@media (max-width: 768px) {
  body {
    @apply pb-20;
  }
}
</style>
