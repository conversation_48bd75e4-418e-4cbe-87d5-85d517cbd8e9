import{x as We,ho as Qe,A as V,C as a,fr as qe,fs as Ge,ft as b,fu as N,E as he,hp as Je,d as Ze,q as f,gO as eo,gP as oo,a1 as no,gQ as to,gT as ee,T as ao,V as ro,z as ge,r as y,gU as io,U as w,K as lo,gs as so,O as fe,gt as Z,R as co,W as ve,X as uo,ga as O,P as U,Q as _}from"./index-pY9FjpQW.js";function ho(t){const i="rgba(0, 0, 0, .85)",k="0 2px 8px 0 rgba(0, 0, 0, 0.12)",{railColor:v,primaryColor:s,baseColor:d,cardColor:S,modalColor:R,popoverColor:K,borderRadius:L,fontSize:M,opacityDisabled:B}=t;return Object.assign(Object.assign({},Qe),{fontSize:M,markFontSize:M,railColor:v,railColorHover:v,fillColor:s,fillColorHover:s,opacityDisabled:B,handleColor:"#FFF",dotColor:S,dotColorModal:R,dotColorPopover:K,handleBoxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.3), inset 0 0 1px 0 rgba(0, 0, 0, 0.05)",handleBoxShadowHover:"0 1px 4px 0 rgba(0, 0, 0, 0.3), inset 0 0 1px 0 rgba(0, 0, 0, 0.05)",handleBoxShadowActive:"0 1px 4px 0 rgba(0, 0, 0, 0.3), inset 0 0 1px 0 rgba(0, 0, 0, 0.05)",handleBoxShadowFocus:"0 1px 4px 0 rgba(0, 0, 0, 0.3), inset 0 0 1px 0 rgba(0, 0, 0, 0.05)",indicatorColor:i,indicatorBoxShadow:k,indicatorTextColor:d,indicatorBorderRadius:L,dotBorder:`2px solid ${v}`,dotBorderActive:`2px solid ${s}`,dotBoxShadow:""})}const fo={common:We,self:ho},vo=V([a("slider",`
 display: block;
 padding: calc((var(--n-handle-size) - var(--n-rail-height)) / 2) 0;
 position: relative;
 z-index: 0;
 width: 100%;
 cursor: pointer;
 user-select: none;
 -webkit-user-select: none;
 `,[b("reverse",[a("slider-handles",[a("slider-handle-wrapper",`
 transform: translate(50%, -50%);
 `)]),a("slider-dots",[a("slider-dot",`
 transform: translateX(50%, -50%);
 `)]),b("vertical",[a("slider-handles",[a("slider-handle-wrapper",`
 transform: translate(-50%, -50%);
 `)]),a("slider-marks",[a("slider-mark",`
 transform: translateY(calc(-50% + var(--n-dot-height) / 2));
 `)]),a("slider-dots",[a("slider-dot",`
 transform: translateX(-50%) translateY(0);
 `)])])]),b("vertical",`
 box-sizing: content-box;
 padding: 0 calc((var(--n-handle-size) - var(--n-rail-height)) / 2);
 width: var(--n-rail-width-vertical);
 height: 100%;
 `,[a("slider-handles",`
 top: calc(var(--n-handle-size) / 2);
 right: 0;
 bottom: calc(var(--n-handle-size) / 2);
 left: 0;
 `,[a("slider-handle-wrapper",`
 top: unset;
 left: 50%;
 transform: translate(-50%, 50%);
 `)]),a("slider-rail",`
 height: 100%;
 `,[N("fill",`
 top: unset;
 right: 0;
 bottom: unset;
 left: 0;
 `)]),b("with-mark",`
 width: var(--n-rail-width-vertical);
 margin: 0 32px 0 8px;
 `),a("slider-marks",`
 top: calc(var(--n-handle-size) / 2);
 right: unset;
 bottom: calc(var(--n-handle-size) / 2);
 left: 22px;
 font-size: var(--n-mark-font-size);
 `,[a("slider-mark",`
 transform: translateY(50%);
 white-space: nowrap;
 `)]),a("slider-dots",`
 top: calc(var(--n-handle-size) / 2);
 right: unset;
 bottom: calc(var(--n-handle-size) / 2);
 left: 50%;
 `,[a("slider-dot",`
 transform: translateX(-50%) translateY(50%);
 `)])]),b("disabled",`
 cursor: not-allowed;
 opacity: var(--n-opacity-disabled);
 `,[a("slider-handle",`
 cursor: not-allowed;
 `)]),b("with-mark",`
 width: 100%;
 margin: 8px 0 32px 0;
 `),V("&:hover",[a("slider-rail",{backgroundColor:"var(--n-rail-color-hover)"},[N("fill",{backgroundColor:"var(--n-fill-color-hover)"})]),a("slider-handle",{boxShadow:"var(--n-handle-box-shadow-hover)"})]),b("active",[a("slider-rail",{backgroundColor:"var(--n-rail-color-hover)"},[N("fill",{backgroundColor:"var(--n-fill-color-hover)"})]),a("slider-handle",{boxShadow:"var(--n-handle-box-shadow-hover)"})]),a("slider-marks",`
 position: absolute;
 top: 18px;
 left: calc(var(--n-handle-size) / 2);
 right: calc(var(--n-handle-size) / 2);
 `,[a("slider-mark",`
 position: absolute;
 transform: translateX(-50%);
 white-space: nowrap;
 `)]),a("slider-rail",`
 width: 100%;
 position: relative;
 height: var(--n-rail-height);
 background-color: var(--n-rail-color);
 transition: background-color .3s var(--n-bezier);
 border-radius: calc(var(--n-rail-height) / 2);
 `,[N("fill",`
 position: absolute;
 top: 0;
 bottom: 0;
 border-radius: calc(var(--n-rail-height) / 2);
 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-fill-color);
 `)]),a("slider-handles",`
 position: absolute;
 top: 0;
 right: calc(var(--n-handle-size) / 2);
 bottom: 0;
 left: calc(var(--n-handle-size) / 2);
 `,[a("slider-handle-wrapper",`
 outline: none;
 position: absolute;
 top: 50%;
 transform: translate(-50%, -50%);
 cursor: pointer;
 display: flex;
 `,[a("slider-handle",`
 height: var(--n-handle-size);
 width: var(--n-handle-size);
 border-radius: 50%;
 overflow: hidden;
 transition: box-shadow .2s var(--n-bezier), background-color .3s var(--n-bezier);
 background-color: var(--n-handle-color);
 box-shadow: var(--n-handle-box-shadow);
 `,[V("&:hover",`
 box-shadow: var(--n-handle-box-shadow-hover);
 `)]),V("&:focus",[a("slider-handle",`
 box-shadow: var(--n-handle-box-shadow-focus);
 `,[V("&:hover",`
 box-shadow: var(--n-handle-box-shadow-active);
 `)])])])]),a("slider-dots",`
 position: absolute;
 top: 50%;
 left: calc(var(--n-handle-size) / 2);
 right: calc(var(--n-handle-size) / 2);
 `,[b("transition-disabled",[a("slider-dot","transition: none;")]),a("slider-dot",`
 transition:
 border-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 position: absolute;
 transform: translate(-50%, -50%);
 height: var(--n-dot-height);
 width: var(--n-dot-width);
 border-radius: var(--n-dot-border-radius);
 overflow: hidden;
 box-sizing: border-box;
 border: var(--n-dot-border);
 background-color: var(--n-dot-color);
 `,[b("active","border: var(--n-dot-border-active);")])])]),a("slider-handle-indicator",`
 font-size: var(--n-font-size);
 padding: 6px 10px;
 border-radius: var(--n-indicator-border-radius);
 color: var(--n-indicator-text-color);
 background-color: var(--n-indicator-color);
 box-shadow: var(--n-indicator-box-shadow);
 `,[he()]),a("slider-handle-indicator",`
 font-size: var(--n-font-size);
 padding: 6px 10px;
 border-radius: var(--n-indicator-border-radius);
 color: var(--n-indicator-text-color);
 background-color: var(--n-indicator-color);
 box-shadow: var(--n-indicator-box-shadow);
 `,[b("top",`
 margin-bottom: 12px;
 `),b("right",`
 margin-left: 12px;
 `),b("bottom",`
 margin-top: 12px;
 `),b("left",`
 margin-right: 12px;
 `),he()]),qe(a("slider",[a("slider-dot","background-color: var(--n-dot-color-modal);")])),Ge(a("slider",[a("slider-dot","background-color: var(--n-dot-color-popover);")]))]);function be(t){return window.TouchEvent&&t instanceof window.TouchEvent}function me(){const t=new Map,i=k=>v=>{t.set(k,v)};return Je(()=>{t.clear()}),[t,i]}const bo=0,mo=Object.assign(Object.assign({},ge.props),{to:ee.propTo,defaultValue:{type:[Number,Array],default:0},marks:Object,disabled:{type:Boolean,default:void 0},formatTooltip:Function,keyboard:{type:Boolean,default:!0},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:[Number,String],default:1},range:Boolean,value:[Number,Array],placement:String,showTooltip:{type:Boolean,default:void 0},tooltip:{type:Boolean,default:!0},vertical:Boolean,reverse:Boolean,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onDragstart:[Function],onDragend:[Function]}),po=Ze({name:"Slider",props:mo,slots:Object,setup(t){const{mergedClsPrefixRef:i,namespaceRef:k,inlineThemeDisabled:v}=ro(t),s=ge("Slider","-slider",vo,fo,t,i),d=y(null),[S,R]=me(),[K,L]=me(),M=y(new Set),B=io(t),{mergedDisabledRef:$}=B,oe=w(()=>{const{step:e}=t;if(Number(e)<=0||e==="mark")return 0;const o=e.toString();let n=0;return o.includes(".")&&(n=o.length-o.indexOf(".")-1),n}),X=y(t.defaultValue),pe=lo(t,"value"),Y=so(pe,X),m=w(()=>{const{value:e}=Y;return(t.range?e:[e]).map(se)}),ne=w(()=>m.value.length>2),we=w(()=>t.placement===void 0?t.vertical?"right":"top":t.placement),te=w(()=>{const{marks:e}=t;return e?Object.keys(e).map(Number.parseFloat):null}),g=y(-1),ae=y(-1),C=y(-1),z=y(!1),F=y(!1),W=w(()=>{const{vertical:e,reverse:o}=t;return e?o?"top":"bottom":o?"right":"left"}),xe=w(()=>{if(ne.value)return;const e=m.value,o=H(t.range?Math.min(...e):t.min),n=H(t.range?Math.max(...e):e[0]),{value:r}=W;return t.vertical?{[r]:`${o}%`,height:`${n-o}%`}:{[r]:`${o}%`,width:`${n-o}%`}}),ye=w(()=>{const e=[],{marks:o}=t;if(o){const n=m.value.slice();n.sort((h,u)=>h-u);const{value:r}=W,{value:l}=ne,{range:c}=t,p=l?()=>!1:h=>c?h>=n[0]&&h<=n[n.length-1]:h<=n[0];for(const h of Object.keys(o)){const u=Number(h);e.push({active:p(u),key:u,label:o[h],style:{[r]:`${H(u)}%`}})}}return e});function ke(e,o){const n=H(e),{value:r}=W;return{[r]:`${n}%`,zIndex:o===g.value?1:0}}function re(e){return t.showTooltip||C.value===e||g.value===e&&z.value}function Re(e){return z.value?!(g.value===e&&ae.value===e):!0}function Ce(e){var o;~e&&(g.value=e,(o=S.get(e))===null||o===void 0||o.focus())}function ze(){K.forEach((e,o)=>{re(o)&&e.syncPosition()})}function ie(e){const{"onUpdate:value":o,onUpdateValue:n}=t,{nTriggerFormInput:r,nTriggerFormChange:l}=B;n&&O(n,e),o&&O(o,e),X.value=e,r(),l()}function le(e){const{range:o}=t;if(o){if(Array.isArray(e)){const{value:n}=m;e.join()!==n.join()&&ie(e)}}else Array.isArray(e)||m.value[0]!==e&&ie(e)}function Q(e,o){if(t.range){const n=m.value.slice();n.splice(o,1,e),le(n)}else le(e)}function q(e,o,n){const r=n!==void 0;n||(n=e-o>0?1:-1);const l=te.value||[],{step:c}=t;if(c==="mark"){const u=I(e,l.concat(o),r?n:void 0);return u?u.value:o}if(c<=0)return o;const{value:p}=oe;let h;if(r){const u=Number((o/c).toFixed(p)),x=Math.floor(u),G=u>x?x:x-1,J=u<x?x:x+1;h=I(o,[Number((G*c).toFixed(p)),Number((J*c).toFixed(p)),...l],n)}else{const u=Te(e);h=I(e,[...l,u])}return h?se(h.value):o}function se(e){return Math.min(t.max,Math.max(t.min,e))}function H(e){const{max:o,min:n}=t;return(e-n)/(o-n)*100}function Se(e){const{max:o,min:n}=t;return n+(o-n)*e}function Te(e){const{step:o,min:n}=t;if(Number(o)<=0||o==="mark")return e;const r=Math.round((e-n)/o)*o+n;return Number(r.toFixed(oe.value))}function I(e,o=te.value,n){if(!o?.length)return null;let r=null,l=-1;for(;++l<o.length;){const c=o[l]-e,p=Math.abs(c);(n===void 0||c*n>0)&&(r===null||p<r.distance)&&(r={index:l,distance:p,value:o[l]})}return r}function de(e){const o=d.value;if(!o)return;const n=be(e)?e.touches[0]:e,r=o.getBoundingClientRect();let l;return t.vertical?l=(r.bottom-n.clientY)/r.height:l=(n.clientX-r.left)/r.width,t.reverse&&(l=1-l),Se(l)}function De(e){if($.value||!t.keyboard)return;const{vertical:o,reverse:n}=t;switch(e.key){case"ArrowUp":e.preventDefault(),A(o&&n?-1:1);break;case"ArrowRight":e.preventDefault(),A(!o&&n?-1:1);break;case"ArrowDown":e.preventDefault(),A(o&&n?1:-1);break;case"ArrowLeft":e.preventDefault(),A(!o&&n?1:-1);break}}function A(e){const o=g.value;if(o===-1)return;const{step:n}=t,r=m.value[o],l=Number(n)<=0||n==="mark"?r:r+n*e;Q(q(l,r,e>0?1:-1),o)}function Ve(e){var o,n;if($.value||!be(e)&&e.button!==bo)return;const r=de(e);if(r===void 0)return;const l=m.value.slice(),c=t.range?(n=(o=I(r,l))===null||o===void 0?void 0:o.index)!==null&&n!==void 0?n:-1:0;c!==-1&&(e.preventDefault(),Ce(c),Me(),Q(q(r,m.value[c]),c))}function Me(){z.value||(z.value=!0,t.onDragstart&&O(t.onDragstart),U("touchend",document,j),U("mouseup",document,j),U("touchmove",document,P),U("mousemove",document,P))}function E(){z.value&&(z.value=!1,t.onDragend&&O(t.onDragend),_("touchend",document,j),_("mouseup",document,j),_("touchmove",document,P),_("mousemove",document,P))}function P(e){const{value:o}=g;if(!z.value||o===-1){E();return}const n=de(e);n!==void 0&&Q(q(n,m.value[o]),o)}function j(){E()}function Be(e){g.value=e,$.value||(C.value=e)}function $e(e){g.value===e&&(g.value=-1,E()),C.value===e&&(C.value=-1)}function Fe(e){C.value=e}function He(e){C.value===e&&(C.value=-1)}fe(g,(e,o)=>void Z(()=>ae.value=o)),fe(Y,()=>{if(t.marks){if(F.value)return;F.value=!0,Z(()=>{F.value=!1})}Z(ze)}),co(()=>{E()});const ce=w(()=>{const{self:{markFontSize:e,railColor:o,railColorHover:n,fillColor:r,fillColorHover:l,handleColor:c,opacityDisabled:p,dotColor:h,dotColorModal:u,handleBoxShadow:x,handleBoxShadowHover:G,handleBoxShadowActive:J,handleBoxShadowFocus:Ie,dotBorder:Ae,dotBoxShadow:Ee,railHeight:Pe,railWidthVertical:je,handleSize:Ne,dotHeight:Oe,dotWidth:Ue,dotBorderRadius:_e,fontSize:Ke,dotBorderActive:Le,dotColorPopover:Xe},common:{cubicBezierEaseInOut:Ye}}=s.value;return{"--n-bezier":Ye,"--n-dot-border":Ae,"--n-dot-border-active":Le,"--n-dot-border-radius":_e,"--n-dot-box-shadow":Ee,"--n-dot-color":h,"--n-dot-color-modal":u,"--n-dot-color-popover":Xe,"--n-dot-height":Oe,"--n-dot-width":Ue,"--n-fill-color":r,"--n-fill-color-hover":l,"--n-font-size":Ke,"--n-handle-box-shadow":x,"--n-handle-box-shadow-active":J,"--n-handle-box-shadow-focus":Ie,"--n-handle-box-shadow-hover":G,"--n-handle-color":c,"--n-handle-size":Ne,"--n-opacity-disabled":p,"--n-rail-color":o,"--n-rail-color-hover":n,"--n-rail-height":Pe,"--n-rail-width-vertical":je,"--n-mark-font-size":e}}),T=v?ve("slider",void 0,ce,t):void 0,ue=w(()=>{const{self:{fontSize:e,indicatorColor:o,indicatorBoxShadow:n,indicatorTextColor:r,indicatorBorderRadius:l}}=s.value;return{"--n-font-size":e,"--n-indicator-border-radius":l,"--n-indicator-box-shadow":n,"--n-indicator-color":o,"--n-indicator-text-color":r}}),D=v?ve("slider-indicator",void 0,ue,t):void 0;return{mergedClsPrefix:i,namespace:k,uncontrolledValue:X,mergedValue:Y,mergedDisabled:$,mergedPlacement:we,isMounted:uo(),adjustedTo:ee(t),dotTransitionDisabled:F,markInfos:ye,isShowTooltip:re,shouldKeepTooltipTransition:Re,handleRailRef:d,setHandleRefs:R,setFollowerRefs:L,fillStyle:xe,getHandleStyle:ke,activeIndex:g,arrifiedValues:m,followerEnabledIndexSet:M,handleRailMouseDown:Ve,handleHandleFocus:Be,handleHandleBlur:$e,handleHandleMouseEnter:Fe,handleHandleMouseLeave:He,handleRailKeyDown:De,indicatorCssVars:v?void 0:ue,indicatorThemeClass:D?.themeClass,indicatorOnRender:D?.onRender,cssVars:v?void 0:ce,themeClass:T?.themeClass,onRender:T?.onRender}},render(){var t;const{mergedClsPrefix:i,themeClass:k,formatTooltip:v}=this;return(t=this.onRender)===null||t===void 0||t.call(this),f("div",{class:[`${i}-slider`,k,{[`${i}-slider--disabled`]:this.mergedDisabled,[`${i}-slider--active`]:this.activeIndex!==-1,[`${i}-slider--with-mark`]:this.marks,[`${i}-slider--vertical`]:this.vertical,[`${i}-slider--reverse`]:this.reverse}],style:this.cssVars,onKeydown:this.handleRailKeyDown,onMousedown:this.handleRailMouseDown,onTouchstart:this.handleRailMouseDown},f("div",{class:`${i}-slider-rail`},f("div",{class:`${i}-slider-rail__fill`,style:this.fillStyle}),this.marks?f("div",{class:[`${i}-slider-dots`,this.dotTransitionDisabled&&`${i}-slider-dots--transition-disabled`]},this.markInfos.map(s=>f("div",{key:s.key,class:[`${i}-slider-dot`,{[`${i}-slider-dot--active`]:s.active}],style:s.style}))):null,f("div",{ref:"handleRailRef",class:`${i}-slider-handles`},this.arrifiedValues.map((s,d)=>{const S=this.isShowTooltip(d);return f(eo,null,{default:()=>[f(oo,null,{default:()=>f("div",{ref:this.setHandleRefs(d),class:`${i}-slider-handle-wrapper`,tabindex:this.mergedDisabled?-1:0,role:"slider","aria-valuenow":s,"aria-valuemin":this.min,"aria-valuemax":this.max,"aria-orientation":this.vertical?"vertical":"horizontal","aria-disabled":this.disabled,style:this.getHandleStyle(s,d),onFocus:()=>{this.handleHandleFocus(d)},onBlur:()=>{this.handleHandleBlur(d)},onMouseenter:()=>{this.handleHandleMouseEnter(d)},onMouseleave:()=>{this.handleHandleMouseLeave(d)}},no(this.$slots.thumb,()=>[f("div",{class:`${i}-slider-handle`})]))}),this.tooltip&&f(to,{ref:this.setFollowerRefs(d),show:S,to:this.adjustedTo,enabled:this.showTooltip&&!this.range||this.followerEnabledIndexSet.has(d),teleportDisabled:this.adjustedTo===ee.tdkey,placement:this.mergedPlacement,containerClass:this.namespace},{default:()=>f(ao,{name:"fade-in-scale-up-transition",appear:this.isMounted,css:this.shouldKeepTooltipTransition(d),onEnter:()=>{this.followerEnabledIndexSet.add(d)},onAfterLeave:()=>{this.followerEnabledIndexSet.delete(d)}},{default:()=>{var R;return S?((R=this.indicatorOnRender)===null||R===void 0||R.call(this),f("div",{class:[`${i}-slider-handle-indicator`,this.indicatorThemeClass,`${i}-slider-handle-indicator--${this.mergedPlacement}`],style:this.indicatorCssVars},typeof v=="function"?v(s):s)):null}})})]})})),this.marks?f("div",{class:`${i}-slider-marks`},this.markInfos.map(s=>f("div",{key:s.key,class:`${i}-slider-mark`,style:s.style},typeof s.label=="function"?s.label():s.label))):null))}});export{po as _};
