<script setup lang="tsx">
// @ts-nocheck
import http from "@/service/axios";
const jsonContent = ref<HTMLElement | null>(null);
const { t } = useI18n();
const copyToClipboard = (data) => {
  console.log(data)
  if (data) { 
    const formattedData = JSON.stringify(data, null, 2); 
    navigator.clipboard.writeText(formattedData).then(() => {
      message.success(t("copysuccess"));
    }).catch((error) => {
      message.error("Failed to copy: " + error.message); 
    });
  } 
};

const formRef = ref(null);
const message = useMessage();
const currencytOption = [
  { label: "THB", value: "THB" },
  { label: "USD", value: "USD" },
];

const formSeamless = ref({
  product: "",
  username: "",
  callbackUrl: "",
  currency: "THB",
});

const rules = ref({
  product: [
    {
      required: true,
      message: "Please select product",
      trigger: ["blur", "change"],
    },
  ],
  username: [
    { required: true, message: "Please input username", trigger: "blur" },
  ],
  callbackUrl: [
    { required: true, message: "Please input callback-url", trigger: "blur" },
  ],
  currency: [
    {
      required: true,
      message: "Please select currency",
      trigger: ["blur", "change"],
    },
  ],
});
const dataSeamless = ref([
  { key: 1, label: "1. Testcase Check Get Balance Success" },
  {
    key: 2,
    label:
      "2. Testcase Check Place Bet and Settle (Win) In One Transaction Success",
  },
  {
    key: 3,
    label:
      "3. Testcase Check Place Bet and Settle (Lose) In One Transaction Success",
  },
  { key: 4, label: "4. Testcase Check Place Bet and Settle (Buy Freespin)" },
  { key: 5, label: "5. Testcase Check Place Bet and Settle (Freespin)" },
  {
    key: 6,
    label: "6. Testcase Check Place Bet and Settle Fail (Insufficient Balance)",
  },
  {
    key: 7,
    label:
      "7. Testcase Check Place Bet and Settle Success (Duplicate Transaction)",
  },
  {
    key: 8,
    label:
      "8. Testcase Check Place Bet and Settle Fail (Insufficient Balance) then Place Bet and Settle Success (Duplicate Transaction)",
  },
  {
    key: 9,
    label:
      "9. Testcase Check Place Bet and Settle Success (Buy Freespin) and Place Bet and Settle Success (Duplicate Transaction)",
  },
  {
    key: 10,
    label:
      "10. Testcase Check Place Bet and Settle Fail (Insufficient Balance - Buy Freespin) and Place Bet-Settle Success (Duplicate Transaction)",
  },
]);

const pattern = ref("");
const defaultExpandedKeys = ref([]);
const defaultCheckedKeys = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
const checkedKeys = ref(defaultCheckedKeys.value);
function updateCheckedKeys(keys) {
  checkedKeys.value = keys.slice();
}

const progressPercentage = ref(0);
const isTestRunning = ref(false);
const isTestCompleted = ref(false);
const isTestLoading = ref(false);
const items = ref([]);
const total = ref(0);
const totalStatus = ref(0);

function runtest() {
  const checkedKeysArray = checkedKeys.value.slice();
  if (
    !formSeamless.value.product ||
    !formSeamless.value.username ||
    !formSeamless.value.callbackUrl ||
    !formSeamless.value.currency
  ) {
    return message.error("กรุณากรอกข้อมูลให้ครบถ้วน");
  }
  if (checkedKeysArray.length === 0) {
    message.error("Please select at least one test case.");
    return;
  }
  isTestRunning.value = true;
  isTestLoading.value = true;
  progressPercentage.value = 0;
  isTestCompleted.value = false;
  const obj = {
    callbackUrl: formSeamless.value.callbackUrl,
    username: formSeamless.value.username,
    currency: formSeamless.value.currency,
    product: formSeamless.value.product,
    typeCase: checkedKeysArray,
  };
  progressPer();
  http.post("v1/runTests", obj).then((response) => {
    // http://127.0.0.1:4444/api admin01
    isTestRunning.value = false;
    if (response.data) {
      items.value = response.data;
      progressPercentage.value = 100;
      message.success("Test completed successfully!");
      isTestCompleted.value = true;
      total.value = items.value.reduce(
        (acc, ele) => acc + ele.subCases.length,
        0
      );
      totalStatus.value = items.value.reduce(
        (acc, ele) =>
          acc +
          ele.subCases.reduce((acz, els) => acz + (els.status ? 1 : 0), 0),
        0
      );
    }
  });
  // Start the test with progress

  // progressPercentage.value = 0;
  // isTestCompleted.value = false;
  // isTestLoading.value = true

  // const totalDuration = 5000; // Total time (10 seconds)
  // const intervalTime = totalDuration / 100; // Time for each 1% increase (100ms)

  // // Run the test and update the progress
  // let currentProgress = 0;

  // const interval = setInterval(() => {
  //   if (currentProgress < 100) {
  //     currentProgress += 1;
  //     progressPercentage.value = currentProgress;
  //   } else {
  //     clearInterval(interval); // Stop the interval once progress reaches 100%
  //     isTestRunning.value = false;
  //     isTestCompleted.value = true;
  //     message.success("Test completed successfully!");
  //   }
  // }, intervalTime); // Update every 100ms to increase progress by 1%
}
const progressPer = () => {
  const interval = setInterval(() => {
    if (progressPercentage.value < 100) {
      progressPercentage.value += 1;
    } else {
      clearInterval(interval);
    }
  }, 500);
};
// function runtest(e: MouseEvent) {
//   const checkedKeysArray = checkedKeys.value.slice();
//   console.log("Checked Keys:", checkedKeysArray);
//   // e.preventDefault();
//   // formRef.value?.validate((errors) => {
//   //   if (!errors) {
//   //     message.success("Valid");

//   //   } else {
//   //     message.error("Invalid");
//   //   }
//   // });
// }
</script>

<template>
  <NSpace vertical size="large">
    <div class="sm:flex gap-3 justify-center items-start">
      <n-card class="sm:w-150">
        <h4>Required Parameters</h4>
        <n-divider />
        <div class="mt-5">
          <n-form
            ref="formRef"
            :model="formSeamless"
            label-placement="left"
            :show-feedback="true"
            :rules="rules"
          >
            <n-flex vertical size="large">
              <n-form-item :label="$t('username')" path="username">
                <n-input
                  v-model:value="formSeamless.username"
                  :placeholder="$t('username')"
                />
              </n-form-item>
              <n-form-item label="Callback URL" path="callbackUrl">
                <n-input
                  v-model:value="formSeamless.callbackUrl"
                  placeholder="Callback URL"
                />
              </n-form-item>
              <n-form-item :label="$t('currency')" path="currency">
                <n-select
                  v-model:value="formSeamless.currency"
                  :placeholder="$t('currency')"
                  :options="currencytOption"
                  clearable
                  filterable
                />
              </n-form-item>
            </n-flex>
          </n-form>

          <div class="mt-5">
            <n-collapse>
              <n-collapse-item :title="$t('ststs')">
                <n-input
                  v-model:value="pattern"
                  :placeholder="$t('search')"
                  class="mb-5"
                />
                <n-tree
                  :pattern="pattern"
                  block-line
                  cascade
                  show-line
                  checkable
                  :data="dataSeamless"
                  :default-expanded-keys="defaultExpandedKeys"
                  :default-checked-keys="defaultCheckedKeys"
                  @update:checked-keys="updateCheckedKeys"
                />
              </n-collapse-item>
              <n-collapse-item :title="$t('tos')">
                <div class="text-orange">
                  {{ $t("tosdetail") }}
                </div>
              </n-collapse-item>
            </n-collapse>
          </div>
        </div>
        <n-flex justify="center" class="mt-10">
          <NButton type="primary" @click="runtest" :disabled="isTestRunning">
            <template #icon>
              <icon-park-outline-mind-mapping />
            </template>
            {{ $t("runtest") }}
          </NButton>
        </n-flex>
      </n-card>
      <div class="w-full">
        <n-card>
          <h4>
            {{ $t("testreport") }}
          </h4>
          <n-divider />
          <div v-if="!isTestLoading" class="justify-center my-5 text-center">
            <icon-park-outline-network-tree class="text-50px mb-5" />
            <p class="font-bold">
              {{ $t("seamless1") }}
            </p>
            <p>
              {{ $t("seamless2") }}
            </p>
          </div>
          <div v-if="isTestLoading" class="flex justify-center my-5">
            <n-progress
              type="circle"
              :percentage="progressPercentage"
              :color="{ stops: ['#E3F2FD', '#18a058'] }"
            />
          </div>
          <div
            v-if="isTestCompleted"
            class="flex flex-wrap gap-3 justify-center"
          >
            <n-alert title="Info" type="info" closable>
              Executed Scenarios: {{ totalStatus }} / {{ total }}
            </n-alert>
            <n-alert title="Success" type="success" closable>
              Passed {{ totalStatus }}
            </n-alert>
            <n-alert title="Fail" type="error" closable>
              Fail {{ total - totalStatus }}
            </n-alert>
            <n-alert title="Rate" type="warning" closable>
              Rate: {{ ((totalStatus / total) * 100).toFixed(2) }}%
            </n-alert>
          </div>
          <!-- <div v-if="isTestCompleted" class="flex justify-center mt-5">
            <n-alert title="Contratulation!" type="default" closable>
              <template #icon>
                <icon-park-outline-check-one />
              </template>
              Gee it's good to be back home
            </n-alert>
          </div> -->
          <n-divider />
        </n-card>
        <div v-if="isTestCompleted" class="mt-3">
          <n-card>
            {{ $t("testresult") }}
            <n-divider></n-divider>
            <div>
              <n-collapse :default-expanded-names="['0']"
                v-for="(item, index) in items"
                :key="index"
                class="my-5"
              >
                <n-collapse-item :title="`${index + 1}. ${item.caseName}`" name="0">
                  <!-- <template #header-extra>
                    <n-button ghost type="success" size="tiny">Pass 1</n-button>
                    <n-button class="ml-2" ghost type="error" size="tiny"
                      >Failed 1</n-button
                    >
                  </template> -->
                  <div class="overflow-x-auto">
                    <n-table :single-line="false">
                      <thead>
                        <tr class="text-center">
                          <th class="w-10">Detail</th>
                          <th class="w-72">Test Step</th>
                          <th class="w-10">Response Time(ms)</th>
                          <th class="w-10">Status</th>
                          <th class="w-auto">Error Description</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="(itemSub, indexSub) in item.subCases"
                          :key="indexSub"
                        >
                          <td class="text-center">
                            <n-popover
                              trigger="click"
                              style="width: auto"
                              placement="top"
                            >
                              <template #trigger>
                                <n-button ghost size="tiny">
                                  <icon-park-outline-eyes />
                                </n-button>
                              </template>
                              <div
                                class="flex w-72 sm:w-auto overflow-auto gap-3 text-xs sm:text-sm"
                              >
                                <div class="rounded-xl border-2 p-5">
                                  <div
                                    class="flex justify-between items-center"
                                  >
                                    <p>Request Body</p>
                                    <n-tooltip trigger="hover">
                                      <template #trigger>
                                        <n-button
                                          type="info"
                                          size="small"
                                          @click="copyToClipboard(itemSub.actualResponse,itemSub.requestDateTime)"
                                          ><icon-park-outline-copy
                                        /></n-button>
                                      </template>
                                      {{ $t("copy") }}
                                    </n-tooltip>
                                  </div>

                                  <n-divider></n-divider>
                                  <pre
                                    >{{ itemSub.requestBody }} </pre
                                  >
                                  <span
                                    >RequestTime:
                                    {{ itemSub.requestDateTime }}</span
                                  >
                                </div>
                                <div class="rounded-xl border-2 p-5">
                                  <div
                                    class="flex justify-between items-center"
                                  >
                                    <p>Expect Response</p>
                                    <n-tooltip trigger="hover">
                                      <template #trigger>
                                        <n-button
                                          type="info"
                                          size="small"
                                          @click="copyToClipboard(itemSub.expectResponse)"
                                          ><icon-park-outline-copy
                                        /></n-button>
                                      </template>
                                      {{ $t("copy") }}
                                    </n-tooltip>
                                  </div>

                                  <n-divider></n-divider>
                                  <pre ref="jsonContent">{{
                                    itemSub.expectResponse
                                  }}</pre>
                                </div>
                                <div class="rounded-xl border-2 p-5">
                                  <div
                                    class="flex justify-between items-center"
                                  >
                                    <p>Actual Response</p>
                                    <n-tooltip trigger="hover">
                                      <template #trigger>
                                        <n-button
                                          type="info"
                                          size="small"
                                          @click="copyToClipboard(itemSub.actualResponse, itemSub.responseDateTime)"
                                          ><icon-park-outline-copy
                                        /></n-button>
                                      </template>
                                      {{ $t("copy") }}
                                    </n-tooltip>
                                  </div>

                                  <n-divider></n-divider>
                                  <pre 
                                    >{{ itemSub.actualResponse }} </pre
                                  >
                                  <span
                                    >ResponseTime:
                                    {{ itemSub.responseDateTime }}</span
                                  >
                                </div>
                              </div>
                            </n-popover>
                          </td>

                          <td>
                            {{ index + 1 }}.{{ indexSub + 1 }}
                            {{ itemSub.title }}
                          </td>
                          <td class="text-center">
                            <n-button ghost type="info" size="tiny"
                              >{{ itemSub.responseTime }} ms</n-button
                            >
                          </td>
                          <td class="text-center">
                            <n-button
                              ghost
                              :type="itemSub.status ? 'success' : 'error'"
                              size="tiny"
                              >{{
                                itemSub.status ? "Pass" : "Failed"
                              }}</n-button
                            >
                          </td>
                          <td class="text-center">
                            <div v-if="!itemSub.status" class="">
                              <div
                                v-for="err in itemSub.failMessage"
                                :key="err"
                              >
                                <p
                                  class="my-0.5 text-[#d03050]"
                                  ghost
                                  type="error"
                                  size="tiny"
                                  >{{ err }}</p
                                >
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </n-table>
                  </div>
                </n-collapse-item>
                <!-- <n-collapse-item title="2. Bet-Settled (Lose) by round">
                  <div>test</div>
                </n-collapse-item> -->
              </n-collapse>
            </div>
          </n-card>
        </div>
      </div>
    </div>
  </NSpace>
</template>
