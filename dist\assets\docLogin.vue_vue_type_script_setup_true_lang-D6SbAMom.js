import{_ as y}from"./copy-BxkeU8Ds.js";import{d as $,r as w,ac as q,ad as k,m as R,o as C,b as o,ab as I,w as d,a as t,g as u,B as T,t as s,ae as B,$ as _}from"./index-pY9FjpQW.js";import{_ as z}from"./Table-DoHSPnrC.js";const L={class:"border border-orange rounded-lg p-2 mt-4"},O={class:"font-bold"},j={class:"border rounded-lg p-2 mt-4"},N={class:"mt-4 overflow-x-auto"},P={class:"w-1/4"},S={class:"w-1/4"},D={class:"w-1/4"},M={class:"w-1/4"},V={class:"mt-4"},A={class:"mt-4 overflow-x-auto"},E={class:"w-1/4"},F={class:"w-1/4"},G={class:"w-1/4"},J={class:"w-1/4"},K={class:"mt-4"},U={class:"bg-black/70 rounded-lg px-4 pt-4"},H={class:"absolute right-0 mr-10"},Q={class:"mt-4"},W={class:"bg-black/70 rounded-lg px-4 pt-4"},X={class:"absolute right-0 mr-10"},c=$({__name:"docLogin",setup(Y){const r=w(null),{t:f}=q(),v=k(),i=()=>{const l=r.value?.innerText;l&&navigator.clipboard.writeText(l).then(()=>{v.success(f("copysuccess"))})};return(l,n)=>{const e=T,p=B,g=z,a=y,m=_,b=I;return C(),R("div",null,[o(b,null,{default:d(()=>[n[38]||(n[38]=t("div",{class:"font-bold"},"Login",-1)),t("div",null,[o(e,{type:"success",round:"",size:"small",class:"mt-3 cursor-default"},{default:d(()=>[u(s(l.$t("update")),1)]),_:1}),u(" "+s(l.$t("lastupdate")),1)]),t("div",L,[o(e,{class:"cursor-default",type:"warning",round:"",size:"tiny"},{default:d(()=>n[0]||(n[0]=[u("POST")])),_:1}),n[1]||(n[1]=t("span",null," {{ API_URL }}/seamless/logIn",-1))]),o(p),t("div",null,[t("div",O,s(l.$t("security")),1),t("div",j,[t("div",null,[o(e,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:d(()=>n[2]||(n[2]=[u("Authorization")])),_:1})]),n[4]||(n[4]=t("span",null," apiKey : Base64({{ agent_username }}:{{ secret_key }})",-1)),n[5]||(n[5]=t("hr",{class:"my-2"},null,-1)),t("div",null,[o(e,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:d(()=>n[3]||(n[3]=[u("Content Type")])),_:1})]),n[6]||(n[6]=t("span",null," Type: application/json",-1))])]),o(p),t("div",null,[n[22]||(n[22]=t("div",{class:"font-bold"},"Parameter Description",-1)),t("div",N,[o(g,null,{default:d(()=>[t("thead",null,[t("tr",null,[t("th",P,s(l.$t("property")),1),t("th",S,s(l.$t("type")),1),t("th",D,s(l.$t("required")),1),t("th",M,s(l.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[7]||(n[7]=t("td",null,"username",-1)),n[8]||(n[8]=t("td",null,"string (a-z,0-9 )",-1)),n[9]||(n[9]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg1")),1)]),t("tr",null,[n[10]||(n[10]=t("td",null,"productId",-1)),n[11]||(n[11]=t("td",null,"string",-1)),n[12]||(n[12]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg2")),1)]),t("tr",null,[n[13]||(n[13]=t("td",null,"gameId",-1)),n[14]||(n[14]=t("td",null,"number",-1)),n[15]||(n[15]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg3")),1)]),t("tr",null,[n[16]||(n[16]=t("td",null,"isMobileLogin",-1)),n[17]||(n[17]=t("td",null,"boolean",-1)),n[18]||(n[18]=t("td",null,"Optional",-1)),t("td",null,s(l.$t("doc.lg4")),1)]),t("tr",null,[n[19]||(n[19]=t("td",null,"language",-1)),n[20]||(n[20]=t("td",null,"string",-1)),n[21]||(n[21]=t("td",null,"Optional",-1)),t("td",null,s(l.$t("doc.lg5")),1)])])]),_:1})])]),t("div",V,[n[35]||(n[35]=t("div",{class:"font-bold"},"Response Description",-1)),t("div",A,[o(g,null,{default:d(()=>[t("thead",null,[t("tr",null,[t("th",E,s(l.$t("property")),1),t("th",F,s(l.$t("type")),1),t("th",G,s(l.$t("required")),1),t("th",J,s(l.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[23]||(n[23]=t("td",null,"reqId",-1)),n[24]||(n[24]=t("td",null,"string",-1)),n[25]||(n[25]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg6")),1)]),t("tr",null,[n[26]||(n[26]=t("td",null,"code",-1)),n[27]||(n[27]=t("td",null,"number",-1)),n[28]||(n[28]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg7")),1)]),t("tr",null,[n[29]||(n[29]=t("td",null,"message",-1)),n[30]||(n[30]=t("td",null,"string",-1)),n[31]||(n[31]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg8")),1)]),t("tr",null,[n[32]||(n[32]=t("td",null,"data",-1)),n[33]||(n[33]=t("td",null,"string",-1)),n[34]||(n[34]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.lg9")),1)])])]),_:1})])]),t("div",K,[n[36]||(n[36]=t("div",{class:"font-bold mb-4"},"Request Body",-1)),t("div",U,[t("div",H,[o(m,{trigger:"hover"},{trigger:d(()=>[o(e,{class:"text-white",onClick:i},{default:d(()=>[o(a)]),_:1})]),default:d(()=>[u(" "+s(l.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:r,class:"font-normal text-white overflow-x-auto"},`{
    "username": "string",
    "productId": "PGSOFT",
    "gameId": "int",
    "isMobileLogin": true,
    "language": "th"
}
    `,512)])]),t("div",Q,[n[37]||(n[37]=t("div",{class:"font-bold mb-4"},"JSON response example",-1)),t("div",W,[t("div",X,[o(m,{trigger:"hover"},{trigger:d(()=>[o(e,{class:"text-white",onClick:i},{default:d(()=>[o(a)]),_:1})]),default:d(()=>[u(" "+s(l.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:r,class:"font-normal text-white overflow-x-auto"},`{
    "reqId": "string",
    "code": "int",
    "message": "string",
    "data": {
    "sessionToken": "string",
    "url": "string",
}
    `,512)])])]),_:1})])}}});export{c as _};
