<script setup lang="ts">
import http from "@/service/axios";
import { useAuthStore } from "@/store/auth";

const { userData } = useAuthStore();
const emit = defineEmits(["save"]);
const message = useMessage();
const { t } = useI18n();
const isChecked = ref(false);
const balance = ref(0);
const handleSwitchChange = (value: boolean) => {
  isChecked.value = value;
};
const winRateSettings = ref({
  player: {
    player: 50,
    high: 50,
    low: 50,
    odd: 50,
    even: 50,
    zero: 5,
    double: 10,
  },
  tie: {
    tie: 8,
    zero: 3,
  },
  banker: {
    banker: 50,
    high: 50,
    low: 50,
    odd: 50,
    even: 50,
    zero: 5,
    double: 10,
  },
});

// คำนวณอัตราชนะรวม
const overallWinRate = computed(() => {
  const allRates = [
    ...Object.values(winRateSettings.value.player),
    ...Object.values(winRateSettings.value.tie),
    ...Object.values(winRateSettings.value.banker),
  ];
  return Math.round(
    allRates.reduce((sum, rate) => sum + rate, 0) / allRates.length
  );
});

const sliderValue = ref(100);
const adjustAllWinRates = (percentage: number) => {
  const multiplier = percentage / 100;
  sliderValue.value = percentage;
  Object.keys(winRateSettings.value.player).forEach((key) => {
    const baseRate = getBaseRate("player", key);
    winRateSettings.value.player[key] = Math.min(
      95,
      Math.max(5, Math.round(baseRate * multiplier))
    );
  });

  Object.keys(winRateSettings.value.tie).forEach((key) => {
    const baseRate = getBaseRate("tie", key);
    winRateSettings.value.tie[key] = Math.min(
      95,
      Math.max(1, Math.round(baseRate * multiplier))
    );
  });

  Object.keys(winRateSettings.value.banker).forEach((key) => {
    const baseRate = getBaseRate("banker", key);
    winRateSettings.value.banker[key] = Math.min(
      95,
      Math.max(5, Math.round(baseRate * multiplier))
    );
  });
};

// อัตราฐาน (Base rates)
const getBaseRate = (category: string, type: string) => {
  const baseRates = {
    player: {
      player: 50,
      high: 50,
      low: 50,
      odd: 50,
      even: 50,
      zero: 5,
      double: 10,
    },
    tie: { tie: 8, zero: 3 },
    banker: {
      banker: 50,
      high: 50,
      low: 50,
      odd: 50,
      even: 50,
      zero: 5,
      double: 10,
    },
  };
  return baseRates[category][type];
};

// Preset configurations
const presetConfigs = computed(() => [
  { name: t("conservative"), value: 70, description: "ผู้เล่นชนะน้อย" },
  { name: t("balanced"), value: 100, description: "สมดุล" },
  { name: t("aggressive"), value: 130, description: "ผู้เล่นชนะมาก" },
  { name: t("highRisk"), value: 150, description: "ความเสี่ยงสูง" },
]);

const handleSubmit = () => {
  if (balance.value <= 0) {
    return message.error(t("balaceNot0"));
  }

  const obj = {
    balance: balance.value,
    winRateSettings: winRateSettings.value,
    status: isChecked.value,
  };

  // Uncomment when API is ready
  // http.post("demo/UpdateBaccaratSetting", obj).then((response) => {
  //   if (response.data.status) {
  //     message.success(response.data.mes);
  //     getData();
  //     emit("save", true);
  //   } else {
  //     message.error(response.data.mes);
  //   }
  // });

  emit("save", true);
};

const checkPrime = () => {
  if (
    (userData?.position_type == 4 && userData?.upline_position_type == 3) ||
    (userData?.position_type == 4 && userData?.upline_position_type == 2) ||
    (userData?.position_type == 4 && userData?.upline_position_type == 1)
  ) {
    const permis = JSON.parse(userData?.permissions);
    if (permis.setting !== 2) {
      return false;
    }
  }
  return true;
};

onMounted(() => {
  getData();
  getProfile();
});

const getData = async () => {
  try {
    const { data: res } = await http.get("demo/BaccaratSetting");
    if (res) {
      balance.value = res.balance;
      if (res.winRateSettings) {
        winRateSettings.value = res.winRateSettings;
      }
      isChecked.value = res.status;
    }
  } catch (err) {
    console.log(err);
  }
};

const UserData = ref(null);

const getProfile = async () => {
  try {
    const res = await http.get("v1/Profile");
    if (res.data) {
      UserData.value = res.data;
    }
  } catch (error) {
    console.log(error);
  }
};
</script>

<template>
  <div>
    <n-space vertical size="large">
      <!-- Header Section -->
      <n-card>
        <n-space justify="space-between" align="center">
          <n-space align="center">
            <n-avatar size="large" color="#2080f0">
              <nova-icon icon="icon-park-outline:setting" />
            </n-avatar>
            <div>
              <n-h3 style="margin: 0">{{ $t("setting") }}</n-h3>
              <n-text depth="3">
                {{ $t("ตั้งค่าเปอร์เซ็นต์รางวัลเดิมพัน") }}
              </n-text>
            </div>
          </n-space>
          <div v-if="checkPrime()">
            <n-button type="primary" @click="handleSubmit">
              <template #icon>
                <icon-park-outline-check />
              </template>
              {{ $t("save") }}
            </n-button>
          </div>
        </n-space>
      </n-card>

      <!-- Main Content Grid -->
      <n-grid cols="1" :x-gap="16" :y-gap="16" responsive="screen">
        <!-- Overall Win Rate Control -->
        <n-gi>
          <n-card>
            <template #header>
              <n-space align="center" justify="center">
                <n-tag size="large">
                  <icon-park-outline-chart-line />
                </n-tag>
                <div>
                  <n-h3 style="margin: 0">{{ $t("set-all") }}</n-h3>
                  <n-text depth="3">{{ $t("overallwinratecontrol") }}</n-text>
                </div>
              </n-space>
            </template>

            <n-space vertical size="large" justify="space-around">
              <!-- Current Overall Win Rate Display -->
              <div class="flex justify-center gap-8">
                <n-space vertical>
                  <n-text strong>{{ $t("overallWinRate") }}</n-text>
                  <n-space class="text-xl">
                    <icon-park-outline-chart-line
                      :class="
                        overallWinRate > 50
                          ? 'text-[#18a058]'
                          : 'text-[#d03050]'
                      "
                    />
                    <n-text> {{ overallWinRate }} %</n-text>
                  </n-space>
                </n-space>
                <!-- Preset Configurations -->
                <n-space vertical>
                  <n-text strong>{{ $t("quickPresets") }}</n-text>
                  <n-space>
                    <n-button
                      v-for="preset in presetConfigs"
                      :key="preset.name"
                      size="small"
                      :type="
                        preset.value === sliderValue ? 'primary' : 'default'
                      "
                      @click="adjustAllWinRates(preset.value)"
                    >
                      {{ preset.name }}
                    </n-button>
                  </n-space>
                </n-space>

                <n-space vertical>
                  <n-text strong>{{ $t("status") }}</n-text>
                  <n-switch
                    v-model:value="isChecked"
                    @update:value="handleSwitchChange"
                    :disabled="!checkPrime()"
                    size="large"
                  >
                    <template #checked>
                      {{ $t("enabled") }}
                    </template>
                    <template #unchecked>
                      {{ $t("disabled") }}
                    </template>
                  </n-switch>
                </n-space>
              </div>

              <!-- Custom Adjustment Slider -->
              <n-space vertical>
                <n-text strong>{{ $t("customAdjustment") }}</n-text>
                <n-slider
                  :value="sliderValue"
                  :min="50"
                  :max="200"
                  :step="10"
                  :marks="{
                    50: '50%',
                    100: '100%',
                    150: '150%',
                    200: '200%',
                  }"
                  @update:value="adjustAllWinRates"
                />
              </n-space>
            </n-space>
          </n-card>
        </n-gi>
      </n-grid>

      <!-- Detailed Win Rate Settings -->
      <n-card>
        <template #header>
          <div class="flex items-center gap-2">
            <icon-park-outline-setting-two />
            <span>{{ $t("detail-winrate") }}</span>
          </div>
        </template>

        <n-grid cols="1 m:3" :x-gap="16" :y-gap="16" responsive="screen">
          <!-- Player Section -->
          <n-gi>
            <n-card class="border-blue-500/30">
              <template #header>
                <n-space justify="center" align="center">
                  <n-tag type="info">P</n-tag>
                  <span class="text-blue-400 font-medium">{{
                    $t("baccarat.player.side")
                  }}</span>
                </n-space>
              </template>

              <n-form-item :label="$t('baccarat.player.player')">
                <n-input-number
                  v-model:value="winRateSettings.player.player"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.player.high')">
                <n-input-number
                  v-model:value="winRateSettings.player.high"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.player.low')">
                <n-input-number
                  v-model:value="winRateSettings.player.low"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.player.odd')">
                <n-input-number
                  v-model:value="winRateSettings.player.odd"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.player.even')">
                <n-input-number
                  v-model:value="winRateSettings.player.even"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.player.zero')">
                <n-input-number
                  v-model:value="winRateSettings.player.zero"
                  :min="1"
                  :max="30"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.player.double')">
                <n-input-number
                  v-model:value="winRateSettings.player.double"
                  :min="1"
                  :max="50"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>
            </n-card>
          </n-gi>

          <!-- Tie Section -->
          <n-gi>
            <n-card class="border-green-500/30">
              <template #header>
                <n-space justify="center" align="center">
                  <n-tag type="success">T</n-tag>
                  <span class="text-green-400 font-medium">{{
                    $t("baccarat.tie.side")
                  }}</span>
                </n-space>
              </template>

              <n-form-item :label="$t('baccarat.tie.tie')">
                <n-input-number
                  v-model:value="winRateSettings.tie.tie"
                  :min="1"
                  :max="30"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.tie.zero')">
                <n-input-number
                  v-model:value="winRateSettings.tie.zero"
                  :min="1"
                  :max="20"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <!-- Spacer for alignment -->
              <div style="height: 200px"></div>
            </n-card>
          </n-gi>

          <!-- Banker Section -->
          <n-gi>
            <n-card class="border-red-500/30">
              <template #header>
                <n-space justify="center" align="center">
                  <n-tag type="error">B</n-tag>
                  <span class="text-red-400 font-medium">{{
                    $t("baccarat.banker.side")
                  }}</span>
                </n-space>
              </template>

              <n-form-item :label="$t('baccarat.banker.banker')">
                <n-input-number
                  v-model:value="winRateSettings.banker.banker"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.banker.high')">
                <n-input-number
                  v-model:value="winRateSettings.banker.high"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.banker.low')">
                <n-input-number
                  v-model:value="winRateSettings.banker.low"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.banker.odd')">
                <n-input-number
                  v-model:value="winRateSettings.banker.odd"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.banker.even')">
                <n-input-number
                  v-model:value="winRateSettings.banker.even"
                  :min="1"
                  :max="95"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.banker.zero')">
                <n-input-number
                  v-model:value="winRateSettings.banker.zero"
                  :min="1"
                  :max="30"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>

              <n-form-item :label="$t('baccarat.banker.double')">
                <n-input-number
                  v-model:value="winRateSettings.banker.double"
                  :min="1"
                  :max="50"
                  :step="1"
                  style="width: 100%"
                >
                  <template #suffix> % </template>
                </n-input-number>
              </n-form-item>
            </n-card>
          </n-gi>
        </n-grid>
      </n-card>
    </n-space>
  </div>
</template>
