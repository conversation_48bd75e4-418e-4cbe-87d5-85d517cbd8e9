<script setup lang="ts">
import { useAppStore } from "@/store";
import * as echarts from "echarts";

const appStore = useAppStore();
const primaryColor = computed(() => appStore.primaryColor);

function hexToRgba(hex: string, alpha = 1): string {
  const sanitizedHex = hex.replace("#", "");

  if (sanitizedHex.length !== 6) return hex;

  const bigint = parseInt(sanitizedHex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

const rgbaPrimary = computed(() => hexToRgba(primaryColor.value, 0.2));
const rgbaPrimary2 = computed(() => hexToRgba(primaryColor.value, 0.05));
const rgbaPrimary3 = computed(() => hexToRgba(primaryColor.value, 0.03));

let myChart: echarts.ECharts | null = null;
const lineRef = ref<HTMLElement | null>(null);

const thaiMonths = [
  "ม.ค.",
  "ก.พ.",
  "มี.ค.",
  "เม.ย.",
  "พ.ค.",
  "มิ.ย.",
  "ก.ค.",
  "ส.ค.",
  "ก.ย.",
  "ต.ค.",
  "พ.ย.",
  "ธ.ค.",
];

const updateChart = () => {
  if (myChart) {
    const option: echarts.EChartsOption = {
      legend: {
        data: ["รายรับ", "รายจ่าย"],
        top: "0",
        right: "5%",
        textStyle: {
          color: "#6B7280",
        },
      },
   
      tooltip: {
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
        },
        trigger: "axis",
        backgroundColor: "#fff",
        borderRadius: 10,
        padding: [10, 14],
        borderColor: "#E5E7EB",
        borderWidth: 1,
        textStyle: { color: "#374151", fontSize: 13 },
        formatter: (params: any) => {
          let tooltipContent = `<div style="font-weight:600;margin-bottom:6px;font-size:14px">${params[0].axisValue}</div>`;
          params.forEach((item: any) => {
            const value = new Intl.NumberFormat("th-TH").format(item.value);
            tooltipContent += `<div style="color:${item.color};">${item.marker} ${item.seriesName} : ${value}</div>`;
          });
          return tooltipContent;
        },
      },
      grid: isMobile.value
        ? {
            left: "3%",
            right: "4%",
            bottom: "15%",
            containLabel: true,
          }
        : {
            left: "4%",
            right: "4%",
            bottom: "4%",
            containLabel: true,
          },
      dataZoom: isMobile.value
        ? [
            {
              type: "inside",
              xAxisIndex: [0],
              start: 0,
              end: 60,
              zoomLock: false,
            },
            {
              type: "slider",
              xAxisIndex: [0],
              start: 0,
              end: 60,
              height: 25,
              handleSize: "100%",
              handleStyle: {
                color: primaryColor.value,
              },
            },
          ]
        : [],
      xAxis: {
        type: "category",
        data: thaiMonths,
        boundaryGap: false,
        axisTick: { alignWithLabel: true, lineStyle: { color: "#E5E7EB" } },
        axisLine: { lineStyle: { color: "#E5E7EB" } },
        axisLabel: { fontSize: 13, color: "#6B7280", padding: [8, 0] },
      },
      yAxis: {
        type: "value",
        splitLine: { lineStyle: { type: "dashed" } },
        axisLabel: {
          color: "#6B7280",
          fontSize: 13,
          padding: [0, 8],
          formatter: (v: number) =>
            new Intl.NumberFormat("th-TH", {
              notation: "compact",
              compactDisplay: "short",
            }).format(v),
        },
      },
      series: [
        {
          name: "รายรับ",
          type: "line",
          smooth: true,
          showSymbol: true,
          symbol: "circle",
          symbolSize: 7,
          emphasis: {
            focus: "series",
            itemStyle: {
              borderWidth: 2,
              borderColor: primaryColor.value,
              shadowColor: rgbaPrimary3.value,
              shadowBlur: 10,
            },
          },
          lineStyle: {
            width: 3,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: primaryColor.value },
              { offset: 1, color: rgbaPrimary.value },
            ]),
            shadowColor: rgbaPrimary2.value,
            shadowBlur: 12,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: rgbaPrimary2.value },
              { offset: 1, color: rgbaPrimary.value },
            ]),
          },
          itemStyle: {
            color: primaryColor.value,
          },
          data: incomeData.value,
        },
        {
          name: "รายจ่าย",
          type: "line",
          smooth: true,
          showSymbol: true,
          symbol: "circle",
          symbolSize: 7,
          emphasis: {
            focus: "series",
            itemStyle: {
              borderWidth: 2,
              borderColor: "#FF4C4C",
              shadowColor: "rgba(255, 76, 76, 0.3)",
              shadowBlur: 10,
            },
          },
          lineStyle: {
            width: 3,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: "#FF4C4C" },
              { offset: 1, color: "rgba(255, 76, 76, 0.2)" },
            ]),
            shadowColor: "rgba(255, 76, 76, 0.1)",
            shadowBlur: 12,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(255, 76, 76, 0.2)" },
              { offset: 1, color: "rgba(255, 76, 76, 0.05)" },
            ]),
          },
          itemStyle: {
            color: "#FF4C4C",
          },
          data: expenseData.value,
        },
      ],
    };
    myChart.setOption(option);
  }
};

const isMobile = ref(window.innerWidth <= 768);
const incomeData = ref([]);
const expenseData = ref([]);

const randomData = () => {
  for (let i = 0; i < 12; i++) {
    incomeData.value.push(Math.floor(Math.random() * 10000));
    expenseData.value.push(Math.floor(Math.random() * 8000));
  }
};

onMounted(() => {
  randomData();
  if (lineRef.value) {
    myChart = echarts.init(lineRef.value);
    updateChart();
  }
  window.addEventListener("resize", () => {
    isMobile.value = window.innerWidth <= 768;
  });
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
  }
});

watch(() => isMobile.value, updateChart, { immediate: true });
watch(() => appStore.primaryColor, updateChart);
</script>

<template>
  <div class="-mt-5">
    <div ref="lineRef" class="h-400px w-full" />
  </div>
</template>
