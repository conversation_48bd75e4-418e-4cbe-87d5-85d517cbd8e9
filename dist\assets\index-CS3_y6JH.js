import{_ as te,a as ne}from"./Pagination.vue_vue_type_script_setup_true_lang-tK6i-N0U.js";import{u as R,_ as ae}from"./download-Bcu0jZCr.js";import{d as oe,fB as le,r as _,ac as se,U as b,b as n,B as u,gb as ie,af as h,ad as re,a3 as ue,fQ as k,m as ce,o as de,w as l,ab as pe,f as a,a as s,fT as _e,fZ as me,g as y,t as d,gc as fe,gj as ge,i as ye,fR as ve,gf as we}from"./index-pY9FjpQW.js";import{_ as be}from"./write-lGVnfd1y.js";import{m as $}from"./index-DZ56Tu_n.js";import{_ as he}from"./Form-_sVFK3VR.js";import{_ as ke}from"./FormItem-B4P0FvHJ.js";import"./Checkbox-CwEpY3xE.js";import"./RadioGroup-DnXU-fFU.js";import"./download-C2161hUv.js";const $e={class:"sm:flex flex-wrap gap-3 w-full sm:w-auto"},Se={class:"my-1 sm: w-auto"},De={class:"my-1 sm: w-auto"},Me={class:"ml-auto sm:flex items-center gap-3"},Be={class:"my-1 sm: w-auto"},Ue={class:"my-1 sm: w-auto"},Ye={class:"flex-center gap-4"},Ce={class:"font-bold text-[1rem]"},xe={class:"flex items-center gap-3 my-2"},ze={class:"w-15"},Re={class:"flex items-center gap-3 my-2"},je={class:"w-15"},He={class:"flex items-center gap-3 my-2"},Pe={class:"w-15"},Te={class:"mt-8 flex justify-end"};function S(w){return typeof w=="function"||Object.prototype.toString.call(w)==="[object Object]"&&!we(w)}const We=oe({__name:"index",setup(w){const{userData:m}=le(),{bool:j,setTrue:H,setFalse:P}=R(!1);R(!1);const D={condition_1:"",condition_2:"",condition_3:null},c=_({...D}),T=_(),{t:o}=se(),N=b(()=>[{title:o("no."),align:"center",key:"index",render:(t,e)=>e+1},{title:o("player"),align:"center",key:"username"},{title:o("currency"),align:"center",key:"currency"},{title:o("createdate"),align:"center",key:"created_at",render:t=>$(t.created_at).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss")},{title:o("status"),align:"center",key:"status",render:t=>{let e,p;return t.status===!0?n(u,{ghost:!0,type:"success",class:"cursor-default"},S(e=o("active"))?e:{default:()=>[e]}):n(u,{ghost:!0,type:"error",class:"cursor-default"},S(p=o("inactive"))?p:{default:()=>[p]})}},{title:o("manage"),align:"center",key:"actions",render:t=>(m.position_type==4&&m.upline_position_type==1||m.position_type==4&&m.upline_position_type==2||m.position_type==4&&m.upline_position_type==3)&&JSON.parse(m.permissions).player!==2?n(u,{class:"cursor-default",tertiary:!0,type:"error",size:"small"},{default:()=>[n(ie,null,null)]}):n(h,{justify:"center"},{default:()=>[n(u,{type:"warning",size:"small",onClick:()=>L(t)},{default:()=>[n(be,null,null)]})]})}]),A=b(()=>[{title:o("no."),align:"center",key:"index",render:(t,e)=>e+1},{title:o("position"),align:"center",key:"position_type",render:t=>{let e;return n(u,{class:"cursor-default",ghost:!0,size:"small",type:t.position_type==2?"warning":"success"},S(e=o(t.position_type==2?"reseller":"agent"))?e:{default:()=>[e]})}},{title:o("player"),align:"center",key:"username"},{title:o("name"),align:"center",key:"name"},{title:o("wallettype"),align:"center",key:"bet_type",render:t=>n(u,{ghost:!0,size:"small",class:"cursor-default",type:t.bet_type==1?"error":"info"},{default:()=>[t.bet_type==1?"Seamless":"Tranfer"]})},{title:o("createdate"),align:"center",key:"created_at"},{title:o("lastlogin"),align:"center",key:"last_login",render:t=>$(t.last_login).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss")},{title:o("lastloginip"),align:"center",key:"last_ip",render:t=>$(t.last_ip).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss")}]),M=re(),f=_(!1),r=_(null),L=t=>{t?r.value=t:r.value=null,f.value=!0},O=()=>{if(r.value){const t={id:r.value._id,name:r.value.name,Status:r.value.status};k.post("PG/Management/Update",t).then(e=>{e.data.status?(f.value=!1,M.success(e.data.mes)):M.error(e.data.mes),v()})}};b(()=>[{role:o("provider"),id:1},{role:o("player"),id:2},{role:o("status"),id:3}]);const E=_([{role:"THB",id:"THB"},{role:"KRW",id:"KRW"},{role:"USD",id:"USD"}]),G=b(()=>[{label:o("active"),value:!0},{label:o("inactive"),value:!1}]),B=_([]),U=_(1),V=JSON.parse(localStorage.getItem("userData"));ue(()=>{v()});function I(t,e){window.$message.success(`${o("gotopage")}: ${t}/${e}`),Y.value=e,C.value=t,v()}const J=t=>{c.value={...D}},Y=_(10),C=_(1),v=async()=>{H();try{const t={perPage:Y.value,page:C.value,search:c.value.condition_2,currency:c.value.condition_3},e=await k.get("PG/Management/GetMembers",{params:t});B.value=e.data.data,U.value=e.data.total?e.data.total:1}catch(t){console.log(t)}finally{P()}},K=()=>{const t={search:c.value.condition_2,currency:c.value.condition_3};k.post("PG/download/player",t,{responseType:"blob"}).then(e=>{const p=new Blob([e.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),g=document.createElement("a");g.href=window.URL.createObjectURL(p),g.download="player",g.click()}).catch(e=>{console.log(e)})};return(t,e)=>{const p=_e,g=ke,x=me,W=fe,F=ge,Q=he,z=pe,Z=ae,q=te,X=ne,ee=ve;return de(),ce("div",null,[n(a(h),{vertical:"",size:"large"},{default:l(()=>[n(z,null,{default:l(()=>[n(Q,{ref_key:"formRef",ref:T,model:a(c),"label-placement":"left",inline:"","show-feedback":!1},{default:l(()=>[s("div",$e,[s("div",Se,[n(g,{label:t.$t("player"),path:"condition_2"},{default:l(()=>[n(p,{value:a(c).condition_2,"onUpdate:value":e[0]||(e[0]=i=>a(c).condition_2=i),placeholder:t.$t("player")},null,8,["value","placeholder"])]),_:1},8,["label"])]),s("div",De,[n(g,{label:t.$t("currency"),path:"condition_3"},{default:l(()=>[n(x,{value:a(c).condition_3,"onUpdate:value":e[1]||(e[1]=i=>a(c).condition_3=i),placeholder:t.$t("currency"),filterable:"",clearable:"","label-field":"role","value-field":"id",options:a(E)},null,8,["value","placeholder","options"])]),_:1},8,["label"])]),s("div",Me,[s("div",Be,[n(a(u),{type:"primary",onClick:v,class:"w-full"},{icon:l(()=>[n(W)]),default:l(()=>[y(" "+d(t.$t("search")),1)]),_:1})]),s("div",Ue,[n(a(u),{secondary:"",onClick:J,class:"w-full"},{icon:l(()=>[n(F)]),default:l(()=>[y(" "+d(t.$t("reset")),1)]),_:1})])])])]),_:1},8,["model"])]),_:1}),n(z,null,{default:l(()=>[n(a(h),{vertical:"",size:"large"},{default:l(()=>[s("div",Ye,[s("h4",Ce,d(t.$t("listplayer")),1),e[9]||(e[9]=s("div",{class:"ml-a"},null,-1)),n(a(u),{type:"primary",class:"ml-a",onClick:e[2]||(e[2]=i=>K())},{icon:l(()=>[n(Z)]),default:l(()=>[y(" "+d(t.$t("download")),1)]),_:1})]),n(q,{"scroll-x":1200,columns:a(V).value.position_type==1?a(A):a(N),data:a(B),loading:a(j)},null,8,["columns","data","loading"]),n(X,{class:"overflow-auto",count:a(U),onChange:I},null,8,["count"])]),_:1})]),_:1})]),_:1}),n(ee,{show:a(f),"onUpdate:show":e[8]||(e[8]=i=>ye(f)?f.value=i:null),class:"w-350px sm:w-400px  max-h-[80vh] overflow-y-auto",size:"small",preset:"card",segmented:{content:!0,footer:!0},onAfterLeave:t.handleClose},{header:l(()=>[y(d(t.$t("updateplayer")),1)]),default:l(()=>[n(a(h),{vertical:""},{default:l(()=>[s("div",xe,[s("span",ze,d(t.$t("player")),1),n(p,{value:a(r).username,"onUpdate:value":e[3]||(e[3]=i=>a(r).username=i),type:"text",disabled:"",placeholder:""},null,8,["value"])]),s("div",Re,[s("span",je,d(t.$t("name")),1),n(p,{value:a(r).name,"onUpdate:value":e[4]||(e[4]=i=>a(r).name=i),type:"text",placeholder:t.$t("name")},null,8,["value","placeholder"])]),s("div",He,[s("span",Pe,d(t.$t("status")),1),n(x,{value:a(r).status,"onUpdate:value":e[5]||(e[5]=i=>a(r).status=i),placeholder:t.$t("status"),filterable:"","label-field":"label","value-field":"value",options:a(G)},null,8,["value","placeholder","options"])]),s("div",Te,[n(a(u),{type:"error",onClick:e[6]||(e[6]=i=>(f.value=!1,v())),class:"mr-2"},{icon:l(()=>e[10]||(e[10]=[])),default:l(()=>[y(" "+d(t.$t("cancel")),1)]),_:1}),n(a(u),{type:"success",onClick:e[7]||(e[7]=i=>O())},{icon:l(()=>e[11]||(e[11]=[])),default:l(()=>[y(" "+d(t.$t("save")),1)]),_:1})])]),_:1})]),_:1},8,["show","onAfterLeave"])])}}});export{We as default};
