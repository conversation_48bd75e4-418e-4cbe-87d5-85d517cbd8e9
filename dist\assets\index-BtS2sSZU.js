import{d as V,ac as F,fB as P,r as s,a3 as $,fQ as g,m,n as d,o as l,b as r,w as o,c as f,ab as A,a as c,G as j,a8 as H,t as b,f as I,af as R,B as Y,g as h,fR as Z,aa as E,a9 as J}from"./index-pY9FjpQW.js";import{_ as B}from"./setting.vue_vue_type_script_setup_true_lang-CxS7Ki8x.js";import"./add-one-BP9Rv5dm.js";import"./Slider-DWNZsvrI.js";import"./headers-CSI__REg.js";import"./FormItem-B4P0FvHJ.js";const K={key:0},Q={class:"grid grid-cols-4 md:grid-cols-6 gap-4"},W=["onClick"],q={class:"transition-transform duration-300 hover:scale-110"},X=["src"],ee={class:"text-xs mt-2"},ae={class:"absolute -top-7 right-0 z-50"},te={class:"flex items-center"},se=["src"],oe=["src"],ve=V({__name:"index",setup(ne){const{t:le,locale:w}=F(),{userData:re}=P(),i=s(!1);function D(e){i.value=e}const _=s(null),p=s(!1);$(()=>{z(),O()});const y=s([]),n=s([]),x=s(0),v=s(!1),k=s("right"),G=e=>{v.value=!0,k.value=e},z=async()=>{const e=await g.get("demo/gamelist");try{y.value=e.data}catch{}},C=async e=>{n.value=e;const a={username:`demo_${_.value.username}`,productId:"PGSOFT",gameId:n.value.gameId,isMobileLogin:!0,externalURL:"www.demo.com",language:"th"};try{const u=await g.post("https://api.pgf-theks69.com/demo/logIn",a,{headers:{apikey:"ZGVtbzphNzY4YWFmOS1mMzA1LTU2OTktODFhNi1jZDgwOGJhNGU3ODA="},maxBodyLength:1/0});n.value.url=u.data.data.url,p.value=!0}catch(u){console.error("error:",u)}},S=()=>{x.value++},O=async()=>{try{const e=await g.get("PG/Profile");e.data&&(_.value=e.data)}catch(e){console.log(e)}};return s(""),(e,a)=>{const u=A,T=R,N=Y,U=Z,L=E,M=J;return _.value?(l(),m("div",K,[r(T,{vertical:"",size:"large"},{default:o(()=>[i.value?d("",!0):(l(),f(u,{key:0},{default:o(()=>[r(B,{onSave:D})]),_:1})),i.value?(l(),f(u,{key:1},{default:o(()=>[c("div",Q,[(l(!0),m(j,null,H(y.value,t=>(l(),m("div",{key:t.id,class:"cursor-pointer text-center",onClick:ce=>C(t)},[c("div",q,[c("img",{src:t.gameIcon,class:"w-24 h-24 object-contain mx-auto"},null,8,X)]),c("div",ee,b(I(w)=="thTH"?t.gameName_th:t.gameName_en),1)],8,W))),128))])]),_:1})):d("",!0)]),_:1}),r(U,{show:p.value,"onUpdate:show":a[1]||(a[1]=t=>p.value=t),"mask-closable":!1,preset:"card",class:"w-auto"},{header:o(()=>[c("div",te,[c("img",{src:n.value.gameIcon,class:"w-[40px]"},null,8,se),h(" ⠀"+b(I(w)=="thTH"?n.value.gameName_th:n.value.gameName_en),1)])]),default:o(()=>[c("div",ae,[r(N,{size:"small",class:"rounded-t-xl",type:"success",dashed:"",onClick:a[0]||(a[0]=t=>G("right"))},{default:o(()=>a[3]||(a[3]=[h("ตั้งค่า")])),_:1}),r(N,{size:"small",type:"warning",class:"rounded-t-xl ml-2 rounded-b-none",dashed:"",onClick:S},{default:o(()=>a[4]||(a[4]=[h("รีโหลด")])),_:1})]),n.value.url?(l(),m("iframe",{key:x.value,src:n.value.url,class:"w-[360px] h-[710px] mx-auto",frameborder:"0",allowfullscreen:""},null,8,oe)):d("",!0)]),_:1},8,["show"]),r(M,{show:v.value,"onUpdate:show":a[2]||(a[2]=t=>v.value=t),width:"30%",placement:k.value},{default:o(()=>[r(L,null,{default:o(()=>[i.value?(l(),f(B,{key:0})):d("",!0)]),_:1})]),_:1},8,["show","placement"])])):d("",!0)}}});export{ve as default};
