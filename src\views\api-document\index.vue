<script setup lang="tsx">
import { useAppStore } from "@/store";
// common
import docLogin from "./components/common/docLogin.vue";
import docGetGame from "./components/common/docGetGame.vue";
import docKillPlayer from "./components/common/docKillPlayer.vue";
import docGerBetRecord from "./components/common/docGerBetRecord.vue";
// wallet
import docGetBalance from "./components/wallet/docGetBalance.vue";
import docSettleBet from "./components/wallet/docSettleBet.vue";
const components = {
    docLogin,
    docGetGame,
    docKillPlayer,
    docGerBetRecord,
    docGetBalance,
    docSettleBet,
  }
import type { DrawerPlacement } from "naive-ui";
const appStore = useAppStore();
const active = ref(false);
const selectedDoc = ref("");
const selectedLabel = ref("");  
const placement = ref<DrawerPlacement>("right");
const activate = (place: DrawerPlacement , item:any) => {
  active.value = true;
  placement.value = place;
  selectedDoc.value = item.doc;
  selectedLabel.value = item.label;
};

const CommonAPI = ref([
  { label: "Login", id: 1, doc: "docLogin" },
  { label: "Get Game List", id: 2, doc: "docGetGame" },
  { label: "Kill Player", id: 3, doc: "docKillPlayer" },
  { label: "Get Bet Records", id: 4, doc: "docGerBetRecord" },
]);

const WalletAPI = ref([
  { label: "Get Balance", id: 1, doc: "docGetBalance" },
  { label: "Settle Bets", id: 2, doc: "docSettleBet" },
]);
</script>

<template>
  <div class="sm:flex gap-3">
    <div class="sm:w-2/4 xl:w-1/4 mb-2">
      <n-card>
        <div class="grid xl:grid-cols-1 gap-3">
          <div>
            <n-button class="cursor-default" size="tiny" type="info" tertiary
              >Common API Functions</n-button
            >
            <n-button
              v-for="item in CommonAPI"
              :key="item.id"
              @click="activate('right', item)"
              tertiary
              class="w-full mt-2"
            >
              {{ item.label }}
            </n-button>
          </div>
          <div>
            <n-button class="cursor-default" size="tiny" type="info" tertiary
              >Wallet Endpoint</n-button
            >
            <n-button  v-for="item in WalletAPI"
            :key="item.id" @click="activate('right', item)" tertiary class="w-full mt-2">
              {{item.label}}
            </n-button>
          </div>
        </div>
        <n-drawer
          v-model:show="active"
          :width="'60%'"
          :placement="placement"
          class="custom-drawer"
        >
          <n-drawer-content :title="$t('apidocument')">
            <template #header>
              <div class="flex justify-between items-center">
                {{ $t('apidocument')}} - {{ selectedLabel}}
                <n-button type="error" size="small" @click="active = false">
                  <icon-park-outline-right-small-down />
                </n-button>
              </div>
            </template>
            <!-- เนื้อหา -->
            <div>
              <component :is="components[selectedDoc]"/>
            </div>
          </n-drawer-content>
        </n-drawer>
      </n-card>
    </div>
    <div class="sm:w-3/4">
      <n-card>
        <n-image
          :src="
            appStore.storeColorMode === 'light'
              ? '/images/docapi/PGAPI.png'
              : '/images/docapi/PGAPI2.png'
          "
          alt="test image"
          :previewed-img-props="{
            style: {
              border: '8px solid ',
              borderRadius: '15px',
              backgroundColor:
                appStore.storeColorMode === 'light' ? '#ffffff' : '#18181c',
            },
          }"
        />
      </n-card>
    </div>
  </div>
</template>

<style>
@media (max-width: 768px) {
  .custom-drawer {
    width: 100% !important;
  }
}
</style>
