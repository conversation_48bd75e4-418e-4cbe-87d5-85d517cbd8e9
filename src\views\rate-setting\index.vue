<template>
  <div>
    <n-space vertical size="large" >
      <!-- Header Section -->
      <n-card>
        <n-space justify="space-between" align="center">
          <n-space align="center">
            <n-avatar size="large" color="#1a8a93">
              <icon-park-outline-exchange />
            </n-avatar>
            <div>
              <n-h2 style="margin: 0">{{ $t("rateSettingManagement") }}</n-h2>
              <n-text depth="3">Manage THB per USDT exchange rates</n-text>
            </div>
          </n-space>
          <n-button type="primary"  @click="showAddModal = true">
            <template #icon>
              <icon-park-outline-plus />
            </template>
            {{ $t("addNewRate") }}
          </n-button>
        </n-space>
      </n-card>

      <!-- Current Rate Display -->
      <n-card>
        <template #header>
          <n-space align="center">
            <icon-park-outline-chart-line class="text-xl text-green-500" />
            <span>{{ $t("currentRate") }}</span>
          </n-space>
        </template>

        <div v-if="currentRate" class="text-center">
          <n-statistic
            :value="parseFloat(currentRate.rateTHBPerUSDT?.toString() || '0')"
            :precision="2"
            class="mb-4"
          >
            <template #label>
              <n-space align="center" justify="center">
                <img
                  src="/images/country/th.webp"
                  alt="THB"
                  class="w-6 rounded"
                />
                <span>THB per USDT</span>
                <img
                  src="/images/country/us.webp"
                  alt="USDT"
                  class="w-6 rounded"
                />
              </n-space>
            </template>
            <template #suffix>
              <span class="text-lg">฿</span>
            </template>
          </n-statistic>

          <n-space justify="center" class="text-sm text-gray-500">
            <span
              >{{ $t("lastUpdated") }}:
              {{ formatDate(currentRate.updatedAt) }}</span
            >
            <n-divider vertical />
            <span
              >{{ $t("updatedBy") }}:
              {{
                currentRate.updatedByName ||
                currentRate.updatedByUsername ||
                "System"
              }}</span
            >
          </n-space>
        </div>

        <div v-else class="text-center py-8">
          <n-empty description="No rate setting found">
            <template #extra>
              <n-button type="primary" @click="showAddModal = true">
                {{ $t("addFirstRate") }}
              </n-button>
            </template>
          </n-empty>
        </div>
      </n-card>

      <!-- Rate History -->
      <n-card>
        <template #header>
          <n-space align="center" justify="space-between">
            <n-space align="center">
              <icon-park-outline-history class="text-xl text-blue-500" />
              <span>{{ $t("rateHistory") }}</span>
            </n-space>
            <n-button @click="loadRateHistory" :loading="loading">
              <template #icon>
                <icon-park-outline-refresh />
              </template>
              {{ $t("refresh") }}
            </n-button>
          </n-space>
        </template>

        <n-data-table
          :columns="columns"
          :data="rateHistory"
          :loading="loading"
          :pagination="pagination"
          :row-key="(row) => row._id"
          :scroll-x="1000"
          size="large"
        />
      </n-card>
    </n-space>
    <!-- Add/Edit Rate Modal -->
    <n-modal
      v-model:show="showAddModal"
      preset="dialog"
      :title="editingRate ? $t('editRate') : $t('addNewRate')"
    >
      <n-form
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="top"
      >
        <n-form-item :label="$t('scope')" path="scope">
          <n-select
            v-model:value="formModel.scope"
            :options="scopeOptions"
            size="large"
            :placeholder="$t('selectScope')"
          />
        </n-form-item>

        <n-form-item :label="$t('rateTHBPerUSDT')" path="rateTHBPerUSDT">
          <n-input-number
            v-model:value="formModel.rateTHBPerUSDT"
            :min="1"
            :max="100"
            :step="0.01"
            :precision="2"
            size="large"
            style="width: 100%"
            :placeholder="$t('enterRate')"
          >
            <template #prefix>
              <img
                src="/images/country/th.webp"
                alt="THB"
                class="w-4 rounded"
              />
            </template>
            <template #suffix>
              <span>฿ / USDT</span>
            </template>
          </n-input-number>
        </n-form-item>

        <n-form-item :label="$t('updatedByName')" path="updatedByName">
          <n-input
            v-model:value="formModel.updatedByName"
            size="large"
            :placeholder="$t('enterUpdaterName')"
          >
            <template #prefix>
              <icon-park-outline-user />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item :label="$t('updatedByUsername')" path="updatedByUsername">
          <n-input
            v-model:value="formModel.updatedByUsername"
            size="large"
            :placeholder="$t('enterUsername')"
          >
            <template #prefix>
              <span>@</span>
            </template>
          </n-input>
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">{{ $t("cancel") }}</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ editingRate ? $t("update") : $t("add") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Delete Confirmation Modal -->
    <n-modal
      v-model:show="showDeleteModal"
      preset="dialog"
      type="warning"
      :title="$t('confirmDelete')"
    >
      <span>{{ $t("deleteRateConfirmation") }}</span>
      <template #action>
        <n-space>
          <n-button @click="showDeleteModal = false">{{
            $t("cancel")
          }}</n-button>
          <n-button type="error" @click="handleDelete" :loading="deleting">
            {{ $t("delete") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="tsx">
import http from "@/service/axios";

const { t } = useI18n();
const message = useMessage();

const loading = ref(false);
const submitting = ref(false);
const deleting = ref(false);
const showAddModal = ref(false);
const showDeleteModal = ref(false);
const editingRate = ref(null);
const deletingRate = ref(null);
const currentRate = ref(
  // Sample data
  {
    _id: "1",
    scope: "global",
    rateTHBPerUSDT: 30.5,
    updatedByName: "John Doe",
    updatedByUsername: "johndoe",
    updatedAt: "2022-01-01T00:00:00.000Z",
  }
);
const rateHistory = ref([
  // Sample data
  {
    _id: "1",
    scope: "global",
    rateTHBPerUSDT: 30.5,
    updatedByName: "John Doe",
    updatedByUsername: "johndoe",
    updatedAt: "2022-01-01T00:00:00.000Z",
  },
  
]);

// Form data
const formRef = ref();
const formModel = ref({
  scope: "global",
  rateTHBPerUSDT: null,
  updatedByName: "",
  updatedByUsername: "",
});

// Scope options
const scopeOptions = [
  { label: "Global", value: "global" },
  { label: "Chat Group", value: "chat" },
];

// Form validation rules
const rules = {
  scope: {
    required: true,
    message: t("scopeRequired"),
    trigger: ["blur", "change"],
  },
  rateTHBPerUSDT: {
    required: true,
    type: "number",
    message: t("rateRequired"),
    trigger: ["blur", "change"],
  },
  updatedByName: {
    required: true,
    message: t("updaterNameRequired"),
    trigger: ["blur"],
  },
};

const columns = computed(() => [
  {
    title: t("no."),
    key: "index",
    align: "center",
    render: (row: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: t("scope"),
    key: "scope",
    align: "center",
    render: (row: any) => {
      return (
        <n-tag type={row.scope === "global" ? "success" : "info"}>
          {row.scope === "global" ? "Global" : "Chat Group"}
        </n-tag>
      );
    },
  },
  {
    title: t("rate"),
    key: "rateTHBPerUSDT",
    render: (row: any) => {
      return (
        <div class="flex items-center gap-2">
          <img src="/images/country/th.webp" alt="THB" class="w-5 rounded" />
          <span class="font-semibold text-lg">
            {parseFloat(row.rateTHBPerUSDT?.toString() || "0").toFixed(2)} ฿
          </span>
          <span class="text-gray-500">/ USDT</span>
        </div>
      );
    },
  },
  {
    title: t("updatedBy"),
    key: "updatedBy",
    render: (row: any) => {
      return (
        <div>
          <div class="font-medium">{row.updatedByName || "Unknown"}</div>
          {row.updatedByUsername && (
            <div class="text-sm text-gray-500">@{row.updatedByUsername}</div>
          )}
        </div>
      );
    },
  },
  {
    title: t("updatedAt"),
    key: "updatedAt",
    render: (row: any) => {
      return formatDate(row.updatedAt);
    },
  },
  {
    title: t("actions"),
    key: "actions",
    render: (row: any) => {
      return (
        <n-space>
          <n-button
            size="small"
            type="primary"
            secondary
            onClick={() => editRate(row)}
          >
            <icon-park-outline-edit />
          </n-button>
          <n-button
            size="small"
            type="error"
            secondary
            onClick={() => confirmDelete(row)}
          >
            <icon-park-outline-delete />
          </n-button>
        </n-space>
      );
    },
  },
]);

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
};

// Methods
const formatDate = (date: string) => {
  return new Date(date).toLocaleString();
};

const loadCurrentRate = async () => {
  try {
    const response = await http.get("/api/rate-settings/current");
    currentRate.value = response.data;
  } catch (error) {
    console.error("Failed to load current rate:", error);
  }
};

const loadRateHistory = async () => {
  loading.value = true;
  try {
    const response = await http.get("/api/rate-settings/history");
    rateHistory.value = response.data;
  } catch (error) {
    message.error(t("failedToLoadHistory"));
    console.error("Failed to load rate history:", error);
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  formModel.value = {
    scope: "global",
    rateTHBPerUSDT: null,
    updatedByName: "",
    updatedByUsername: "",
  };
  editingRate.value = null;
};

const editRate = (rate: any) => {
  editingRate.value = rate;
  formModel.value = {
    scope: rate.scope,
    rateTHBPerUSDT: parseFloat(rate.rateTHBPerUSDT?.toString() || "0"),
    updatedByName: rate.updatedByName || "",
    updatedByUsername: rate.updatedByUsername || "",
  };
  showAddModal.value = true;
};

const confirmDelete = (rate: any) => {
  deletingRate.value = rate;
  showDeleteModal.value = true;
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;

    const payload = {
      ...formModel.value,
      updatedAt: new Date(),
    };

    if (editingRate.value) {
      await http.put(`/api/rate-settings/${editingRate.value._id}`, payload);
      message.success(t("rateUpdatedSuccessfully"));
    } else {
      await http.post("/api/rate-settings", payload);
      message.success(t("rateAddedSuccessfully"));
    }

    showAddModal.value = false;
    resetForm();
    await loadCurrentRate();
    await loadRateHistory();
  } catch (error) {
    message.error(t("operationFailed"));
    console.error("Failed to submit rate:", error);
  } finally {
    submitting.value = false;
  }
};

const handleDelete = async () => {
  if (!deletingRate.value) return;

  deleting.value = true;
  try {
    await http.delete(`/api/rate-settings/${deletingRate.value._id}`);
    message.success(t("rateDeletedSuccessfully"));
    showDeleteModal.value = false;
    deletingRate.value = null;
    await loadCurrentRate();
    await loadRateHistory();
  } catch (error) {
    message.error(t("deleteFailed"));
    console.error("Failed to delete rate:", error);
  } finally {
    deleting.value = false;
  }
};

onMounted(async () => {
  await loadCurrentRate();
  await loadRateHistory();
});

watch(showAddModal, (newVal) => {
  if (!newVal) {
    resetForm();
  }
});
</script>
