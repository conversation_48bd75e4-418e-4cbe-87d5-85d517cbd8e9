<template>
  <div>
    <n-card>
      <template #header>
        <div
          class="flex flex-wrap sm:flex-nowrap justify-between items-center space-y-5 sm:space-y-0"
        >
          <div class="flex items-center gap-2">
            <nova-icon icon="icon-park-outline:setting" />
            <span class="text-lg font-medium">{{
              $t("baccarat.settings.title")
            }}</span>
          </div>
          <!-- Action Buttons -->
          <div class="flex gap-3">
            <n-button @click="resetSettings">
              <template #icon>
                <nova-icon icon="icon-park-outline:refresh" />
              </template>
              {{ $t("reset") }}
            </n-button>
            <n-button type="primary" @click="saveSettings">
              <template #icon>
                <nova-icon icon="icon-park-outline:check" />
              </template>
              {{ $t("save") }}
            </n-button>
          </div>
        </div>
      </template>

      <div class="flex flex-wrap lg:flex-nowrap gap-4">
        <!-- Player Section -->
        <n-card class="border-blue-500/30">
          <template #header>
            <div class="flex justify-center items-center gap-2">
              <n-tag type="info">P</n-tag>
              <span class="text-blue-400 font-medium">{{
                $t("baccarat.player.side")
              }}</span>
            </div>
          </template>

          <n-grid cols="1">
            <n-gi>
              <n-form-item :label="$t('baccarat.player.player')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.player"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="2.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.player.high')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.high"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="2.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.player.low')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.low"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="2.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.player.odd')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.odd"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="2.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.player.even')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.even"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="2.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.player.zero')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.zero"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="15.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.player.double')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.player.double"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="10.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- Tie Section -->
        <n-card class="border-green-500/30">
          <template #header>
            <div class="flex justify-center items-center gap-2">
              <n-tag type="success">T</n-tag>
              <span class="text-green-400 font-medium">{{
                $t("baccarat.tie.side")
              }}</span>
            </div>
          </template>

          <n-grid cols="1">
            <n-gi>
              <n-form-item :label="$t('baccarat.tie.tie')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.tie.tie"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="8.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.tie.zero')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.tie.zero"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="25.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- Banker Section -->
        <n-card class="border-red-500/30">
          <template #header>
            <div class="flex justify-center items-center gap-2">
              <n-tag type="error">B</n-tag>
              <span class="text-red-400 font-medium">{{
                $t("baccarat.banker.side")
              }}</span>
            </div>
          </template>

          <n-grid cols="1">
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.banker')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.banker"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="1.98"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.high')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.high"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="1.98"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.low')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.low"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="1.98"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.odd')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.odd"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="1.98"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.even')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.even"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="1.98"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.zero')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.zero"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="15.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="$t('baccarat.banker.double')">
                <n-input-number
                  class="w-full"
                  v-model:value="settings.banker.double"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  placeholder="10.00"
                >
                  <template #prefix>x</template>
                </n-input-number>
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="tsx">
import http from "@/service/axios";
const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();
const createDefaultSettings = () => ({
  player: {
    player: 2.0,
    high: 2.0,
    low: 2.0,
    odd: 2.0,
    even: 2.0,
    zero: 15.0,
    double: 10.0,
  },
  tie: {
    tie: 8.0,
    zero: 25.0,
  },
  banker: {
    banker: 1.98,
    high: 1.98,
    low: 1.98,
    odd: 1.98,
    even: 1.98,
    zero: 15.0,
    double: 10.0,
  },
});

const settings = ref(createDefaultSettings());

const resetSettings = () => {
  const d = dialog.warning({
    title: t("baccarat.settings.confirmReset"),
    content: t("baccarat.settings.resetWarning"),
    positiveText: t("confirm"),
    negativeText: t("cancel"),
    onPositiveClick: async () => {
      d.loading = true;
      try {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        settings.value = createDefaultSettings();
        message.info(t("baccarat.settings.reset"));
      } catch (error) {
        message.error(t("baccarat.settings.resetError"));
      } finally {
        d.loading = false;
      }
    },
  });
};

const saveSettings = () => {
  const d = dialog.warning({
    title: t("baccarat.settings.confirmSave"),
    content: t("baccarat.settings.saveWarning"),
    positiveText: t("confirm"),
    negativeText: t("cancel"),
    onPositiveClick: async () => {
      d.loading = true;
      try {
        const response = await http.post("baccarat/settings", settings.value);

        if (response.data.success) {
          message.success(t("baccarat.settings.saved"));
        } else {
          message.error(
            response.data.message || t("baccarat.settings.saveError")
          );
        }
      } catch (error) {
        console.error("Save settings error:", error);
        message.error(t("baccarat.settings.saveError"));
      } finally {
        d.loading = false;
      }
    },
  });
};
</script>
