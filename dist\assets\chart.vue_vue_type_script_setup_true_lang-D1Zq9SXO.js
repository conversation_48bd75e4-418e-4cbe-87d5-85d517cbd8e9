import{d as a,r as s,ai as o,aj as t,m as r,o as l}from"./index-pY9FjpQW.js";import"./index-Dx6UGq-y.js";const i={ref:"lineRef",class:"h-400px"},y=a({__name:"chart",setup(f){const e=s({tooltip:{trigger:"axis"},grid:{left:"2%",right:"2%",bottom:"0%",top:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["10:00","10:10","10:10","10:30","10:40","10:50"],axisTick:{show:!1},axisLine:{show:!1}},yAxis:{type:"value",splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}},series:[{name:"2",type:"line",z:3,showSymbol:!1,smoothMonotone:"x",lineStyle:{width:3,color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(59,102,246)"},{offset:1,color:"rgba(118,237,252)"}]},shadowBlur:4,shadowColor:"rgba(69,126,247,.2)",shadowOffsetY:4},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(227,233,250,.9)"},{offset:1,color:"rgba(248,251,252,.3)"}]}},smooth:!0,data:[20,56,17,40,68,42]},{name:"1",type:"line",showSymbol:!1,smoothMonotone:"x",lineStyle:{width:3,color:new o(0,0,0,1,[{offset:0,color:"rgba(255,84,108)"},{offset:1,color:"rgba(252,140,118)"}],!1),shadowBlur:4,shadowColor:"rgba(253,121,128,.2)",shadowOffsetY:4},areaStyle:{color:new o(0,0,0,1,[{offset:0,color:"rgba(255,84,108,.15)"},{offset:1,color:"rgba(252,140,118,0)"}],!1)},smooth:!0,data:[20,71,8,50,57,32]}]});return t("lineRef",e),(n,c)=>(l(),r("div",i,null,512))}});export{y as _};
