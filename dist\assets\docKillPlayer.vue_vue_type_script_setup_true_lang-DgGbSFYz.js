import{_ as y}from"./copy-BxkeU8Ds.js";import{d as v,r as b,ac as w,ad as k,m as $,o as B,b as o,ab as C,w as e,a as t,g as r,B as T,t as l,ae as P,$ as z}from"./index-pY9FjpQW.js";import{_ as j}from"./Table-DoHSPnrC.js";const q={class:"border border-orange rounded-lg p-2 mt-4"},K={class:"font-bold"},N={class:"border rounded-lg p-2 mt-4"},R={class:"mt-4 overflow-x-auto"},V={class:"w-1/4"},h={class:"w-1/4"},x={class:"w-1/4"},A={class:"w-1/4"},D={class:"mt-4"},I={class:"bg-black/70 rounded-lg px-4 pt-4"},S={class:"absolute right-0 mr-10"},U=v({__name:"docKillPlayer",setup(E){const i=b(null),{t:u}=w(),p=k(),_=()=>{const n=i.value?.innerText;n&&navigator.clipboard.writeText(n).then(()=>{p.success(u("copysuccess"))})};return(n,s)=>{const d=T,a=P,m=j,c=y,f=z,g=C;return B(),$("div",null,[o(g,null,{default:e(()=>[s[12]||(s[12]=t("div",{class:"font-bold"},"Kill Player",-1)),t("div",null,[o(d,{type:"success",round:"",size:"small",class:"mt-3 cursor-default"},{default:e(()=>[r(l(n.$t("update")),1)]),_:1}),r(" "+l(n.$t("lastupdate")),1)]),t("div",q,[o(d,{class:"cursor-default",type:"warning",round:"",size:"tiny"},{default:e(()=>s[0]||(s[0]=[r("POST")])),_:1}),s[1]||(s[1]=t("span",null," {{ API_URL }}/seamless/kickPlayer",-1))]),o(a),t("div",null,[t("div",K,l(n.$t("security")),1),t("div",N,[t("div",null,[o(d,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:e(()=>s[2]||(s[2]=[r("Authorization")])),_:1})]),s[4]||(s[4]=t("span",null," apiKey : Base64({{ agent_username }}:{{ secret_key }})",-1)),s[5]||(s[5]=t("hr",{class:"my-2"},null,-1)),t("div",null,[o(d,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:e(()=>s[3]||(s[3]=[r("Content Type")])),_:1})]),s[6]||(s[6]=t("span",null," Type: application/json",-1))])]),o(a),t("div",null,[s[10]||(s[10]=t("div",{class:"font-bold"},"Parameter Description",-1)),t("div",R,[o(m,null,{default:e(()=>[t("thead",null,[t("tr",null,[t("th",V,l(n.$t("property")),1),t("th",h,l(n.$t("type")),1),t("th",x,l(n.$t("required")),1),t("th",A,l(n.$t("description")),1)])]),t("tbody",null,[t("tr",null,[s[7]||(s[7]=t("td",null,"username",-1)),s[8]||(s[8]=t("td",null,"string",-1)),s[9]||(s[9]=t("td",null,"Required",-1)),t("td",null,l(n.$t("doc.kp1")),1)])])]),_:1})])]),t("div",D,[s[11]||(s[11]=t("div",{class:"font-bold mb-4"},"Request Body",-1)),t("div",I,[t("div",S,[o(f,{trigger:"hover"},{trigger:e(()=>[o(d,{class:"text-white",onClick:_},{default:e(()=>[o(c)]),_:1})]),default:e(()=>[r(" "+l(n.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:i,class:"font-normal text-white overflow-x-auto"},`{
    "username": "hw22850"
}
    `,512)])])]),_:1})])}}});export{U as _};
