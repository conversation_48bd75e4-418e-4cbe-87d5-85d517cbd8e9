import{p as G,q as t,d as q,s as He,v as Ee,x as Ae,y as we,z as pe,A as ee,C as T,D as ue,E as je,F as Ve,N as M,G as W,L as Ze,H as fe,I as We,T as ne,J as Ge,K as ve,r as w,M as Xe,O as Ye,P as te,Q as Y,R as Ce,S as be,U as Fe,V as xe,W as Ue,X as Ke,Y as qe,Z as Je,$ as Qe,a0 as en,a1 as nn,a2 as tn,a3 as he,a4 as me,a5 as on,a6 as ln,a7 as rn,m as K,o as N,a as D,u as an,b as z,w as I,B as sn,g as Z,a8 as ge,f as $,c as oe,t as F,a9 as dn,i as cn,aa as un,h as fn,ab as hn}from"./index-pY9FjpQW.js";import{_ as mn}from"./docLogin.vue_vue_type_script_setup_true_lang-D6SbAMom.js";import{_ as gn}from"./docGetGame.vue_vue_type_script_setup_true_lang-UKUJbRls.js";import{_ as wn}from"./docKillPlayer.vue_vue_type_script_setup_true_lang-DgGbSFYz.js";import{_ as pn}from"./docGerBetRecord.vue_vue_type_script_setup_true_lang-BJIOKFyH.js";import{_ as vn}from"./docGetBalance.vue_vue_type_script_setup_true_lang-D8-aMIWT.js";import{_ as Cn}from"./docSettleBet.vue_vue_type_script_setup_true_lang-Wal4ny8s.js";import{d as bn}from"./download-C2161hUv.js";import"./copy-BxkeU8Ds.js";import"./Table-DoHSPnrC.js";const xn=G("download",()=>t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M3.5,13 L12.5,13 C12.7761424,13 13,13.2238576 13,13.5 C13,13.7454599 12.8231248,13.9496084 12.5898756,13.9919443 L12.5,14 L3.5,14 C3.22385763,14 3,13.7761424 3,13.5 C3,13.2545401 3.17687516,13.0503916 3.41012437,13.0080557 L3.5,13 L12.5,13 L3.5,13 Z M7.91012437,1.00805567 L8,1 C8.24545989,1 8.44960837,1.17687516 8.49194433,1.41012437 L8.5,1.5 L8.5,10.292 L11.1819805,7.6109127 C11.3555469,7.43734635 11.6249713,7.4180612 11.8198394,7.55305725 L11.8890873,7.6109127 C12.0626536,7.78447906 12.0819388,8.05390346 11.9469427,8.2487716 L11.8890873,8.31801948 L8.35355339,11.8535534 C8.17998704,12.0271197 7.91056264,12.0464049 7.7156945,11.9114088 L7.64644661,11.8535534 L4.1109127,8.31801948 C3.91565056,8.12275734 3.91565056,7.80617485 4.1109127,7.6109127 C4.28447906,7.43734635 4.55390346,7.4180612 4.7487716,7.55305725 L4.81801948,7.6109127 L7.5,10.292 L7.5,1.5 C7.5,1.25454011 7.67687516,1.05039163 7.91012437,1.00805567 L8,1 L7.91012437,1.00805567 Z"}))))),_n=q({name:"ResizeSmall",render(){return t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},t("g",{fill:"none"},t("path",{d:"M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zM16 5.5A1.5 1.5 0 0 0 14.5 4h-1a.5.5 0 0 1 0-1h1A2.5 2.5 0 0 1 17 5.5v1a.5.5 0 0 1-1 0v-1zm0 9a1.5 1.5 0 0 1-1.5 1.5h-1a.5.5 0 0 0 0 1h1a2.5 2.5 0 0 0 2.5-2.5v-1a.5.5 0 0 0-1 0v1zm-12 0A1.5 1.5 0 0 0 5.5 16h1.25a.5.5 0 0 1 0 1H5.5A2.5 2.5 0 0 1 3 14.5v-1.25a.5.5 0 0 1 1 0v1.25zM8.5 7A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zM8 8.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-3z",fill:"currentColor"})))}}),Ln=G("rotateClockwise",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 12.7916 15.3658 15.2026 13 16.3265V14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5V17.5C12 17.7761 12.2239 18 12.5 18H15.5C15.7761 18 16 17.7761 16 17.5C16 17.2239 15.7761 17 15.5 17H13.8758C16.3346 15.6357 18 13.0128 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 10.2761 2.22386 10.5 2.5 10.5C2.77614 10.5 3 10.2761 3 10Z",fill:"currentColor"}),t("path",{d:"M10 12C11.1046 12 12 11.1046 12 10C12 8.89543 11.1046 8 10 8C8.89543 8 8 8.89543 8 10C8 11.1046 8.89543 12 10 12ZM10 11C9.44772 11 9 10.5523 9 10C9 9.44772 9.44772 9 10 9C10.5523 9 11 9.44772 11 10C11 10.5523 10.5523 11 10 11Z",fill:"currentColor"}))),Sn=G("rotateClockwise",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 12.7916 4.63419 15.2026 7 16.3265V14.5C7 14.2239 7.22386 14 7.5 14C7.77614 14 8 14.2239 8 14.5V17.5C8 17.7761 7.77614 18 7.5 18H4.5C4.22386 18 4 17.7761 4 17.5C4 17.2239 4.22386 17 4.5 17H6.12422C3.66539 15.6357 2 13.0128 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 10.2761 17.7761 10.5 17.5 10.5C17.2239 10.5 17 10.2761 17 10Z",fill:"currentColor"}),t("path",{d:"M10 12C8.89543 12 8 11.1046 8 10C8 8.89543 8.89543 8 10 8C11.1046 8 12 8.89543 12 10C12 11.1046 11.1046 12 10 12ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z",fill:"currentColor"}))),kn=G("zoomIn",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M11.5 8.5C11.5 8.22386 11.2761 8 11 8H9V6C9 5.72386 8.77614 5.5 8.5 5.5C8.22386 5.5 8 5.72386 8 6V8H6C5.72386 8 5.5 8.22386 5.5 8.5C5.5 8.77614 5.72386 9 6 9H8V11C8 11.2761 8.22386 11.5 8.5 11.5C8.77614 11.5 9 11.2761 9 11V9H11C11.2761 9 11.5 8.77614 11.5 8.5Z",fill:"currentColor"}),t("path",{d:"M8.5 3C11.5376 3 14 5.46243 14 8.5C14 9.83879 13.5217 11.0659 12.7266 12.0196L16.8536 16.1464C17.0488 16.3417 17.0488 16.6583 16.8536 16.8536C16.68 17.0271 16.4106 17.0464 16.2157 16.9114L16.1464 16.8536L12.0196 12.7266C11.0659 13.5217 9.83879 14 8.5 14C5.46243 14 3 11.5376 3 8.5C3 5.46243 5.46243 3 8.5 3ZM8.5 4C6.01472 4 4 6.01472 4 8.5C4 10.9853 6.01472 13 8.5 13C10.9853 13 13 10.9853 13 8.5C13 6.01472 10.9853 4 8.5 4Z",fill:"currentColor"}))),yn=G("zoomOut",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M11 8C11.2761 8 11.5 8.22386 11.5 8.5C11.5 8.77614 11.2761 9 11 9H6C5.72386 9 5.5 8.77614 5.5 8.5C5.5 8.22386 5.72386 8 6 8H11Z",fill:"currentColor"}),t("path",{d:"M14 8.5C14 5.46243 11.5376 3 8.5 3C5.46243 3 3 5.46243 3 8.5C3 11.5376 5.46243 14 8.5 14C9.83879 14 11.0659 13.5217 12.0196 12.7266L16.1464 16.8536L16.2157 16.9114C16.4106 17.0464 16.68 17.0271 16.8536 16.8536C17.0488 16.6583 17.0488 16.3417 16.8536 16.1464L12.7266 12.0196C13.5217 11.0659 14 9.83879 14 8.5ZM4 8.5C4 6.01472 6.01472 4 8.5 4C10.9853 4 13 6.01472 13 8.5C13 10.9853 10.9853 13 8.5 13C6.01472 13 4 10.9853 4 8.5Z",fill:"currentColor"})));function Pn(){return{toolbarIconColor:"rgba(255, 255, 255, .9)",toolbarColor:"rgba(0, 0, 0, .35)",toolbarBoxShadow:"none",toolbarBorderRadius:"24px"}}const On=He({name:"Image",common:Ae,peers:{Tooltip:Ee},self:Pn});function In(){return t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M6 5C5.75454 5 5.55039 5.17688 5.50806 5.41012L5.5 5.5V14.5C5.5 14.7761 5.72386 15 6 15C6.24546 15 6.44961 14.8231 6.49194 14.5899L6.5 14.5V5.5C6.5 5.22386 6.27614 5 6 5ZM13.8536 5.14645C13.68 4.97288 13.4106 4.9536 13.2157 5.08859L13.1464 5.14645L8.64645 9.64645C8.47288 9.82001 8.4536 10.0894 8.58859 10.2843L8.64645 10.3536L13.1464 14.8536C13.3417 15.0488 13.6583 15.0488 13.8536 14.8536C14.0271 14.68 14.0464 14.4106 13.9114 14.2157L13.8536 14.1464L9.70711 10L13.8536 5.85355C14.0488 5.65829 14.0488 5.34171 13.8536 5.14645Z",fill:"currentColor"}))}function Mn(){return t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M13.5 5C13.7455 5 13.9496 5.17688 13.9919 5.41012L14 5.5V14.5C14 14.7761 13.7761 15 13.5 15C13.2545 15 13.0504 14.8231 13.0081 14.5899L13 14.5V5.5C13 5.22386 13.2239 5 13.5 5ZM5.64645 5.14645C5.82001 4.97288 6.08944 4.9536 6.28431 5.08859L6.35355 5.14645L10.8536 9.64645C11.0271 9.82001 11.0464 10.0894 10.9114 10.2843L10.8536 10.3536L6.35355 14.8536C6.15829 15.0488 5.84171 15.0488 5.64645 14.8536C5.47288 14.68 5.4536 14.4106 5.58859 14.2157L5.64645 14.1464L9.79289 10L5.64645 5.85355C5.45118 5.65829 5.45118 5.34171 5.64645 5.14645Z",fill:"currentColor"}))}function zn(){return t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M4.089 4.216l.057-.07a.5.5 0 0 1 .638-.057l.07.057L10 9.293l5.146-5.147a.5.5 0 0 1 .638-.057l.07.057a.5.5 0 0 1 .057.638l-.057.07L10.707 10l5.147 5.146a.5.5 0 0 1 .057.638l-.057.07a.5.5 0 0 1-.638.057l-.07-.057L10 10.707l-5.146 5.147a.5.5 0 0 1-.638.057l-.07-.057a.5.5 0 0 1-.057-.638l.057-.07L9.293 10L4.146 4.854a.5.5 0 0 1-.057-.638l.057-.07l-.057.07z",fill:"currentColor"}))}const _e=Object.assign(Object.assign({},pe.props),{onPreviewPrev:Function,onPreviewNext:Function,showToolbar:{type:Boolean,default:!0},showToolbarTooltip:Boolean,renderToolbar:Function}),Le=we("n-image"),Rn=ee([ee("body >",[T("image-container","position: fixed;")]),T("image-preview-container",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 `),T("image-preview-overlay",`
 z-index: -1;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background: rgba(0, 0, 0, .3);
 `,[ue()]),T("image-preview-toolbar",`
 z-index: 1;
 position: absolute;
 left: 50%;
 transform: translateX(-50%);
 border-radius: var(--n-toolbar-border-radius);
 height: 48px;
 bottom: 40px;
 padding: 0 12px;
 background: var(--n-toolbar-color);
 box-shadow: var(--n-toolbar-box-shadow);
 color: var(--n-toolbar-icon-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 `,[T("base-icon",`
 padding: 0 8px;
 font-size: 28px;
 cursor: pointer;
 `),ue()]),T("image-preview-wrapper",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 pointer-events: none;
 `,[je()]),T("image-preview",`
 user-select: none;
 -webkit-user-select: none;
 pointer-events: all;
 margin: auto;
 max-height: calc(100vh - 32px);
 max-width: calc(100vw - 32px);
 transition: transform .3s var(--n-bezier);
 `),T("image",`
 display: inline-flex;
 max-height: 100%;
 max-width: 100%;
 `,[Ve("preview-disabled",`
 cursor: pointer;
 `),ee("img",`
 border-radius: inherit;
 `)])]),U=32,Bn=q({name:"ImagePreview",props:Object.assign(Object.assign({},_e),{onNext:Function,onPrev:Function,clsPrefix:{type:String,required:!0}}),setup(l){const h=pe("Image","-image",Rn,On,l,ve(l,"clsPrefix"));let a=null;const c=w(null),s=w(null),v=w(void 0),m=w(!1),g=w(!1),{localeRef:L}=Xe("Image");function i(){const{value:e}=s;if(!a||!e)return;const{style:o}=e,n=a.getBoundingClientRect(),u=n.left+n.width/2,f=n.top+n.height/2;o.transformOrigin=`${u}px ${f}px`}function r(e){var o,n;switch(e.key){case" ":e.preventDefault();break;case"ArrowLeft":(o=l.onPrev)===null||o===void 0||o.call(l);break;case"ArrowRight":(n=l.onNext)===null||n===void 0||n.call(l);break;case"Escape":se();break}}Ye(m,e=>{e?te("keydown",document,r):Y("keydown",document,r)}),Ce(()=>{Y("keydown",document,r)});let d=0,y=0,S=0,x=0,R=0,H=0,X=0,_=0,V=!1;function ie(e){const{clientX:o,clientY:n}=e;S=o-d,x=n-y,en(P)}function Se(e){const{mouseUpClientX:o,mouseUpClientY:n,mouseDownClientX:u,mouseDownClientY:f}=e,b=u-o,k=f-n,O=`vertical${k>0?"Top":"Bottom"}`,B=`horizontal${b>0?"Left":"Right"}`;return{moveVerticalDirection:O,moveHorizontalDirection:B,deltaHorizontal:b,deltaVertical:k}}function le(e){const{value:o}=c;if(!o)return{offsetX:0,offsetY:0};const n=o.getBoundingClientRect(),{moveVerticalDirection:u,moveHorizontalDirection:f,deltaHorizontal:b,deltaVertical:k}=e||{};let O=0,B=0;return n.width<=window.innerWidth?O=0:n.left>0?O=(n.width-window.innerWidth)/2:n.right<window.innerWidth?O=-(n.width-window.innerWidth)/2:f==="horizontalRight"?O=Math.min((n.width-window.innerWidth)/2,R-(b??0)):O=Math.max(-((n.width-window.innerWidth)/2),R-(b??0)),n.height<=window.innerHeight?B=0:n.top>0?B=(n.height-window.innerHeight)/2:n.bottom<window.innerHeight?B=-(n.height-window.innerHeight)/2:u==="verticalBottom"?B=Math.min((n.height-window.innerHeight)/2,H-(k??0)):B=Math.max(-((n.height-window.innerHeight)/2),H-(k??0)),{offsetX:O,offsetY:B}}function re(e){Y("mousemove",document,ie),Y("mouseup",document,re);const{clientX:o,clientY:n}=e;V=!1;const u=Se({mouseUpClientX:o,mouseUpClientY:n,mouseDownClientX:X,mouseDownClientY:_}),f=le(u);S=f.offsetX,x=f.offsetY,P()}const p=be(Le,null);function ke(e){var o,n;if((n=(o=p?.previewedImgPropsRef.value)===null||o===void 0?void 0:o.onMousedown)===null||n===void 0||n.call(o,e),e.button!==0)return;const{clientX:u,clientY:f}=e;V=!0,d=u-S,y=f-x,R=S,H=x,X=u,_=f,P(),te("mousemove",document,ie),te("mouseup",document,re)}const J=1.5;let E=0,C=1,A=0;function ye(e){var o,n;(n=(o=p?.previewedImgPropsRef.value)===null||o===void 0?void 0:o.onDblclick)===null||n===void 0||n.call(o,e);const u=ae();C=C===u?1:u,P()}function Q(){C=1,E=0}function Pe(){var e;Q(),A=0,(e=l.onPrev)===null||e===void 0||e.call(l)}function Oe(){var e;Q(),A=0,(e=l.onNext)===null||e===void 0||e.call(l)}function Ie(){A-=90,P()}function Me(){A+=90,P()}function ze(){const{value:e}=c;if(!e)return 1;const{innerWidth:o,innerHeight:n}=window,u=Math.max(1,e.naturalHeight/(n-U)),f=Math.max(1,e.naturalWidth/(o-U));return Math.max(3,u*2,f*2)}function ae(){const{value:e}=c;if(!e)return 1;const{innerWidth:o,innerHeight:n}=window,u=e.naturalHeight/(n-U),f=e.naturalWidth/(o-U);return u<1&&f<1?1:Math.max(u,f)}function Re(){const e=ze();C<e&&(E+=1,C=Math.min(e,Math.pow(J,E)),P())}function Be(){if(C>.5){const e=C;E-=1,C=Math.max(.5,Math.pow(J,E));const o=e-C;P(!1);const n=le();C+=o,P(!1),C-=o,S=n.offsetX,x=n.offsetY,P()}}function Te(){const e=v.value;e&&bn(e,void 0)}function P(e=!0){var o;const{value:n}=c;if(!n)return;const{style:u}=n,f=qe((o=p?.previewedImgPropsRef.value)===null||o===void 0?void 0:o.style);let b="";if(typeof f=="string")b=`${f};`;else for(const O in f)b+=`${Je(O)}: ${f[O]};`;const k=`transform-origin: center; transform: translateX(${S}px) translateY(${x}px) rotate(${A}deg) scale(${C});`;V?u.cssText=`${b}cursor: grabbing; transition: none;${k}`:u.cssText=`${b}cursor: grab;${k}${e?"":"transition: none;"}`,e||n.offsetHeight}function se(){m.value=!m.value,g.value=!0}function $e(){C=ae(),E=Math.ceil(Math.log(C)/Math.log(J)),S=0,x=0,P()}const De={setPreviewSrc:e=>{v.value=e},setThumbnailEl:e=>{a=e},toggleShow:se};function Ne(e,o){if(l.showToolbarTooltip){const{value:n}=h;return t(Qe,{to:!1,theme:n.peers.Tooltip,themeOverrides:n.peerOverrides.Tooltip,keepAliveOnHover:!1},{default:()=>L.value[o],trigger:()=>e})}else return e}const de=Fe(()=>{const{common:{cubicBezierEaseInOut:e},self:{toolbarIconColor:o,toolbarBorderRadius:n,toolbarBoxShadow:u,toolbarColor:f}}=h.value;return{"--n-bezier":e,"--n-toolbar-icon-color":o,"--n-toolbar-color":f,"--n-toolbar-border-radius":n,"--n-toolbar-box-shadow":u}}),{inlineThemeDisabled:ce}=xe(),j=ce?Ue("image-preview",void 0,de,l):void 0;return Object.assign({previewRef:c,previewWrapperRef:s,previewSrc:v,show:m,appear:Ke(),displayed:g,previewedImgProps:p?.previewedImgPropsRef,handleWheel(e){e.preventDefault()},handlePreviewMousedown:ke,handlePreviewDblclick:ye,syncTransformOrigin:i,handleAfterLeave:()=>{Q(),A=0,g.value=!1},handleDragStart:e=>{var o,n;(n=(o=p?.previewedImgPropsRef.value)===null||o===void 0?void 0:o.onDragstart)===null||n===void 0||n.call(o,e),e.preventDefault()},zoomIn:Re,zoomOut:Be,handleDownloadClick:Te,rotateCounterclockwise:Ie,rotateClockwise:Me,handleSwitchPrev:Pe,handleSwitchNext:Oe,withTooltip:Ne,resizeToOrignalImageSize:$e,cssVars:ce?void 0:de,themeClass:j?.themeClass,onRender:j?.onRender},De)},render(){var l,h;const{clsPrefix:a,renderToolbar:c,withTooltip:s}=this,v=s(t(M,{clsPrefix:a,onClick:this.handleSwitchPrev},{default:In}),"tipPrevious"),m=s(t(M,{clsPrefix:a,onClick:this.handleSwitchNext},{default:Mn}),"tipNext"),g=s(t(M,{clsPrefix:a,onClick:this.rotateCounterclockwise},{default:()=>t(Sn,null)}),"tipCounterclockwise"),L=s(t(M,{clsPrefix:a,onClick:this.rotateClockwise},{default:()=>t(Ln,null)}),"tipClockwise"),i=s(t(M,{clsPrefix:a,onClick:this.resizeToOrignalImageSize},{default:()=>t(_n,null)}),"tipOriginalSize"),r=s(t(M,{clsPrefix:a,onClick:this.zoomOut},{default:()=>t(yn,null)}),"tipZoomOut"),d=s(t(M,{clsPrefix:a,onClick:this.handleDownloadClick},{default:()=>t(xn,null)}),"tipDownload"),y=s(t(M,{clsPrefix:a,onClick:this.toggleShow},{default:zn}),"tipClose"),S=s(t(M,{clsPrefix:a,onClick:this.zoomIn},{default:()=>t(kn,null)}),"tipZoomIn");return t(W,null,(h=(l=this.$slots).default)===null||h===void 0?void 0:h.call(l),t(Ze,{show:this.show},{default:()=>{var x;return this.show||this.displayed?((x=this.onRender)===null||x===void 0||x.call(this),fe(t("div",{class:[`${a}-image-preview-container`,this.themeClass],style:this.cssVars,onWheel:this.handleWheel},t(ne,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?t("div",{class:`${a}-image-preview-overlay`,onClick:this.toggleShow}):null}),this.showToolbar?t(ne,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?t("div",{class:`${a}-image-preview-toolbar`},c?c({nodes:{prev:v,next:m,rotateCounterclockwise:g,rotateClockwise:L,resizeToOriginalSize:i,zoomOut:r,zoomIn:S,download:d,close:y}}):t(W,null,this.onPrev?t(W,null,v,m):null,g,L,i,r,S,d,y)):null}):null,t(ne,{name:"fade-in-scale-up-transition",onAfterLeave:this.handleAfterLeave,appear:this.appear,onEnter:this.syncTransformOrigin,onBeforeLeave:this.syncTransformOrigin},{default:()=>{const{previewedImgProps:R={}}=this;return fe(t("div",{class:`${a}-image-preview-wrapper`,ref:"previewWrapperRef"},t("img",Object.assign({},R,{draggable:!1,onMousedown:this.handlePreviewMousedown,onDblclick:this.handlePreviewDblclick,class:[`${a}-image-preview`,R.class],key:this.previewSrc,src:this.previewSrc,ref:"previewRef",onDragstart:this.handleDragStart}))),[[Ge,this.show]])}})),[[We,{enabled:this.show}]])):null}}))}}),Tn=we("n-image-group"),$n=Object.assign({alt:String,height:[String,Number],imgProps:Object,previewedImgProps:Object,lazy:Boolean,intersectionObserverOptions:Object,objectFit:{type:String,default:"fill"},previewSrc:String,fallbackSrc:String,width:[String,Number],src:String,previewDisabled:Boolean,loadDescription:String,onError:Function,onLoad:Function},_e),Dn=q({name:"Image",props:$n,slots:Object,inheritAttrs:!1,setup(l){const h=w(null),a=w(!1),c=w(null),s=be(Tn,null),{mergedClsPrefixRef:v}=s||xe(l),m={click:()=>{if(l.previewDisabled||a.value)return;const i=l.previewSrc||l.src;if(s){s.setPreviewSrc(i),s.setThumbnailEl(h.value),s.toggleShow();return}const{value:r}=c;r&&(r.setPreviewSrc(i),r.setThumbnailEl(h.value),r.toggleShow())}},g=w(!l.lazy);he(()=>{var i;(i=h.value)===null||i===void 0||i.setAttribute("data-group-id",s?.groupId||"")}),he(()=>{if(l.lazy&&l.intersectionObserverOptions){let i;const r=me(()=>{i?.(),i=void 0,i=on(h.value,l.intersectionObserverOptions,g)});Ce(()=>{r(),i?.()})}}),me(()=>{var i;l.src||((i=l.imgProps)===null||i===void 0||i.src),a.value=!1});const L=w(!1);return ln(Le,{previewedImgPropsRef:ve(l,"previewedImgProps")}),Object.assign({mergedClsPrefix:v,groupId:s?.groupId,previewInstRef:c,imageRef:h,showError:a,shouldStartLoading:g,loaded:L,mergedOnClick:i=>{var r,d;m.click(),(d=(r=l.imgProps)===null||r===void 0?void 0:r.onClick)===null||d===void 0||d.call(r,i)},mergedOnError:i=>{if(!g.value)return;a.value=!0;const{onError:r,imgProps:{onError:d}={}}=l;r?.(i),d?.(i)},mergedOnLoad:i=>{const{onLoad:r,imgProps:{onLoad:d}={}}=l;r?.(i),d?.(i),L.value=!0}},m)},render(){var l,h;const{mergedClsPrefix:a,imgProps:c={},loaded:s,$attrs:v,lazy:m}=this,g=nn(this.$slots.error,()=>[]),L=(h=(l=this.$slots).placeholder)===null||h===void 0?void 0:h.call(l),i=this.src||c.src,r=this.showError&&g.length?g:t("img",Object.assign(Object.assign({},c),{ref:"imageRef",width:this.width||c.width,height:this.height||c.height,src:this.showError?this.fallbackSrc:m&&this.intersectionObserverOptions?this.shouldStartLoading?i:void 0:i,alt:this.alt||c.alt,"aria-label":this.alt||c.alt,onClick:this.mergedOnClick,onError:this.mergedOnError,onLoad:this.mergedOnLoad,loading:tn&&m&&!this.intersectionObserverOptions?"lazy":"eager",style:[c.style||"",L&&!s?{height:"0",width:"0",visibility:"hidden"}:"",{objectFit:this.objectFit}],"data-error":this.showError,"data-preview-src":this.previewSrc||this.src}));return t("div",Object.assign({},v,{role:"none",class:[v.class,`${a}-image`,(this.previewDisabled||this.showError)&&`${a}-image--preview-disabled`]}),this.groupId?r:t(Bn,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:a,ref:"previewInstRef",showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip,renderToolbar:this.renderToolbar},{default:()=>r}),!s&&L)}}),Nn={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Hn(l,h){return N(),K("svg",Nn,h[0]||(h[0]=[D("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"m32.353 32.353l-17-17m17-.999v18h-18"},null,-1)]))}const En=rn({name:"icon-park-outline-right-small-down",render:Hn}),An={class:"sm:flex gap-3"},jn={class:"sm:w-2/4 xl:w-1/4 mb-2"},Vn={class:"grid xl:grid-cols-1 gap-3"},Zn={class:"flex justify-between items-center"},Wn={class:"sm:w-3/4"},nt=q({__name:"index",setup(l){const h={docLogin:mn,docGetGame:gn,docKillPlayer:wn,docGerBetRecord:pn,docGetBalance:vn,docSettleBet:Cn},a=an(),c=w(!1),s=w(""),v=w(""),m=w("right"),g=(r,d)=>{c.value=!0,m.value=r,s.value=d.doc,v.value=d.label},L=w([{label:"Login",id:1,doc:"docLogin"},{label:"Get Game List",id:2,doc:"docGetGame"},{label:"Kill Player",id:3,doc:"docKillPlayer"},{label:"Get Bet Records",id:4,doc:"docGerBetRecord"}]),i=w([{label:"Get Balance",id:1,doc:"docGetBalance"},{label:"Settle Bets",id:2,doc:"docSettleBet"}]);return(r,d)=>{const y=sn,S=En,x=un,R=dn,H=hn,X=Dn;return N(),K("div",An,[D("div",jn,[z(H,null,{default:I(()=>[D("div",Vn,[D("div",null,[z(y,{class:"cursor-default",size:"tiny",type:"info",tertiary:""},{default:I(()=>d[2]||(d[2]=[Z("Common API Functions")])),_:1}),(N(!0),K(W,null,ge($(L),_=>(N(),oe(y,{key:_.id,onClick:V=>g("right",_),tertiary:"",class:"w-full mt-2"},{default:I(()=>[Z(F(_.label),1)]),_:2},1032,["onClick"]))),128))]),D("div",null,[z(y,{class:"cursor-default",size:"tiny",type:"info",tertiary:""},{default:I(()=>d[3]||(d[3]=[Z("Wallet Endpoint")])),_:1}),(N(!0),K(W,null,ge($(i),_=>(N(),oe(y,{key:_.id,onClick:V=>g("right",_),tertiary:"",class:"w-full mt-2"},{default:I(()=>[Z(F(_.label),1)]),_:2},1032,["onClick"]))),128))])]),z(R,{show:$(c),"onUpdate:show":d[1]||(d[1]=_=>cn(c)?c.value=_:null),width:"60%",placement:$(m),class:"custom-drawer"},{default:I(()=>[z(x,{title:r.$t("apidocument")},{header:I(()=>[D("div",Zn,[Z(F(r.$t("apidocument"))+" - "+F($(v))+" ",1),z(y,{type:"error",size:"small",onClick:d[0]||(d[0]=_=>c.value=!1)},{default:I(()=>[z(S)]),_:1})])]),default:I(()=>[D("div",null,[(N(),oe(fn(h[$(s)])))])]),_:1},8,["title"])]),_:1},8,["show","placement"])]),_:1})]),D("div",Wn,[z(H,null,{default:I(()=>[z(X,{src:$(a).storeColorMode==="light"?"/images/docapi/PGAPI.png":"/images/docapi/PGAPI2.png",alt:"test image","previewed-img-props":{style:{border:"8px solid ",borderRadius:"15px",backgroundColor:$(a).storeColorMode==="light"?"#ffffff":"#18181c"}}},null,8,["src","previewed-img-props"])]),_:1})])])}}});export{nt as default};
