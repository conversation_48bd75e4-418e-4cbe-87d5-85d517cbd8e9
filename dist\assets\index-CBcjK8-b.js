import{d as Se,l as ke,ad as Ee,r as n,ac as Re,U as j,a3 as ye,fY as Ue,fQ as F,O as te,m,o as f,b as t,w as o,ab as Le,af as se,a as u,f as a,n as h,fD as Te,fT as Ce,t as c,B as ne,g as b,fZ as Be,i as L,G as We,a8 as Ae,c as oe,f_ as Ne,f$ as Pe,g0 as De,g1 as <PERSON>,g2 as Ke}from"./index-pY9FjpQW.js";import{_ as Ie,a as Me,b as Ve}from"./left-BmgoECzM.js";import{_ as Ge}from"./Form-_sVFK3VR.js";import{_ as Fe}from"./FormItem-B4P0FvHJ.js";const Oe={key:0,class:"mt-10"},ze={class:"w-full sm:w-1/2"},je={class:"text-center"},qe={class:"flex gap-3 mt-2"},Qe={key:0,class:"mt-2"},Ye={class:"text-center"},Ze={class:"flex gap-3 mt-2"},Je={key:0},Xe={key:1,class:"mt-10"},xe={key:2,class:"mt-10"},el={key:0,class:"mb-2"},ll={class:"sm:flex gap-3"},al={class:"text-nowrap mt-1"},tl={class:"text-nowrap mt-1"},sl={key:1,class:"mb-2"},nl={class:"sm:flex gap-3"},ol={class:"text-nowrap mt-1"},ul={class:"text-nowrap mt-1"},rl={key:2,class:"mb-2"},dl={class:"sm:flex gap-3"},il={class:"text-nowrap mt-1"},vl={class:"text-nowrap mt-1"},pl={key:1,class:"flex gap-3"},wl=Se({__name:"index",setup(cl){const ue=ke(),I=Ee(),p=n(1),re=n("process"),d=n([]),i=n({}),g=n(!1),A=n("RESELLER"),w=n("SEAMLESS"),de=n([{label:"THB",value:"THB"},{label:"KRW",value:"KRW"},{label:"USD",value:"USD"}]),O=n(),v=n({username:"",password:"",confirmPassword:"",seamlessInput:""}),{t:N}=Re(),ie=j(()=>({username:[{required:!0,message:N("validusername"),trigger:"blur"}],password:[{required:!0,message:N("validpassword"),trigger:"blur"}],confirmPassword:[{required:!0,message:N("validconfirmpassword"),trigger:"blur"},{validator:(l,e)=>e===v.value.password,message:N("validmatchpassword"),trigger:"blur"}]}));n("0.00%"),n(100),n([{whateverLabel:"0.00%",whateverValue:"0.00%"},{whateverLabel:"0.50%",whateverValue:"0.50%"},{whateverLabel:"1.00%",whateverValue:"1.00%"}]),n([{whateverLabel:"100.00%",whateverValue:100},{whateverLabel:"50.00%",whateverValue:50},{whateverLabel:"0.00%",whateverValue:0}]);const ve=j(()=>d.value.every(l=>i.value[l]>0));async function pe(){if(p.value===1){if(!await O.value?.validate())return;const e={username:v.value.username};await F.get("PG/Profile/CheckUser",{params:e}).then(r=>{r.data.success?p.value++:I.error(r.data.mes)})}else{if(p.value===2&&!ve.value||!d.value.length||_.value)return;p.value++}}function ce(){p.value>1&&p.value--}function fe(){p.value=1,A.value="RESELLER",g.value=!1,S.value=0,E.value=0,y.value=0,v.value={username:"",password:"",confirmPassword:"",seamlessInput:""},d.value=[],i.value={},Y(),Z()}const me=j(()=>q.value<=768),q=n(window.innerWidth);function Q(){q.value=window.innerWidth}ye(()=>{Y(),Z(),window.addEventListener("resize",Q)}),Ue(()=>{window.removeEventListener("resize",Q)});const M=n(0),V=n(0),G=n(0),P=n(0),D=n(0),H=n(0),$=n(0),S=n(0),k=n(0),E=n(0),R=n(0),y=n(0),Y=async()=>{const{data:l}=await F.get("PG/wallet/credit");M.value=l.credit_thb,V.value=l.credit_krw,G.value=l.credit_usd},Z=async()=>{const{data:l}=await F.get("PG/Profile/agent");l.success&&(P.value=l.data.hold_percent,D.value=l.data.hold_percent_krw,H.value=l.data.hold_percent_usd,$.value=l.data.hold_percent,k.value=l.data.hold_percent_krw,R.value=l.data.hold_percent_usd)},_=n(!1),_e=(l,e)=>{l=="THB"&&Number(e)>M.value||l=="KRW"&&Number(e)>V.value||l=="USD"&&Number(e)>G.value?_.value=!0:_.value=!1},T=l=>{try{const e=[];for(let r=0;r<=l;r+=.5)e.push({state:`${r.toFixed(2)}%`,abbr:r});return e}catch(e){return console.log(e),[]}},C=(l,e)=>{e=="THB"?l=="our"?$.value=P.value-S.value:S.value=P.value-$.value:e=="KRW"?l=="our"?k.value=D.value-E.value:E.value=D.value-k.value:e=="USD"&&(l=="our"?R.value=H.value-y.value:y.value=H.value-R.value)},ge=()=>{const l={username:v.value.username,password:v.value.password,position_type:g.value?3:2,bet_type:g.value&&w.value=="SEAMLESS"?1:g.value&&w.value=="TRANSFER"?2:null,currency:d.value,credit:i.value.THB?i.value.THB:0,credit_krw:i.value.KRW?i.value.KRW:0,credit_usd:i.value.USD?i.value.USD:0,callback_url:g.value&&w.value=="SEAMLESS"&&v.value.seamlessInput?v.value.seamlessInput:null,hold_percent:d.value.includes("THB")?$.value:0,our_percent:d.value.includes("THB")?S.value:0,hold_percentage_krw:d.value.includes("KRW")?k.value:0,our_percentage_krw:d.value.includes("KRW")?E.value:0,hold_percentage_usd:d.value.includes("USD")?R.value:0,our_percentage_usd:d.value.includes("USD")?y.value:0};F.post("PG/Account/Store",l).then(e=>{e.data.success?(I.success(e.data.mes),ue.push("/management/agent-list")):I.error(e.data.mes)})};return te(d,l=>{g.value&&l.length>1&&(I.error(N("onlyOneCurrencyForAgent")),d.value=[l[0]])}),te(d,(l,e)=>{l.forEach(r=>{r in i.value||(i.value[r]="")}),Object.keys(i.value).forEach(r=>{l.includes(r)||delete i.value[r],r&&(r=="THB"&&Number(i.value[r])>M.value||r=="KRW"&&Number(i.value[r])>V.value||r=="USD"&&Number(i.value[r])>G.value?_.value=!0:_.value=!1)})}),(l,e)=>{const r=Me,we=Ie,K=Ce,B=Fe,J=Ge,W=ne,X=se,z=Te,U=Be,x=Le,he=Ve,ee=He,be=Ke,le=ne,$e=se;return f(),m("div",null,[t($e,{vertical:"",size:"large"},{default:o(()=>[t(x,null,{default:o(()=>[t(X,{vertical:""},{default:o(()=>[t(we,{current:a(p),status:a(re),vertical:a(me)},{default:o(()=>[t(r,{title:l.$t("accountsetup"),description:l.$t("createagentinfomation")},null,8,["title","description"]),t(r,{title:l.$t("walletsetup"),description:l.$t("managewalletcredit")},null,8,["title","description"]),t(r,{title:l.$t("royaltysetting"),description:l.$t("configureproductroyalty")},null,8,["title","description"])]),_:1},8,["current","status","vertical"]),u("div",null,[a(p)===1?(f(),m("div",Oe,[t(z,null,{default:o(()=>[u("div",ze,[t(J,{ref_key:"formRef",ref:O,model:a(v),rules:a(ie),vertical:""},{default:o(()=>[t(B,{label:l.$t("username"),path:"username"},{default:o(()=>[t(K,{value:a(v).username,"onUpdate:value":e[0]||(e[0]=s=>a(v).username=s),placeholder:l.$t("username")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(B,{label:l.$t("password"),path:"password"},{default:o(()=>[t(K,{value:a(v).password,"onUpdate:value":e[1]||(e[1]=s=>a(v).password=s),type:"password",placeholder:l.$t("password")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(B,{label:l.$t("confirmpassword"),path:"confirmPassword"},{default:o(()=>[t(K,{value:a(v).confirmPassword,"onUpdate:value":e[2]||(e[2]=s=>a(v).confirmPassword=s),type:"password",placeholder:l.$t("confirmpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t(X,{class:"mx-auto"},{default:o(()=>[u("div",null,[u("p",je,c(l.$t("positiontype")),1),u("div",qe,[t(W,{class:"w-28",type:a(A)==="RESELLER"?"warning":"default",onClick:e[3]||(e[3]=s=>{A.value="RESELLER",g.value=!1})},{default:o(()=>[b(c(l.$t("reseller")),1)]),_:1},8,["type"]),t(W,{class:"w-28",type:a(A)==="AGENT"?"success":"default",onClick:e[4]||(e[4]=s=>{A.value="AGENT",g.value=!0,w.value="SEAMLESS",d.value=[]})},{default:o(()=>[b(c(l.$t("agent")),1)]),_:1},8,["type"])]),a(g)?(f(),m("div",Qe,[u("p",Ye,c(l.$t("bettype")),1),u("div",Ze,[t(W,{class:"w-28",type:a(w)==="SEAMLESS"?"error":"default",onClick:e[5]||(e[5]=s=>w.value="SEAMLESS")},{default:o(()=>e[22]||(e[22]=[b(" Seamless ")])),_:1},8,["type"]),t(W,{class:"w-28",type:a(w)==="TRANSFER"?"info":"default",onClick:e[6]||(e[6]=s=>w.value="TRANSFER")},{default:o(()=>e[23]||(e[23]=[b(" Tranfer ")])),_:1},8,["type"])]),a(w)==="SEAMLESS"?(f(),m("div",Je,[t(B,null,{default:o(()=>[t(K,{value:a(v).seamlessInput,"onUpdate:value":e[7]||(e[7]=s=>a(v).seamlessInput=s),placeholder:"Callback API"},null,8,["value"])]),_:1})])):h("",!0)])):h("",!0)])]),_:1})]),_:1})])):h("",!0),a(p)===2?(f(),m("div",Xe,[t(J,{ref_key:"formRef",ref:O},{default:o(()=>[t(B,{label:l.$t("walletcurrency")},{default:o(()=>[t(U,{value:a(d),"onUpdate:value":e[8]||(e[8]=s=>L(d)?d.value=s:null),multiple:"",options:a(de),placeholder:l.$t("selectcurrency")},null,8,["value","options","placeholder"])]),_:1},8,["label"]),t(z,{wrap:!1,size:"large"},{default:o(()=>[(f(!0),m(We,null,Ae(a(d),s=>(f(),oe(B,{key:s,label:`${s}`,feedback:`${l.$t("min")} 1, ${l.$t("max")} ${s=="THB"?a(M):s=="KRW"?a(V):a(G)}`},{default:o(()=>[t(K,{value:a(i)[s],"onUpdate:value":ae=>a(i)[s]=ae,type:"number",placeholder:l.$t("amount"),onInput:ae=>_e(s,a(i)[s])},null,8,["value","onUpdate:value","placeholder","onInput"])]),_:2},1032,["label","feedback"]))),128))]),_:1})]),_:1},512)])):h("",!0),a(p)===3?(f(),m("div",xe,[t(x,{title:l.$t("royaltysetting")},{default:o(()=>[a(d).includes("THB")?(f(),m("div",el,[e[24]||(e[24]=u("div",{class:"flex"},[u("p",null,"THB"),u("img",{class:"mx-2 rounded-sm",src:Ne,alt:"Language Icon",width:"30",height:"10"})],-1)),u("div",ll,[u("span",al,c(l.$t("ourpercentage")),1),t(U,{value:a(S),"onUpdate:value":[e[9]||(e[9]=s=>L(S)?S.value=s:null),e[10]||(e[10]=s=>C("our","THB"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("ourpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:T(a(P))},null,8,["value","placeholder","options"]),u("span",tl,c(l.$t("holdpercentage")),1),t(U,{value:a($),"onUpdate:value":[e[11]||(e[11]=s=>L($)?$.value=s:null),e[12]||(e[12]=s=>C("hold","THB"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("holdpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:T(a(P))},null,8,["value","placeholder","options"])])])):h("",!0),a(d).includes("KRW")?(f(),m("div",sl,[e[25]||(e[25]=u("div",{class:"flex"},[u("p",null,"KRW"),u("img",{class:"mx-2 rounded-sm",src:Pe,alt:"Language Icon",width:"30",height:"10"})],-1)),u("div",nl,[u("span",ol,c(l.$t("ourpercentage")),1),t(U,{value:a(E),"onUpdate:value":[e[13]||(e[13]=s=>L(E)?E.value=s:null),e[14]||(e[14]=s=>C("our","KRW"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("ourpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:T(a(D))},null,8,["value","placeholder","options"]),u("span",ul,c(l.$t("holdpercentage")),1),t(U,{value:a(k),"onUpdate:value":[e[15]||(e[15]=s=>L(k)?k.value=s:null),e[16]||(e[16]=s=>C("hold","KRW"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("holdpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:T(a(D))},null,8,["value","placeholder","options"])])])):h("",!0),a(d).includes("USD")?(f(),m("div",rl,[e[26]||(e[26]=u("div",{class:"flex"},[u("p",null,"USD"),u("img",{class:"mx-2 rounded-sm",src:De,alt:"Language Icon",width:"30",height:"10"})],-1)),u("div",dl,[u("span",il,c(l.$t("ourpercentage")),1),t(U,{value:a(y),"onUpdate:value":[e[17]||(e[17]=s=>L(y)?y.value=s:null),e[18]||(e[18]=s=>C("our","USD"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("ourpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:T(a(H))},null,8,["value","placeholder","options"]),u("span",vl,c(l.$t("holdpercentage")),1),t(U,{value:a(R),"onUpdate:value":[e[19]||(e[19]=s=>L(R)?R.value=s:null),e[20]||(e[20]=s=>C("hold","USD"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("holdpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:T(a(H))},null,8,["value","placeholder","options"])])])):h("",!0)]),_:1},8,["title"])])):h("",!0)])]),_:1}),t(z,{justify:"space-between",class:"mt-10"},{default:o(()=>[t(W,{ghost:"",disabled:a(p)===1,onClick:ce},{default:o(()=>[t(he),b(" "+c(l.$t("previous")),1)]),_:1},8,["disabled"]),a(p)<3?(f(),oe(W,{key:0,ghost:"",disabled:!a(d).length&&a(p)==2||a(_),onClick:pe},{default:o(()=>[b(c(l.$t("next"))+" ",1),t(ee)]),_:1},8,["disabled"])):(f(),m("div",pl,[t(le,{type:"warning",onClick:fe},{default:o(()=>[b(c(l.$t("reset")),1),t(be)]),_:1}),t(le,{type:"success",onClick:e[21]||(e[21]=s=>ge())},{default:o(()=>[b(c(l.$t("finish"))+" ",1),t(ee)]),_:1})]))]),_:1})]),_:1})]),_:1})])}}});export{wl as default};
