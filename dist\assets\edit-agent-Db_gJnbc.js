import{d as ke,l as Se,ad as Re,g4 as Ue,r as u,U as O,ac as Ee,a3 as Le,fY as Te,fQ as q,m as v,o as i,b as a,w as n,ab as Be,af as le,n as _,f as t,fD as Ce,a as o,fT as We,t as c,B as te,g as f,i as g,G as ae,a8 as se,c as I,fZ as Ae,f_ as De,f$ as Pe,g0 as He,g1 as Ie,g2 as Ke}from"./index-pY9FjpQW.js";import{_ as Ne,a as Ge,b as Me}from"./left-BmgoECzM.js";import{_ as Fe}from"./Form-_sVFK3VR.js";import{_ as ze}from"./FormItem-B4P0FvHJ.js";import{_ as Ve,a as je}from"./RadioGroup-DnXU-fFU.js";const Oe={key:0,class:"mt-10"},qe={class:"w-full sm:w-1/2"},Qe={class:"text-center"},Ye={class:"flex gap-3 mt-2"},Ze={key:0,class:"mt-2"},Je={class:"text-center"},Xe={class:"flex gap-3 mt-2"},xe={key:0},el={class:"mt-5 flex flex-wrap gap-x-10 gap-y-3 items-center justify-center"},ll={key:1,class:"mt-10"},tl={key:2,class:"mt-10"},al={key:0,class:"mb-2"},sl={class:"sm:flex gap-3"},nl={class:"text-nowrap mt-1"},ol={class:"text-nowrap mt-1"},ul={key:1,class:"mb-2"},rl={class:"sm:flex gap-3"},dl={class:"text-nowrap mt-1"},il={class:"text-nowrap mt-1"},pl={key:2,class:"mb-2"},cl={class:"sm:flex gap-3"},vl={class:"text-nowrap mt-1"},_l={class:"text-nowrap mt-1"},fl={key:1,class:"flex gap-3"},kl=ke({__name:"edit-agent",setup(ml){const Q=Se(),M=Re(),F=Ue().params.id,p=u(1),ne=u("process"),d=u([]),L=u({}),K=u(!1),N=u("RESELLER"),T=u("SEAMLESS"),oe=u([{label:"THB",value:"THB",total:"69,096,900"},{label:"KRW",value:"KRW",total:"958,998,000"},{label:"USD",value:"USD",total:"999,892,000"}]),ue=O(()=>[{value:1,label:D("active")},{value:3,label:D("inactive")},{value:2,label:D("suspend")}]),z=u(),r=u({username:"",password:"",confirmPassword:"",seamlessInput:""}),re=O(()=>Y.value<=768),Y=u(window.innerWidth);function Z(){Y.value=window.innerWidth}Le(()=>{J(),de(),window.addEventListener("resize",Z)}),Te(()=>{window.removeEventListener("resize",Z)});const B=u(0),C=u(0),W=u(0),w=u(0),b=u(0),h=u(0),$=u(0),y=u(0),k=u(0),A=u(null),J=async()=>{const l={id:F},{data:e}=await q.get("PG/Account/Getuser",{params:l});r.value.username=e.username,A.value=e.status,e.position_type==3?d.value.push(e.ag_currency):(e.reseller_thb&&d.value.push("THB"),e.reseller_krw&&d.value.push("KRW"),e.reseller_usd&&d.value.push("USD")),B.value=e.hold_percent+e.our_percent,C.value=e.hold_percent_krw+e.our_percent_krw,W.value=e.hold_percent_usd+e.our_percent_usd,w.value=e.hold_percent,h.value=e.hold_percent_krw,y.value=e.hold_percent_usd,b.value=e.our_percent,$.value=e.our_percent_krw,k.value=e.our_percent_usd,K.value=!1,e.position_type==2?(K.value=!1,N.value="RESELLER"):(K.value=!0,N.value="AGENT",e.bet_type==1?(T.value="SEAMLESS",r.value.seamlessInput=e.callback_url):T.value="TRANSFER")},de=async()=>{const l={id:F},{data:e}=await q.get("PG/wallet/agent/credit",{params:l});L.value.THB=e.credit_thb.toLocaleString(),L.value.KRW=e.credit_krw.toLocaleString(),L.value.USD=e.credit_usd.toLocaleString()},R=l=>{try{const e=[];for(let m=0;m<=l;m+=.5)e.push({state:`${m.toFixed(2)}%`,abbr:m});return e}catch(e){return console.log(e),[]}},U=(l,e)=>{e=="THB"?l=="our"?w.value=B.value-b.value:b.value=B.value-w.value:e=="KRW"?l=="our"?h.value=C.value-$.value:$.value=C.value-h.value:e=="USD"&&(l=="our"?y.value=W.value-k.value:k.value=W.value-y.value)},{t:D}=Ee(),ie=O(()=>({confirmPassword:[{required:!1,message:D("validconfirmpassword"),trigger:"blur"},{validator:(l,e)=>e===r.value.password,message:D("validmatchpassword"),trigger:"blur"}]}));async function pe(){p.value===1&&!await z.value?.validate()||p.value<3&&p.value++}function ce(){p.value>1&&p.value--}function ve(){J()}const _e=()=>{Q.push("/management/agent-list")};function fe(){if(p.value==1&&r.value.password&&!r.value.confirmPassword||!r.value.password&&r.value.confirmPassword)return M.error("กรุณากรอกข้อมูลให้ครบถ้วน");const l={agent_id:F,username:r.value.username,password:null,currency:d.value,hold_percentage:d.value.includes("THB")?w.value:0,our_percentage:d.value.includes("THB")?b.value:0,hold_percentage_krw:d.value.includes("KRW")?h.value:0,our_percentage_krw:d.value.includes("KRW")?$.value:0,hold_percentage_usd:d.value.includes("USD")?y.value:0,our_percentage_usd:d.value.includes("USD")?k.value:0};p.value==1&&(l.status=A.value,r.value.password&&r.value.confirmPassword&&(l.password=r.value.password)),q.post("PG/Account/update/agent",l).then(e=>{e.data.status?(M.success(e.data.mes),Q.push("/management/agent-list")):M.error(e.data.mes)})}return(l,e)=>{const m=Ge,me=Ne,P=We,E=ze,X=Fe,G=te,ge=je,V=le,we=Ve,j=Ce,S=Ae,x=Be,be=Me,H=te,ee=Ie,he=Ke,$e=le;return i(),v("div",null,[a($e,{vertical:"",size:"large"},{default:n(()=>[a(x,null,{default:n(()=>[a(V,{vertical:""},{default:n(()=>[a(me,{current:t(p),status:t(ne),vertical:t(re)},{default:n(()=>[a(m,{title:l.$t("accountsetup"),description:l.$t("createagentinfomation")},null,8,["title","description"]),a(m,{title:l.$t("walletsetup"),description:l.$t("managewalletcredit")},null,8,["title","description"]),a(m,{title:l.$t("royaltysetting"),description:l.$t("configureproductroyalty")},null,8,["title","description"])]),_:1},8,["current","status","vertical"]),t(p)===1?(i(),v("div",Oe,[a(j,null,{default:n(()=>[o("div",qe,[a(X,{ref_key:"formRef",ref:z,model:t(r),rules:t(ie),vertical:""},{default:n(()=>[a(E,{label:l.$t("username"),path:"username"},{default:n(()=>[a(P,{value:t(r).username,"onUpdate:value":e[0]||(e[0]=s=>t(r).username=s),disabled:"",placeholder:l.$t("username")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(E,{label:l.$t("password"),path:"password"},{default:n(()=>[a(P,{value:t(r).password,"onUpdate:value":e[1]||(e[1]=s=>t(r).password=s),type:"password",placeholder:l.$t("password")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(E,{label:l.$t("confirmpassword"),path:"confirmPassword"},{default:n(()=>[a(P,{value:t(r).confirmPassword,"onUpdate:value":e[2]||(e[2]=s=>t(r).confirmPassword=s),type:"password",placeholder:l.$t("confirmpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),a(V,{class:"mx-auto"},{default:n(()=>[o("div",null,[o("p",Qe,c(l.$t("positiontype")),1),o("div",Ye,[a(G,{class:"w-28",type:t(N)==="RESELLER"?"warning":"default",disabled:""},{default:n(()=>[f(c(l.$t("reseller")),1)]),_:1},8,["type"]),a(G,{class:"w-28",type:t(N)==="AGENT"?"success":"default",disabled:""},{default:n(()=>[f(c(l.$t("agent")),1)]),_:1},8,["type"])]),t(K)?(i(),v("div",Ze,[o("p",Je,c(l.$t("bettype")),1),o("div",Xe,[a(G,{class:"w-28",type:t(T)==="SEAMLESS"?"error":"default",disabled:""},{default:n(()=>e[19]||(e[19]=[f(" Seamless ")])),_:1},8,["type"]),a(G,{class:"w-28",type:t(T)==="TRANSFER"?"info":"default",disabled:""},{default:n(()=>e[20]||(e[20]=[f(" Tranfer ")])),_:1},8,["type"])]),t(T)==="SEAMLESS"?(i(),v("div",xe,[a(E,null,{default:n(()=>[a(P,{value:t(r).seamlessInput,"onUpdate:value":e[3]||(e[3]=s=>t(r).seamlessInput=s),placeholder:"Callback API",disabled:""},null,8,["value"])]),_:1})])):_("",!0)])):_("",!0),o("div",el,[o("p",null,c(l.$t("status")),1),o("div",null,[a(we,{value:t(A),"onUpdate:value":e[4]||(e[4]=s=>g(A)?A.value=s:null),class:""},{default:n(()=>[a(V,{vertical:""},{default:n(()=>[(i(!0),v(ae,null,se(t(ue),s=>(i(),I(ge,{key:s.value,value:s.value,label:s.label},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])])])])]),_:1})]),_:1})])):_("",!0),t(p)===2?(i(),v("div",ll,[a(X,{ref_key:"formRef",ref:z},{default:n(()=>[a(E,{label:l.$t("walletcurrency")},{default:n(()=>[a(S,{value:t(d),"onUpdate:value":e[5]||(e[5]=s=>g(d)?d.value=s:null),multiple:"",options:t(oe),placeholder:l.$t("selectcurrency"),disabled:""},null,8,["value","options","placeholder"])]),_:1},8,["label"]),a(j,{wrap:"false",size:"large"},{default:n(()=>[(i(!0),v(ae,null,se(t(d),s=>(i(),I(E,{key:s,label:`${s}`},{default:n(()=>[a(P,{value:t(L)[s],"onUpdate:value":ye=>t(L)[s]=ye,disabled:"",placeholder:l.$t("amount")},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label"]))),128))]),_:1})]),_:1},512)])):_("",!0),t(p)===3?(i(),v("div",tl,[a(x,{title:l.$t("royaltysetting")},{default:n(()=>[t(d).includes("THB")?(i(),v("div",al,[e[21]||(e[21]=o("div",{class:"flex"},[o("p",null,"THB"),o("img",{class:"mx-2 rounded-sm",src:De,alt:"Language Icon",width:"30",height:"10"})],-1)),o("div",sl,[o("span",nl,c(l.$t("ourpercentage")),1),a(S,{value:t(b),"onUpdate:value":[e[6]||(e[6]=s=>g(b)?b.value=s:null),e[7]||(e[7]=s=>U("our","THB"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("ourpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:R(t(B))},null,8,["value","placeholder","options"]),o("span",ol,c(l.$t("holdpercentage")),1),a(S,{value:t(w),"onUpdate:value":[e[8]||(e[8]=s=>g(w)?w.value=s:null),e[9]||(e[9]=s=>U("hold","THB"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("holdpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:R(t(B))},null,8,["value","placeholder","options"])])])):_("",!0),t(d).includes("KRW")?(i(),v("div",ul,[e[22]||(e[22]=o("div",{class:"flex"},[o("p",null,"KRW"),o("img",{class:"mx-2 rounded-sm",src:Pe,alt:"Language Icon",width:"30",height:"10"})],-1)),o("div",rl,[o("span",dl,c(l.$t("ourpercentage")),1),a(S,{value:t($),"onUpdate:value":[e[10]||(e[10]=s=>g($)?$.value=s:null),e[11]||(e[11]=s=>U("our","KRW"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("ourpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:R(t(C))},null,8,["value","placeholder","options"]),o("span",il,c(l.$t("holdpercentage")),1),a(S,{value:t(h),"onUpdate:value":[e[12]||(e[12]=s=>g(h)?h.value=s:null),e[13]||(e[13]=s=>U("hold","KRW"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("holdpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:R(t(C))},null,8,["value","placeholder","options"])])])):_("",!0),t(d).includes("USD")?(i(),v("div",pl,[e[23]||(e[23]=o("div",{class:"flex"},[o("p",null,"USD"),o("img",{class:"mx-2 rounded-sm",src:He,alt:"Language Icon",width:"30",height:"10"})],-1)),o("div",cl,[o("span",vl,c(l.$t("ourpercentage")),1),a(S,{value:t(k),"onUpdate:value":[e[14]||(e[14]=s=>g(k)?k.value=s:null),e[15]||(e[15]=s=>U("our","USD"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("ourpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:R(t(W))},null,8,["value","placeholder","options"]),o("span",_l,c(l.$t("holdpercentage")),1),a(S,{value:t(y),"onUpdate:value":[e[16]||(e[16]=s=>g(y)?y.value=s:null),e[17]||(e[17]=s=>U("hold","USD"))],"label-field":"state","value-field":"abbr",placeholder:l.$t("holdpercentage"),filterable:"",clearable:"","children-field":"whateverChildren",options:R(t(W))},null,8,["value","placeholder","options"])])])):_("",!0)]),_:1},8,["title"])])):_("",!0)]),_:1}),a(j,{justify:"space-between",class:"mt-10"},{default:n(()=>[a(H,{disabled:t(p)===1,onClick:ce},{default:n(()=>[a(be),f(" "+c(l.$t("previous")),1)]),_:1},8,["disabled"]),t(p)!==2?(i(),I(H,{key:0,type:"success",onClick:fe},{default:n(()=>[f(c(l.$t("save")),1)]),_:1})):_("",!0),t(p)<4?(i(),v("div",fl,[t(p)!==3?(i(),I(H,{key:0,onClick:pe},{default:n(()=>[f(c(l.$t("next"))+" ",1),a(ee)]),_:1})):_("",!0),t(p)==3?(i(),I(H,{key:1,type:"warning",onClick:ve},{default:n(()=>[f(c(l.$t("reset"))+" ",1),a(he)]),_:1})):_("",!0),a(H,{type:"success",onClick:e[18]||(e[18]=s=>_e())},{default:n(()=>[f(c(l.$t("finish"))+" ",1),a(ee)]),_:1})])):_("",!0)]),_:1})]),_:1})]),_:1})])}}});export{kl as default};
