import{d as X,q as l,r as A,gk as ut,gl as me,gm as pt,fF as vt,gn as ze,y as ht,S as $e,fx as gt,fJ as xt,go as mt,G as yt,N as wt,gp as Ct,U as Z,gq as St,C as n,ft as i,A as C,fu as T,F as Rt,fM as ne,fz as ye,fK as re,V as Tt,z as Pe,gr as zt,fW as we,gs as $t,O as oe,gt as ie,a3 as Pt,a6 as Wt,K as j,gu as _t,a4 as Et,fA as H,gv as J,W as Lt,gw as At,H as kt,J as Bt,gx as jt,fO as Ht,ga as Q}from"./index-pY9FjpQW.js";import{t as Ce}from"./toNumber-C1SyHx2r.js";const Ot=me(".v-x-scroll",{overflow:"auto",scrollbarWidth:"none"},[me("&::-webkit-scrollbar",{width:0,height:0})]),It=X({name:"XScroll",props:{disabled:<PERSON><PERSON><PERSON>,onScroll:Function},setup(){const e=A(null);function o(d){!(d.currentTarget.offsetWidth<d.currentTarget.scrollWidth)||d.deltaY===0||(d.currentTarget.scrollLeft+=d.deltaY+d.deltaX,d.preventDefault())}const c=ut();return Ot.mount({id:"vueuc/x-scroll",head:!0,anchorMetaName:pt,ssr:c}),Object.assign({selfRef:e,handleWheel:o},{scrollTo(...d){var m;(m=e.value)===null||m===void 0||m.scrollTo(...d)}})},render(){return l("div",{ref:"selfRef",onScroll:this.onScroll,onWheel:this.disabled?void 0:this.handleWheel,class:"v-x-scroll"},this.$slots)}});var se=function(){return vt.Date.now()},Ft="Expected a function",Mt=Math.max,Dt=Math.min;function Vt(e,o,c){var p,d,m,v,f,x,h=0,g=!1,_=!1,E=!0;if(typeof e!="function")throw new TypeError(Ft);o=Ce(o)||0,ze(c)&&(g=!!c.leading,_="maxWait"in c,m=_?Mt(Ce(c.maxWait)||0,o):m,E="trailing"in c?!!c.trailing:E);function y(b){var R=p,F=d;return p=d=void 0,h=b,v=e.apply(F,R),v}function z(b){return h=b,f=setTimeout(W,o),g?y(b):v}function S(b){var R=b-x,F=b-h,G=o-R;return _?Dt(G,m-F):G}function P(b){var R=b-x,F=b-h;return x===void 0||R>=o||R<0||_&&F>=m}function W(){var b=se();if(P(b))return $(b);f=setTimeout(W,S(b))}function $(b){return f=void 0,E&&p?y(b):(p=d=void 0,v)}function I(){f!==void 0&&clearTimeout(f),h=0,p=x=d=f=void 0}function L(){return f===void 0?v:$(se())}function u(){var b=se(),R=P(b);if(p=arguments,d=this,x=b,R){if(f===void 0)return z(x);if(_)return clearTimeout(f),f=setTimeout(W,o),y(x)}return f===void 0&&(f=setTimeout(W,o)),v}return u.cancel=I,u.flush=L,u}var Nt="Expected a function";function le(e,o,c){var p=!0,d=!0;if(typeof e!="function")throw new TypeError(Nt);return ze(c)&&(p="leading"in c?!!c.leading:p,d="trailing"in c?!!c.trailing:d),Vt(e,o,{leading:p,maxWait:o,trailing:d})}const Ut=X({name:"Add",render(){return l("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},l("path",{d:"M256 112V400M400 256H112",stroke:"currentColor","stroke-width":"32","stroke-linecap":"round","stroke-linejoin":"round"}))}}),be=ht("n-tabs"),We={tab:[String,Number,Object,Function],name:{type:[String,Number],required:!0},disabled:Boolean,displayDirective:{type:String,default:"if"},closable:{type:Boolean,default:void 0},tabProps:Object,label:[String,Number,Object,Function]},Jt=X({__TAB_PANE__:!0,name:"TabPane",alias:["TabPanel"],props:We,slots:Object,setup(e){const o=$e(be,null);return o||gt("tab-pane","`n-tab-pane` must be placed inside `n-tabs`."),{style:o.paneStyleRef,class:o.paneClassRef,mergedClsPrefix:o.mergedClsPrefixRef}},render(){return l("div",{class:[`${this.mergedClsPrefix}-tab-pane`,this.class],style:this.style},this.$slots)}}),Xt=Object.assign({internalLeftPadded:Boolean,internalAddable:Boolean,internalCreatedByPane:Boolean},St(We,["displayDirective"])),ce=X({__TAB__:!0,inheritAttrs:!1,name:"Tab",props:Xt,setup(e){const{mergedClsPrefixRef:o,valueRef:c,typeRef:p,closableRef:d,tabStyleRef:m,addTabStyleRef:v,tabClassRef:f,addTabClassRef:x,tabChangeIdRef:h,onBeforeLeaveRef:g,triggerRef:_,handleAdd:E,activateTab:y,handleClose:z}=$e(be);return{trigger:_,mergedClosable:Z(()=>{if(e.internalAddable)return!1;const{closable:S}=e;return S===void 0?d.value:S}),style:m,addStyle:v,tabClass:f,addTabClass:x,clsPrefix:o,value:c,type:p,handleClose(S){S.stopPropagation(),!e.disabled&&z(e.name)},activateTab(){if(e.disabled)return;if(e.internalAddable){E();return}const{name:S}=e,P=++h.id;if(S!==c.value){const{value:W}=g;W?Promise.resolve(W(e.name,c.value)).then($=>{$&&h.id===P&&y(S)}):y(S)}}}},render(){const{internalAddable:e,clsPrefix:o,name:c,disabled:p,label:d,tab:m,value:v,mergedClosable:f,trigger:x,$slots:{default:h}}=this,g=d??m;return l("div",{class:`${o}-tabs-tab-wrapper`},this.internalLeftPadded?l("div",{class:`${o}-tabs-tab-pad`}):null,l("div",Object.assign({key:c,"data-name":c,"data-disabled":p?!0:void 0},xt({class:[`${o}-tabs-tab`,v===c&&`${o}-tabs-tab--active`,p&&`${o}-tabs-tab--disabled`,f&&`${o}-tabs-tab--closable`,e&&`${o}-tabs-tab--addable`,e?this.addTabClass:this.tabClass],onClick:x==="click"?this.activateTab:void 0,onMouseenter:x==="hover"?this.activateTab:void 0,style:e?this.addStyle:this.style},this.internalCreatedByPane?this.tabProps||{}:this.$attrs)),l("span",{class:`${o}-tabs-tab__label`},e?l(yt,null,l("div",{class:`${o}-tabs-tab__height-placeholder`}," "),l(wt,{clsPrefix:o},{default:()=>l(Ut,null)})):h?h():typeof g=="object"?g:mt(g??c)),f&&this.type==="card"?l(Ct,{clsPrefix:o,class:`${o}-tabs-tab__close`,onClick:this.handleClose,disabled:p}):null))}}),Gt=n("tabs",`
 box-sizing: border-box;
 width: 100%;
 display: flex;
 flex-direction: column;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
`,[i("segment-type",[n("tabs-rail",[C("&.transition-disabled",[n("tabs-capsule",`
 transition: none;
 `)])])]),i("top",[n("tab-pane",`
 padding: var(--n-pane-padding-top) var(--n-pane-padding-right) var(--n-pane-padding-bottom) var(--n-pane-padding-left);
 `)]),i("left",[n("tab-pane",`
 padding: var(--n-pane-padding-right) var(--n-pane-padding-bottom) var(--n-pane-padding-left) var(--n-pane-padding-top);
 `)]),i("left, right",`
 flex-direction: row;
 `,[n("tabs-bar",`
 width: 2px;
 right: 0;
 transition:
 top .2s var(--n-bezier),
 max-height .2s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `),n("tabs-tab",`
 padding: var(--n-tab-padding-vertical); 
 `)]),i("right",`
 flex-direction: row-reverse;
 `,[n("tab-pane",`
 padding: var(--n-pane-padding-left) var(--n-pane-padding-top) var(--n-pane-padding-right) var(--n-pane-padding-bottom);
 `),n("tabs-bar",`
 left: 0;
 `)]),i("bottom",`
 flex-direction: column-reverse;
 justify-content: flex-end;
 `,[n("tab-pane",`
 padding: var(--n-pane-padding-bottom) var(--n-pane-padding-right) var(--n-pane-padding-top) var(--n-pane-padding-left);
 `),n("tabs-bar",`
 top: 0;
 `)]),n("tabs-rail",`
 position: relative;
 padding: 3px;
 border-radius: var(--n-tab-border-radius);
 width: 100%;
 background-color: var(--n-color-segment);
 transition: background-color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 `,[n("tabs-capsule",`
 border-radius: var(--n-tab-border-radius);
 position: absolute;
 pointer-events: none;
 background-color: var(--n-tab-color-segment);
 box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .08);
 transition: transform 0.3s var(--n-bezier);
 `),n("tabs-tab-wrapper",`
 flex-basis: 0;
 flex-grow: 1;
 display: flex;
 align-items: center;
 justify-content: center;
 `,[n("tabs-tab",`
 overflow: hidden;
 border-radius: var(--n-tab-border-radius);
 width: 100%;
 display: flex;
 align-items: center;
 justify-content: center;
 `,[i("active",`
 font-weight: var(--n-font-weight-strong);
 color: var(--n-tab-text-color-active);
 `),C("&:hover",`
 color: var(--n-tab-text-color-hover);
 `)])])]),i("flex",[n("tabs-nav",`
 width: 100%;
 position: relative;
 `,[n("tabs-wrapper",`
 width: 100%;
 `,[n("tabs-tab",`
 margin-right: 0;
 `)])])]),n("tabs-nav",`
 box-sizing: border-box;
 line-height: 1.5;
 display: flex;
 transition: border-color .3s var(--n-bezier);
 `,[T("prefix, suffix",`
 display: flex;
 align-items: center;
 `),T("prefix","padding-right: 16px;"),T("suffix","padding-left: 16px;")]),i("top, bottom",[n("tabs-nav-scroll-wrapper",[C("&::before",`
 top: 0;
 bottom: 0;
 left: 0;
 width: 20px;
 `),C("&::after",`
 top: 0;
 bottom: 0;
 right: 0;
 width: 20px;
 `),i("shadow-start",[C("&::before",`
 box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, .12);
 `)]),i("shadow-end",[C("&::after",`
 box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, .12);
 `)])])]),i("left, right",[n("tabs-nav-scroll-content",`
 flex-direction: column;
 `),n("tabs-nav-scroll-wrapper",[C("&::before",`
 top: 0;
 left: 0;
 right: 0;
 height: 20px;
 `),C("&::after",`
 bottom: 0;
 left: 0;
 right: 0;
 height: 20px;
 `),i("shadow-start",[C("&::before",`
 box-shadow: inset 0 10px 8px -8px rgba(0, 0, 0, .12);
 `)]),i("shadow-end",[C("&::after",`
 box-shadow: inset 0 -10px 8px -8px rgba(0, 0, 0, .12);
 `)])])]),n("tabs-nav-scroll-wrapper",`
 flex: 1;
 position: relative;
 overflow: hidden;
 `,[n("tabs-nav-y-scroll",`
 height: 100%;
 width: 100%;
 overflow-y: auto; 
 scrollbar-width: none;
 `,[C("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `)]),C("&::before, &::after",`
 transition: box-shadow .3s var(--n-bezier);
 pointer-events: none;
 content: "";
 position: absolute;
 z-index: 1;
 `)]),n("tabs-nav-scroll-content",`
 display: flex;
 position: relative;
 min-width: 100%;
 min-height: 100%;
 width: fit-content;
 box-sizing: border-box;
 `),n("tabs-wrapper",`
 display: inline-flex;
 flex-wrap: nowrap;
 position: relative;
 `),n("tabs-tab-wrapper",`
 display: flex;
 flex-wrap: nowrap;
 flex-shrink: 0;
 flex-grow: 0;
 `),n("tabs-tab",`
 cursor: pointer;
 white-space: nowrap;
 flex-wrap: nowrap;
 display: inline-flex;
 align-items: center;
 color: var(--n-tab-text-color);
 font-size: var(--n-tab-font-size);
 background-clip: padding-box;
 padding: var(--n-tab-padding);
 transition:
 box-shadow .3s var(--n-bezier),
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[i("disabled",{cursor:"not-allowed"}),T("close",`
 margin-left: 6px;
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `),T("label",`
 display: flex;
 align-items: center;
 z-index: 1;
 `)]),n("tabs-bar",`
 position: absolute;
 bottom: 0;
 height: 2px;
 border-radius: 1px;
 background-color: var(--n-bar-color);
 transition:
 left .2s var(--n-bezier),
 max-width .2s var(--n-bezier),
 opacity .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `,[C("&.transition-disabled",`
 transition: none;
 `),i("disabled",`
 background-color: var(--n-tab-text-color-disabled)
 `)]),n("tabs-pane-wrapper",`
 position: relative;
 overflow: hidden;
 transition: max-height .2s var(--n-bezier);
 `),n("tab-pane",`
 color: var(--n-pane-text-color);
 width: 100%;
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .2s var(--n-bezier);
 left: 0;
 right: 0;
 top: 0;
 `,[C("&.next-transition-leave-active, &.prev-transition-leave-active, &.next-transition-enter-active, &.prev-transition-enter-active",`
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 transform .2s var(--n-bezier),
 opacity .2s var(--n-bezier);
 `),C("&.next-transition-leave-active, &.prev-transition-leave-active",`
 position: absolute;
 `),C("&.next-transition-enter-from, &.prev-transition-leave-to",`
 transform: translateX(32px);
 opacity: 0;
 `),C("&.next-transition-leave-to, &.prev-transition-enter-from",`
 transform: translateX(-32px);
 opacity: 0;
 `),C("&.next-transition-leave-from, &.next-transition-enter-to, &.prev-transition-leave-from, &.prev-transition-enter-to",`
 transform: translateX(0);
 opacity: 1;
 `)]),n("tabs-tab-pad",`
 box-sizing: border-box;
 width: var(--n-tab-gap);
 flex-grow: 0;
 flex-shrink: 0;
 `),i("line-type, bar-type",[n("tabs-tab",`
 font-weight: var(--n-tab-font-weight);
 box-sizing: border-box;
 vertical-align: bottom;
 `,[C("&:hover",{color:"var(--n-tab-text-color-hover)"}),i("active",`
 color: var(--n-tab-text-color-active);
 font-weight: var(--n-tab-font-weight-active);
 `),i("disabled",{color:"var(--n-tab-text-color-disabled)"})])]),n("tabs-nav",[i("line-type",[i("top",[T("prefix, suffix",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),n("tabs-nav-scroll-content",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),n("tabs-bar",`
 bottom: -1px;
 `)]),i("left",[T("prefix, suffix",`
 border-right: 1px solid var(--n-tab-border-color);
 `),n("tabs-nav-scroll-content",`
 border-right: 1px solid var(--n-tab-border-color);
 `),n("tabs-bar",`
 right: -1px;
 `)]),i("right",[T("prefix, suffix",`
 border-left: 1px solid var(--n-tab-border-color);
 `),n("tabs-nav-scroll-content",`
 border-left: 1px solid var(--n-tab-border-color);
 `),n("tabs-bar",`
 left: -1px;
 `)]),i("bottom",[T("prefix, suffix",`
 border-top: 1px solid var(--n-tab-border-color);
 `),n("tabs-nav-scroll-content",`
 border-top: 1px solid var(--n-tab-border-color);
 `),n("tabs-bar",`
 top: -1px;
 `)]),T("prefix, suffix",`
 transition: border-color .3s var(--n-bezier);
 `),n("tabs-nav-scroll-content",`
 transition: border-color .3s var(--n-bezier);
 `),n("tabs-bar",`
 border-radius: 0;
 `)]),i("card-type",[T("prefix, suffix",`
 transition: border-color .3s var(--n-bezier);
 `),n("tabs-pad",`
 flex-grow: 1;
 transition: border-color .3s var(--n-bezier);
 `),n("tabs-tab-pad",`
 transition: border-color .3s var(--n-bezier);
 `),n("tabs-tab",`
 font-weight: var(--n-tab-font-weight);
 border: 1px solid var(--n-tab-border-color);
 background-color: var(--n-tab-color);
 box-sizing: border-box;
 position: relative;
 vertical-align: bottom;
 display: flex;
 justify-content: space-between;
 font-size: var(--n-tab-font-size);
 color: var(--n-tab-text-color);
 `,[i("addable",`
 padding-left: 8px;
 padding-right: 8px;
 font-size: 16px;
 justify-content: center;
 `,[T("height-placeholder",`
 width: 0;
 font-size: var(--n-tab-font-size);
 `),Rt("disabled",[C("&:hover",`
 color: var(--n-tab-text-color-hover);
 `)])]),i("closable","padding-right: 8px;"),i("active",`
 background-color: #0000;
 font-weight: var(--n-tab-font-weight-active);
 color: var(--n-tab-text-color-active);
 `),i("disabled","color: var(--n-tab-text-color-disabled);")])]),i("left, right",`
 flex-direction: column; 
 `,[T("prefix, suffix",`
 padding: var(--n-tab-padding-vertical);
 `),n("tabs-wrapper",`
 flex-direction: column;
 `),n("tabs-tab-wrapper",`
 flex-direction: column;
 `,[n("tabs-tab-pad",`
 height: var(--n-tab-gap-vertical);
 width: 100%;
 `)])]),i("top",[i("card-type",[n("tabs-scroll-padding","border-bottom: 1px solid var(--n-tab-border-color);"),T("prefix, suffix",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),n("tabs-tab",`
 border-top-left-radius: var(--n-tab-border-radius);
 border-top-right-radius: var(--n-tab-border-radius);
 `,[i("active",`
 border-bottom: 1px solid #0000;
 `)]),n("tabs-tab-pad",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),n("tabs-pad",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `)])]),i("left",[i("card-type",[n("tabs-scroll-padding","border-right: 1px solid var(--n-tab-border-color);"),T("prefix, suffix",`
 border-right: 1px solid var(--n-tab-border-color);
 `),n("tabs-tab",`
 border-top-left-radius: var(--n-tab-border-radius);
 border-bottom-left-radius: var(--n-tab-border-radius);
 `,[i("active",`
 border-right: 1px solid #0000;
 `)]),n("tabs-tab-pad",`
 border-right: 1px solid var(--n-tab-border-color);
 `),n("tabs-pad",`
 border-right: 1px solid var(--n-tab-border-color);
 `)])]),i("right",[i("card-type",[n("tabs-scroll-padding","border-left: 1px solid var(--n-tab-border-color);"),T("prefix, suffix",`
 border-left: 1px solid var(--n-tab-border-color);
 `),n("tabs-tab",`
 border-top-right-radius: var(--n-tab-border-radius);
 border-bottom-right-radius: var(--n-tab-border-radius);
 `,[i("active",`
 border-left: 1px solid #0000;
 `)]),n("tabs-tab-pad",`
 border-left: 1px solid var(--n-tab-border-color);
 `),n("tabs-pad",`
 border-left: 1px solid var(--n-tab-border-color);
 `)])]),i("bottom",[i("card-type",[n("tabs-scroll-padding","border-top: 1px solid var(--n-tab-border-color);"),T("prefix, suffix",`
 border-top: 1px solid var(--n-tab-border-color);
 `),n("tabs-tab",`
 border-bottom-left-radius: var(--n-tab-border-radius);
 border-bottom-right-radius: var(--n-tab-border-radius);
 `,[i("active",`
 border-top: 1px solid #0000;
 `)]),n("tabs-tab-pad",`
 border-top: 1px solid var(--n-tab-border-color);
 `),n("tabs-pad",`
 border-top: 1px solid var(--n-tab-border-color);
 `)])])])]),Kt=Object.assign(Object.assign({},Pe.props),{value:[String,Number],defaultValue:[String,Number],trigger:{type:String,default:"click"},type:{type:String,default:"bar"},closable:Boolean,justifyContent:String,size:{type:String,default:"medium"},placement:{type:String,default:"top"},tabStyle:[String,Object],tabClass:String,addTabStyle:[String,Object],addTabClass:String,barWidth:Number,paneClass:String,paneStyle:[String,Object],paneWrapperClass:String,paneWrapperStyle:[String,Object],addable:[Boolean,Object],tabsPadding:{type:Number,default:0},animated:Boolean,onBeforeLeave:Function,onAdd:Function,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onClose:[Function,Array],labelSize:String,activeName:[String,Number],onActiveNameChange:[Function,Array]}),Qt=X({name:"Tabs",props:Kt,slots:Object,setup(e,{slots:o}){var c,p,d,m;const{mergedClsPrefixRef:v,inlineThemeDisabled:f}=Tt(e),x=Pe("Tabs","-tabs",Gt,zt,e,v),h=A(null),g=A(null),_=A(null),E=A(null),y=A(null),z=A(null),S=A(!0),P=A(!0),W=we(e,["labelSize","size"]),$=we(e,["activeName","value"]),I=A((p=(c=$.value)!==null&&c!==void 0?c:e.defaultValue)!==null&&p!==void 0?p:o.default?(m=(d=ne(o.default())[0])===null||d===void 0?void 0:d.props)===null||m===void 0?void 0:m.name:null),L=$t($,I),u={id:0},b=Z(()=>{if(!(!e.justifyContent||e.type==="card"))return{display:"flex",justifyContent:e.justifyContent}});oe(L,()=>{u.id=0,K(),ue()});function R(){var t;const{value:a}=L;return a===null?null:(t=h.value)===null||t===void 0?void 0:t.querySelector(`[data-name="${a}"]`)}function F(t){if(e.type==="card")return;const{value:a}=g;if(!a)return;const r=a.style.opacity==="0";if(t){const s=`${v.value}-tabs-bar--disabled`,{barWidth:w,placement:k}=e;if(t.dataset.disabled==="true"?a.classList.add(s):a.classList.remove(s),["top","bottom"].includes(k)){if(fe(["top","maxHeight","height"]),typeof w=="number"&&t.offsetWidth>=w){const B=Math.floor((t.offsetWidth-w)/2)+t.offsetLeft;a.style.left=`${B}px`,a.style.maxWidth=`${w}px`}else a.style.left=`${t.offsetLeft}px`,a.style.maxWidth=`${t.offsetWidth}px`;a.style.width="8192px",r&&(a.style.transition="none"),a.offsetWidth,r&&(a.style.transition="",a.style.opacity="1")}else{if(fe(["left","maxWidth","width"]),typeof w=="number"&&t.offsetHeight>=w){const B=Math.floor((t.offsetHeight-w)/2)+t.offsetTop;a.style.top=`${B}px`,a.style.maxHeight=`${w}px`}else a.style.top=`${t.offsetTop}px`,a.style.maxHeight=`${t.offsetHeight}px`;a.style.height="8192px",r&&(a.style.transition="none"),a.offsetHeight,r&&(a.style.transition="",a.style.opacity="1")}}}function G(){if(e.type==="card")return;const{value:t}=g;t&&(t.style.opacity="0")}function fe(t){const{value:a}=g;if(a)for(const r of t)a.style[r]=""}function K(){if(e.type==="card")return;const t=R();t?F(t):G()}function ue(){var t;const a=(t=y.value)===null||t===void 0?void 0:t.$el;if(!a)return;const r=R();if(!r)return;const{scrollLeft:s,offsetWidth:w}=a,{offsetLeft:k,offsetWidth:B}=r;s>k?a.scrollTo({top:0,left:k,behavior:"smooth"}):k+B>s+w&&a.scrollTo({top:0,left:k+B-w,behavior:"smooth"})}const q=A(null);let ee=0,O=null;function _e(t){const a=q.value;if(a){ee=t.getBoundingClientRect().height;const r=`${ee}px`,s=()=>{a.style.height=r,a.style.maxHeight=r};O?(s(),O(),O=null):O=s}}function Ee(t){const a=q.value;if(a){const r=t.getBoundingClientRect().height,s=()=>{document.body.offsetHeight,a.style.maxHeight=`${r}px`,a.style.height=`${Math.max(ee,r)}px`};O?(O(),O=null,s()):O=s}}function Le(){const t=q.value;if(t){t.style.maxHeight="",t.style.height="";const{paneWrapperStyle:a}=e;if(typeof a=="string")t.style.cssText=a;else if(a){const{maxHeight:r,height:s}=a;r!==void 0&&(t.style.maxHeight=r),s!==void 0&&(t.style.height=s)}}}const pe={value:[]},ve=A("next");function Ae(t){const a=L.value;let r="next";for(const s of pe.value){if(s===a)break;if(s===t){r="prev";break}}ve.value=r,ke(t)}function ke(t){const{onActiveNameChange:a,onUpdateValue:r,"onUpdate:value":s}=e;a&&Q(a,t),r&&Q(r,t),s&&Q(s,t),I.value=t}function Be(t){const{onClose:a}=e;a&&Q(a,t)}function he(){const{value:t}=g;if(!t)return;const a="transition-disabled";t.classList.add(a),K(),t.classList.remove(a)}const M=A(null);function te({transitionDisabled:t}){const a=h.value;if(!a)return;t&&a.classList.add("transition-disabled");const r=R();r&&M.value&&(M.value.style.width=`${r.offsetWidth}px`,M.value.style.height=`${r.offsetHeight}px`,M.value.style.transform=`translateX(${r.offsetLeft-At(getComputedStyle(a).paddingLeft)}px)`,t&&M.value.offsetWidth),t&&a.classList.remove("transition-disabled")}oe([L],()=>{e.type==="segment"&&ie(()=>{te({transitionDisabled:!1})})}),Pt(()=>{e.type==="segment"&&te({transitionDisabled:!0})});let ge=0;function je(t){var a;if(t.contentRect.width===0&&t.contentRect.height===0||ge===t.contentRect.width)return;ge=t.contentRect.width;const{type:r}=e;if((r==="line"||r==="bar")&&he(),r!=="segment"){const{placement:s}=e;ae((s==="top"||s==="bottom"?(a=y.value)===null||a===void 0?void 0:a.$el:z.value)||null)}}const He=le(je,64);oe([()=>e.justifyContent,()=>e.size],()=>{ie(()=>{const{type:t}=e;(t==="line"||t==="bar")&&he()})});const D=A(!1);function Oe(t){var a;const{target:r,contentRect:{width:s,height:w}}=t,k=r.parentElement.parentElement.offsetWidth,B=r.parentElement.parentElement.offsetHeight,{placement:N}=e;if(!D.value)N==="top"||N==="bottom"?k<s&&(D.value=!0):B<w&&(D.value=!0);else{const{value:U}=E;if(!U)return;N==="top"||N==="bottom"?k-s>U.$el.offsetWidth&&(D.value=!1):B-w>U.$el.offsetHeight&&(D.value=!1)}ae(((a=y.value)===null||a===void 0?void 0:a.$el)||null)}const Ie=le(Oe,64);function Fe(){const{onAdd:t}=e;t&&t(),ie(()=>{const a=R(),{value:r}=y;!a||!r||r.scrollTo({left:a.offsetLeft,top:0,behavior:"smooth"})})}function ae(t){if(!t)return;const{placement:a}=e;if(a==="top"||a==="bottom"){const{scrollLeft:r,scrollWidth:s,offsetWidth:w}=t;S.value=r<=0,P.value=r+w>=s}else{const{scrollTop:r,scrollHeight:s,offsetHeight:w}=t;S.value=r<=0,P.value=r+w>=s}}const Me=le(t=>{ae(t.target)},64);Wt(be,{triggerRef:j(e,"trigger"),tabStyleRef:j(e,"tabStyle"),tabClassRef:j(e,"tabClass"),addTabStyleRef:j(e,"addTabStyle"),addTabClassRef:j(e,"addTabClass"),paneClassRef:j(e,"paneClass"),paneStyleRef:j(e,"paneStyle"),mergedClsPrefixRef:v,typeRef:j(e,"type"),closableRef:j(e,"closable"),valueRef:L,tabChangeIdRef:u,onBeforeLeaveRef:j(e,"onBeforeLeave"),activateTab:Ae,handleClose:Be,handleAdd:Fe}),_t(()=>{K(),ue()}),Et(()=>{const{value:t}=_;if(!t)return;const{value:a}=v,r=`${a}-tabs-nav-scroll-wrapper--shadow-start`,s=`${a}-tabs-nav-scroll-wrapper--shadow-end`;S.value?t.classList.remove(r):t.classList.add(r),P.value?t.classList.remove(s):t.classList.add(s)});const De={syncBarPosition:()=>{K()}},Ve=()=>{te({transitionDisabled:!0})},xe=Z(()=>{const{value:t}=W,{type:a}=e,r={card:"Card",bar:"Bar",line:"Line",segment:"Segment"}[a],s=`${t}${r}`,{self:{barColor:w,closeIconColor:k,closeIconColorHover:B,closeIconColorPressed:N,tabColor:U,tabBorderColor:Ne,paneTextColor:Ue,tabFontWeight:Xe,tabBorderRadius:Ge,tabFontWeightActive:Ke,colorSegment:qe,fontWeightStrong:Ye,tabColorSegment:Je,closeSize:Qe,closeIconSize:Ze,closeColorHover:et,closeColorPressed:tt,closeBorderRadius:at,[H("panePadding",t)]:Y,[H("tabPadding",s)]:nt,[H("tabPaddingVertical",s)]:rt,[H("tabGap",s)]:ot,[H("tabGap",`${s}Vertical`)]:it,[H("tabTextColor",a)]:st,[H("tabTextColorActive",a)]:lt,[H("tabTextColorHover",a)]:dt,[H("tabTextColorDisabled",a)]:ct,[H("tabFontSize",t)]:bt},common:{cubicBezierEaseInOut:ft}}=x.value;return{"--n-bezier":ft,"--n-color-segment":qe,"--n-bar-color":w,"--n-tab-font-size":bt,"--n-tab-text-color":st,"--n-tab-text-color-active":lt,"--n-tab-text-color-disabled":ct,"--n-tab-text-color-hover":dt,"--n-pane-text-color":Ue,"--n-tab-border-color":Ne,"--n-tab-border-radius":Ge,"--n-close-size":Qe,"--n-close-icon-size":Ze,"--n-close-color-hover":et,"--n-close-color-pressed":tt,"--n-close-border-radius":at,"--n-close-icon-color":k,"--n-close-icon-color-hover":B,"--n-close-icon-color-pressed":N,"--n-tab-color":U,"--n-tab-font-weight":Xe,"--n-tab-font-weight-active":Ke,"--n-tab-padding":nt,"--n-tab-padding-vertical":rt,"--n-tab-gap":ot,"--n-tab-gap-vertical":it,"--n-pane-padding-left":J(Y,"left"),"--n-pane-padding-right":J(Y,"right"),"--n-pane-padding-top":J(Y,"top"),"--n-pane-padding-bottom":J(Y,"bottom"),"--n-font-weight-strong":Ye,"--n-tab-color-segment":Je}}),V=f?Lt("tabs",Z(()=>`${W.value[0]}${e.type[0]}`),xe,e):void 0;return Object.assign({mergedClsPrefix:v,mergedValue:L,renderedNames:new Set,segmentCapsuleElRef:M,tabsPaneWrapperRef:q,tabsElRef:h,barElRef:g,addTabInstRef:E,xScrollInstRef:y,scrollWrapperElRef:_,addTabFixed:D,tabWrapperStyle:b,handleNavResize:He,mergedSize:W,handleScroll:Me,handleTabsResize:Ie,cssVars:f?void 0:xe,themeClass:V?.themeClass,animationDirection:ve,renderNameListRef:pe,yScrollElRef:z,handleSegmentResize:Ve,onAnimationBeforeLeave:_e,onAnimationEnter:Ee,onAnimationAfterEnter:Le,onRender:V?.onRender},De)},render(){const{mergedClsPrefix:e,type:o,placement:c,addTabFixed:p,addable:d,mergedSize:m,renderNameListRef:v,onRender:f,paneWrapperClass:x,paneWrapperStyle:h,$slots:{default:g,prefix:_,suffix:E}}=this;f?.();const y=g?ne(g()).filter(u=>u.type.__TAB_PANE__===!0):[],z=g?ne(g()).filter(u=>u.type.__TAB__===!0):[],S=!z.length,P=o==="card",W=o==="segment",$=!P&&!W&&this.justifyContent;v.value=[];const I=()=>{const u=l("div",{style:this.tabWrapperStyle,class:`${e}-tabs-wrapper`},$?null:l("div",{class:`${e}-tabs-scroll-padding`,style:c==="top"||c==="bottom"?{width:`${this.tabsPadding}px`}:{height:`${this.tabsPadding}px`}}),S?y.map((b,R)=>(v.value.push(b.props.name),de(l(ce,Object.assign({},b.props,{internalCreatedByPane:!0,internalLeftPadded:R!==0&&(!$||$==="center"||$==="start"||$==="end")}),b.children?{default:b.children.tab}:void 0)))):z.map((b,R)=>(v.value.push(b.props.name),de(R!==0&&!$?Te(b):b))),!p&&d&&P?Re(d,(S?y.length:z.length)!==0):null,$?null:l("div",{class:`${e}-tabs-scroll-padding`,style:{width:`${this.tabsPadding}px`}}));return l("div",{ref:"tabsElRef",class:`${e}-tabs-nav-scroll-content`},P&&d?l(re,{onResize:this.handleTabsResize},{default:()=>u}):u,P?l("div",{class:`${e}-tabs-pad`}):null,P?null:l("div",{ref:"barElRef",class:`${e}-tabs-bar`}))},L=W?"top":c;return l("div",{class:[`${e}-tabs`,this.themeClass,`${e}-tabs--${o}-type`,`${e}-tabs--${m}-size`,$&&`${e}-tabs--flex`,`${e}-tabs--${L}`],style:this.cssVars},l("div",{class:[`${e}-tabs-nav--${o}-type`,`${e}-tabs-nav--${L}`,`${e}-tabs-nav`]},ye(_,u=>u&&l("div",{class:`${e}-tabs-nav__prefix`},u)),W?l(re,{onResize:this.handleSegmentResize},{default:()=>l("div",{class:`${e}-tabs-rail`,ref:"tabsElRef"},l("div",{class:`${e}-tabs-capsule`,ref:"segmentCapsuleElRef"},l("div",{class:`${e}-tabs-wrapper`},l("div",{class:`${e}-tabs-tab`}))),S?y.map((u,b)=>(v.value.push(u.props.name),l(ce,Object.assign({},u.props,{internalCreatedByPane:!0,internalLeftPadded:b!==0}),u.children?{default:u.children.tab}:void 0))):z.map((u,b)=>(v.value.push(u.props.name),b===0?u:Te(u))))}):l(re,{onResize:this.handleNavResize},{default:()=>l("div",{class:`${e}-tabs-nav-scroll-wrapper`,ref:"scrollWrapperElRef"},["top","bottom"].includes(L)?l(It,{ref:"xScrollInstRef",onScroll:this.handleScroll},{default:I}):l("div",{class:`${e}-tabs-nav-y-scroll`,onScroll:this.handleScroll,ref:"yScrollElRef"},I()))}),p&&d&&P?Re(d,!0):null,ye(E,u=>u&&l("div",{class:`${e}-tabs-nav__suffix`},u))),S&&(this.animated&&(L==="top"||L==="bottom")?l("div",{ref:"tabsPaneWrapperRef",style:h,class:[`${e}-tabs-pane-wrapper`,x]},Se(y,this.mergedValue,this.renderedNames,this.onAnimationBeforeLeave,this.onAnimationEnter,this.onAnimationAfterEnter,this.animationDirection)):Se(y,this.mergedValue,this.renderedNames)))}});function Se(e,o,c,p,d,m,v){const f=[];return e.forEach(x=>{const{name:h,displayDirective:g,"display-directive":_}=x.props,E=z=>g===z||_===z,y=o===h;if(x.key!==void 0&&(x.key=h),y||E("show")||E("show:lazy")&&c.has(h)){c.has(h)||c.add(h);const z=!E("if");f.push(z?kt(x,[[Bt,y]]):x)}}),v?l(jt,{name:`${v}-transition`,onBeforeLeave:p,onEnter:d,onAfterEnter:m},{default:()=>f}):f}function Re(e,o){return l(ce,{ref:"addTabInstRef",key:"__addable",name:"__addable",internalCreatedByPane:!0,internalAddable:!0,internalLeftPadded:o,disabled:typeof e=="object"&&e.disabled})}function Te(e){const o=Ht(e);return o.props?o.props.internalLeftPadded=!0:o.props={internalLeftPadded:!0},o}function de(e){return Array.isArray(e.dynamicProps)?e.dynamicProps.includes("internalLeftPadded")||e.dynamicProps.push("internalLeftPadded"):e.dynamicProps=["internalLeftPadded"],e}export{Qt as _,Jt as a};
