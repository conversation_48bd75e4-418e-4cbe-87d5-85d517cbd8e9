import{_ as y}from"./copy-BxkeU8Ds.js";import{d as $,r as w,ac as q,ad as R,m as k,o as B,b as o,ab as C,w as d,a as t,g as u,B as T,t as s,ae as z,$ as I}from"./index-pY9FjpQW.js";import{_ as M}from"./Table-DoHSPnrC.js";const N={class:"border border-orange rounded-lg p-2 mt-4"},S={class:"font-bold"},j={class:"border rounded-lg p-2 mt-4"},G={class:"mt-4 overflow-x-auto"},O={class:"w-1/4"},P={class:"w-1/4"},A={class:"w-1/4"},D={class:"w-1/4"},L={class:"mt-4"},V={class:"mt-4 overflow-x-auto"},F={class:"w-1/4"},H={class:"w-1/4"},K={class:"w-1/4"},E={class:"w-1/4"},J={class:"mt-4"},U={class:"bg-black/70 rounded-lg px-4 pt-4"},Q={class:"absolute right-0 mr-10"},W={class:"mt-4"},X={class:"bg-black/70 rounded-lg px-4 pt-4"},Y={class:"absolute right-0 mr-10"},c=$({__name:"docGetBalance",setup(Z){const i=w(null),{t:a}=q(),f=R(),e=()=>{const l=i.value?.innerText;l&&navigator.clipboard.writeText(l).then(()=>{f.success(a("copysuccess"))})};return(l,n)=>{const r=T,p=z,g=M,m=y,b=I,v=C;return B(),k("div",null,[o(v,null,{default:d(()=>[n[50]||(n[50]=t("div",{class:"font-bold"},"Get Balance",-1)),t("div",null,[o(r,{type:"success",round:"",size:"small",class:"mt-3 cursor-default"},{default:d(()=>[u(s(l.$t("update")),1)]),_:1}),u(" "+s(l.$t("lastupdate")),1)]),t("div",N,[o(r,{class:"cursor-default",type:"warning",round:"",size:"tiny"},{default:d(()=>n[0]||(n[0]=[u("POST")])),_:1}),n[1]||(n[1]=t("span",null," {{ CALLBACK_URL }}/api/pgpro/checkBalance",-1))]),o(p),t("div",null,[t("div",S,s(l.$t("security")),1),t("div",j,[t("div",null,[o(r,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:d(()=>n[2]||(n[2]=[u("Authorization")])),_:1})]),n[4]||(n[4]=t("span",null," apiKey : Base64({{ agent_username }}:{{ secret_key }})",-1)),n[5]||(n[5]=t("hr",{class:"my-2"},null,-1)),t("div",null,[o(r,{class:"cursor-default",ghost:"",round:"",size:"tiny"},{default:d(()=>n[3]||(n[3]=[u("Content Type")])),_:1})]),n[6]||(n[6]=t("span",null," Type: application/json",-1))])]),o(p),t("div",null,[n[25]||(n[25]=t("div",{class:"font-bold"},"Parameter Description",-1)),t("div",G,[o(g,null,{default:d(()=>[t("thead",null,[t("tr",null,[t("th",O,s(l.$t("property")),1),t("th",P,s(l.$t("type")),1),t("th",A,s(l.$t("required")),1),t("th",D,s(l.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[7]||(n[7]=t("td",null,"id",-1)),n[8]||(n[8]=t("td",null,"string",-1)),n[9]||(n[9]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb1")),1)]),t("tr",null,[n[10]||(n[10]=t("td",null,"timestampMillis",-1)),n[11]||(n[11]=t("td",null,"number",-1)),n[12]||(n[12]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb2")),1)]),t("tr",null,[n[13]||(n[13]=t("td",null,"productId",-1)),n[14]||(n[14]=t("td",null,"string",-1)),n[15]||(n[15]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb3")),1)]),t("tr",null,[n[16]||(n[16]=t("td",null,"currency",-1)),n[17]||(n[17]=t("td",null,"string",-1)),n[18]||(n[18]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb4")),1)]),t("tr",null,[n[19]||(n[19]=t("td",null,"username",-1)),n[20]||(n[20]=t("td",null,"string",-1)),n[21]||(n[21]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb5")),1)]),t("tr",null,[n[22]||(n[22]=t("td",null,"sessionToken",-1)),n[23]||(n[23]=t("td",null,"string",-1)),n[24]||(n[24]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb6")),1)])])]),_:1})])]),t("div",L,[n[47]||(n[47]=t("div",{class:"font-bold"},"Response Description",-1)),t("div",V,[o(g,null,{default:d(()=>[t("thead",null,[t("tr",null,[t("th",F,s(l.$t("property")),1),t("th",H,s(l.$t("type")),1),t("th",K,s(l.$t("required")),1),t("th",E,s(l.$t("description")),1)])]),t("tbody",null,[t("tr",null,[n[26]||(n[26]=t("td",null,"id",-1)),n[27]||(n[27]=t("td",null,"string",-1)),n[28]||(n[28]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb7")),1)]),t("tr",null,[n[29]||(n[29]=t("td",null,"statusCode",-1)),n[30]||(n[30]=t("td",null,"number",-1)),n[31]||(n[31]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb8")),1)]),t("tr",null,[n[32]||(n[32]=t("td",null,"timestampMillis",-1)),n[33]||(n[33]=t("td",null,"number",-1)),n[34]||(n[34]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb9")),1)]),t("tr",null,[n[35]||(n[35]=t("td",null,"productId",-1)),n[36]||(n[36]=t("td",null,"string",-1)),n[37]||(n[37]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb10")),1)]),t("tr",null,[n[38]||(n[38]=t("td",null,"currency",-1)),n[39]||(n[39]=t("td",null,"string",-1)),n[40]||(n[40]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb11")),1)]),t("tr",null,[n[41]||(n[41]=t("td",null,"balance",-1)),n[42]||(n[42]=t("td",null,"number",-1)),n[43]||(n[43]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb12")),1)]),t("tr",null,[n[44]||(n[44]=t("td",null,"username",-1)),n[45]||(n[45]=t("td",null,"string",-1)),n[46]||(n[46]=t("td",null,"Required",-1)),t("td",null,s(l.$t("doc.gb13")),1)])])]),_:1})])]),t("div",J,[n[48]||(n[48]=t("div",{class:"font-bold mb-4"},"Request Body",-1)),t("div",U,[t("div",Q,[o(b,{trigger:"hover"},{trigger:d(()=>[o(r,{class:"text-white",onClick:e},{default:d(()=>[o(m)]),_:1})]),default:d(()=>[u(" "+s(l.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:i,class:"font-normal text-white overflow-x-auto"},`{
  "id": "string",
  "timestampMillis": 0,
  "productId": "PGSOFT",
  "currency": "THB",
  "username": "string",
  "sessionToken": "string"
}
    `,512)])]),t("div",W,[n[49]||(n[49]=t("div",{class:"font-bold mb-4"},"JSON response example",-1)),t("div",X,[t("div",Y,[o(b,{trigger:"hover"},{trigger:d(()=>[o(r,{class:"text-white",onClick:e},{default:d(()=>[o(m)]),_:1})]),default:d(()=>[u(" "+s(l.$t("copy")),1)]),_:1})]),t("pre",{ref_key:"jsonContent",ref:i,class:"font-normal text-white overflow-x-auto"},`{
    "id": "string",
    "statusCode": 0,
    "timestampMillis": "int",
    "productId": "PGSOFT",
    "currency": "THB",
    "balance": "Number",
    "username": "string"
}
    `,512)])])]),_:1})])}}});export{c as _};
