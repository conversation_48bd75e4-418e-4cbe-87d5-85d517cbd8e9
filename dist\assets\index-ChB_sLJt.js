import{_ as Z,a as X}from"./Pagination.vue_vue_type_script_setup_true_lang-tK6i-N0U.js";import{d as ee,fB as te,l as ne,r as _,ac as se,U as T,b as s,B as r,gb as w,q as ae,a3 as le,fQ as A,ad as oe,m as U,o as y,w as p,c as G,n as z,ab as ue,af as H,fD as ie,a as g,t as D,fT as re,f as l,i as I,fZ as pe,gc as ce,G as _e,a8 as de,g as L}from"./index-pY9FjpQW.js";import{_ as ye}from"./write-lGVnfd1y.js";import{m as fe}from"./index-DZ56Tu_n.js";import"./Checkbox-CwEpY3xE.js";import"./RadioGroup-DnXU-fFU.js";import"./download-C2161hUv.js";const me={class:"flex items-center gap-3 w-full sm:w-72"},ge={for:"searchInput",class:"mb-1 text-sm text-nowrap"},ve={class:"w-full"},he={class:"ml-2"},Se=ee({__name:"index",setup(ke){const{userData:o}=te(),K=ne(),f=_(""),m=_("ALL"),v=_(10),h=_(1),$=_(0),x=_([]),d=_(!1),{t:a}=se(),N=n=>n?fe(n).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm"):"-",M=T(()=>[{label:a("all"),value:"ALL"},{label:"THB",value:"THB"},{label:"KRW",value:"KRW"},{label:"USD",value:"USD"}]),V=T(()=>{const n=[{title:a("no."),align:"center",key:"index",render:(e,t)=>v.value*(h.value-1)+t+1},{title:a("loginname"),align:"center",key:"username",render:e=>e.position_type&&(e.position_type==2||e.position_type==3)?s(r,{tertiary:!0,round:!0,type:e.position_type===2?"warning":e.position_type===3?"success":"info",onClick:()=>B(e.id),style:{cursor:"pointer"}},{default:()=>[e.username]}):s(r,{tertiary:!0,round:!0,type:e.position_type===2?"warning":e.position_type===3?"success":"info",style:{cursor:"default"}},{default:()=>[e.username]})},{title:a("position"),align:"center",key:"position",render:e=>s(r,{size:"small",class:"cursor-default",ghost:!0,type:e.position_type===2?"warning":e.position_type===3?"success":"info"},{default:()=>[e.position_type===2?a("reseller"):e.position_type===3?a("agent"):a("member")]})},{title:a("currency"),align:"center",key:"currency",render:e=>{let t="";return e.position_type==3?t=e.ag_currency:(e.reseller_thb==1&&(t="THB"),e.reseller_krw==1&&(t?t+=",KRW":t="KRW"),e.reseller_usd==1&&(t?t+=",USD":t="USD")),!t&&e.currency&&(t=e.currency),t}},{title:a("wallettype"),align:"center",key:"wallet_type",render:e=>e.bet_type&&e.bet_type==1?"Seamless":e.bet_type&&e.bet_type==1?"Tranfer":"-"},{title:a("createdate"),align:"center",key:"created_at",render:e=>N(e.created_at)},{title:a("lastlogindate"),align:"center",key:"last_login",render:e=>N(e.last_login)},{title:a("lastloginip"),align:"center",key:"last_ip"},{title:a("status"),align:"center",key:"status",render:e=>s(r,{class:"cursor-default",ghost:!0,type:e.status===1||e.status==!0?"success":"error",size:"small"},{default:()=>[e.status===1||e.status==!0?a("active"):e.status===2?a("suspend"):a("inactive")]})}];return(o.position_type==2||o.position_type==1)&&n.push({title:a("edit"),align:"center",key:"actions",render:e=>e.position_type?(o.position_type==4&&o.upline_position_type==1||o.position_type==4&&o.upline_position_type==2||o.position_type==4&&o.upline_position_type==3)&&JSON.parse(o?.permissions).management!==2?s(r,{class:"cursor-default",tertiary:!0,type:"error",size:"small"},{default:()=>[s(w,null,null)]}):ae(r,{type:"warning",size:"small",onClick:()=>W(e)},{default:()=>s(ye,null,null)}):s(r,{class:"cursor-default",tertiary:!0,type:"error",size:"small"},{default:()=>[s(w,null,null)]})}),n}),u=_([]),k=_(null);function W(n){K.push({name:"edit-agent",params:{id:n.id}})}le(()=>{Y(),b()});const Y=()=>{A.get("PG/Profile/agent").then(n=>{n.data.success&&(k.value=n.data.data)})},b=async()=>{d.value=!0;const n={currency:m.value,perPage:v.value,page:h.value,search:f.value},{data:e}=await A.get("PG/Account/list",{params:n});x.value=e.data,$.value=e.total,u.value=[],d.value=!1},E=()=>{if(u.value.length&&u.value.length>1){const n=u.value[u.value.length-1];B(n.id)}else b()},B=async n=>{d.value=!0;const e={currency:m.value,perPage:v.value,page:h.value,id:n,search:f.value},{data:t}=await A.get("PG/Account/list/detall",{params:e});if(x.value=t.data,$.value=t.total,u.value.length===0){const c={id:k.value.id,username:k.value.username,position_type:k.value.position_type};u.value.push(c)}if(d.value=!1,t.user&&!u.value.some(c=>c.id===t.user.id))u.value.push(t.user),console.log(t),d.value=!1;else{const c=u.value.findIndex(C=>C.id===t.user.id);c!==-1&&c+1<u.value.length&&u.value.splice(c+1),d.value=!1}};oe();function j(n,e){window.$message.success(`${a("gotopage")} : ${n}/${e}`),v.value=e,h.value=n,b()}return(n,e)=>{const t=re,c=pe,C=ce,q=ie,R=H,P=ue,F=Z,J=X,O=H;return y(),U("div",null,[s(O,{vertical:"",size:"large"},{default:p(()=>[s(P,null,{default:p(()=>[s(R,null,{default:p(()=>[s(q,null,{default:p(()=>[g("div",me,[g("div",null,[g("label",ge,D(n.$t("loginname")),1)]),g("div",ve,[s(t,{id:"searchInput",value:l(f),"onUpdate:value":e[0]||(e[0]=i=>I(f)?f.value=i:null),type:"text",placeholder:n.$t("loginname")},null,8,["value","placeholder"])])]),s(c,{value:l(m),"onUpdate:value":e[1]||(e[1]=i=>I(m)?m.value=i:null),class:"w-full sm:w-24",options:l(M)},null,8,["value","options"]),s(l(r),{type:"primary",class:"w-full sm:w-auto",onClick:e[2]||(e[2]=i=>E())},{default:p(()=>[s(C),g("span",he,D(n.$t("search")),1)]),_:1})]),_:1})]),_:1})]),_:1}),l(u).length?(y(),G(P,{key:0},{default:p(()=>[(y(!0),U(_e,null,de(l(u),(i,S)=>(y(),U("span",{key:S},[S==0?(y(),G(l(r),{key:0,tertiary:"",round:"",type:i.position_type===1||l(o)?.position_type==4&&l(o)?.upline_position_type==1?"error":i.position_type===2||l(o)?.position_type==4&&l(o)?.upline_position_type==2?"warning":i.position_type===3||l(o)?.position_type==4&&l(o)?.upline_position_type==3?"success":"default",class:"mx-2 my-1",onClick:e[3]||(e[3]=Q=>b())},{default:p(()=>[L(D(i.username),1)]),_:2},1032,["type"])):z("",!0),S>0?(y(),G(l(r),{key:1,round:"",tertiary:"",type:i.position_type===1?"error":i.position_type===2?"warning":i.position_type===3?"success":"info",class:"mx-2 my-1",onClick:Q=>B(i.id)},{default:p(()=>[L(D(i.username),1)]),_:2},1032,["type","onClick"])):z("",!0)]))),128))]),_:1})):z("",!0),s(P,null,{default:p(()=>[s(R,{vertical:""},{default:p(()=>[s(F,{"scroll-x":800,loading:l(d),columns:l(V),data:l(x),bordered:!1},null,8,["loading","columns","data"]),s(J,{class:"overflow-auto",count:l($),onChange:j},null,8,["count"])]),_:1})]),_:1})]),_:1})])}}});export{Se as default};
