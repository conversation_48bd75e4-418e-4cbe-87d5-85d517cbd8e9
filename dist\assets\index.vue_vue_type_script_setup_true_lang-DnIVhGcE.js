import{_ as V}from"./preview-open-D9wsvucx.js";import{_ as A}from"./preview-close-one-CsVAe9aK.js";import{d as B,ac as C,r as f,m as q,o as x,b as e,w as o,g as r,t as i,f as l,fT as N,af as z,i as D,B as I,fD as j}from"./index-pY9FjpQW.js";import{_ as E}from"./text-GY30jg6U.js";import{a as H}from"./headers-CSI__REg.js";import{_ as L}from"./Form-_sVFK3VR.js";import{_ as S}from"./FormItem-B4P0FvHJ.js";import{_ as F}from"./Checkbox-CwEpY3xE.js";const Z=B({__name:"index",emits:["update:modelValue"],setup(G,{emit:w}){const h=w;function v(){h("update:modelValue","login")}const{t:_}=C(),b={account:{required:!0,trigger:"blur",message:_("login.accountRuleTip")},pwd:{required:!0,trigger:"blur",message:_("login.passwordRuleTip")},rePwd:{required:!0,trigger:"blur",message:_("login.checkPasswordRuleTip")}},a=f({account:"admin",pwd:"000000",rePwd:"000000"}),u=f(!1);function k(){}return(n,t)=>{const $=H,c=N,p=S,m=A,g=V,d=I,y=F,P=E,T=j,R=z,U=L;return x(),q("div",null,[e($,{depth:"3",class:"text-center"},{default:o(()=>[r(i(n.$t("login.registerTitle")),1)]),_:1}),e(U,{rules:b,model:l(a),"show-label":!1,size:"large"},{default:o(()=>[e(p,{path:"account"},{default:o(()=>[e(c,{value:l(a).account,"onUpdate:value":t[0]||(t[0]=s=>l(a).account=s),clearable:"",placeholder:n.$t("login.accountPlaceholder")},null,8,["value","placeholder"])]),_:1}),e(p,{path:"pwd"},{default:o(()=>[e(c,{value:l(a).pwd,"onUpdate:value":t[1]||(t[1]=s=>l(a).pwd=s),type:"password",placeholder:n.$t("login.passwordPlaceholder"),clearable:"","show-password-on":"click"},{"password-invisible-icon":o(()=>[e(m)]),"password-visible-icon":o(()=>[e(g)]),_:1},8,["value","placeholder"])]),_:1}),e(p,{path:"rePwd"},{default:o(()=>[e(c,{value:l(a).rePwd,"onUpdate:value":t[2]||(t[2]=s=>l(a).rePwd=s),type:"password",placeholder:n.$t("login.checkPasswordPlaceholder"),clearable:"","show-password-on":"click"},{"password-invisible-icon":o(()=>[e(m)]),"password-visible-icon":o(()=>[e(g)]),_:1},8,["value","placeholder"])]),_:1}),e(p,null,{default:o(()=>[e(R,{vertical:"",size:20,class:"w-full"},{default:o(()=>[e(y,{checked:l(u),"onUpdate:checked":t[3]||(t[3]=s=>D(u)?u.value=s:null)},{default:o(()=>[r(i(n.$t("login.readAndAgree"))+" ",1),e(d,{type:"primary",text:""},{default:o(()=>[r(i(n.$t("login.userAgreement")),1)]),_:1})]),_:1},8,["checked"]),e(d,{block:"",type:"primary",onClick:k},{default:o(()=>[r(i(n.$t("login.signUp")),1)]),_:1}),e(T,{justify:"center"},{default:o(()=>[e(P,null,{default:o(()=>[r(i(n.$t("login.haveAccountText")),1)]),_:1}),e(d,{text:"",type:"primary",onClick:v},{default:o(()=>[r(i(n.$t("login.signIn")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])])}}});export{Z as _};
