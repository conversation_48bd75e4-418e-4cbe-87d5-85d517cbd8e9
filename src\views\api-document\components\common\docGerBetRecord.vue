<script setup lang="ts">
import { useMessage } from 'naive-ui';
const jsonContent = ref<HTMLElement | null>(null); 
const {t} = useI18n()
const message = useMessage();
const copyToClipboard = () => {
  const content = jsonContent.value?.innerText;
  if (content) {
    navigator.clipboard.writeText(content).then(() => {
      message.success(t('copysuccess'));
    })
  }
};</script>
<template>
  <div>
    <n-card>
      <div class="font-bold">Get Bet Records</div>
      <div>
        <n-button
          type="success"
          round
          size="small"
          class="mt-3 cursor-default"
          >{{ $t("update") }}</n-button
        >
        {{ $t("lastupdate") }}
      </div>
      <div class="border border-orange rounded-lg p-2 mt-4">
        <n-button class="cursor-default" type="warning" round size="tiny"
          >POST</n-button
        >
        <span v-pre> {{ API_URL }}/seamless/betTransactionReplay</span>
      </div>
      <n-divider></n-divider>
      <!-- Security -->
      <div>
        <div class="font-bold">{{$t('security')}}</div>
        <div class="border rounded-lg p-2 mt-4">
          <div>
            <n-button class="cursor-default" ghost round size="tiny"
              >Authorization</n-button
            >
          </div>
          <span v-pre>
            apiKey : Base64({{ agent_username }}:{{ secret_key }})</span
          >
          <hr class="my-2" />
          <div>
            <n-button class="cursor-default" ghost round size="tiny"
              >Content Type</n-button
            >
          </div>
          <span v-pre> Type: application/json</span>
        </div>
      </div>
      <n-divider></n-divider>

       <!-- Parameter  -->
       <div>
        <div class="font-bold">Parameter Description</div>
        <div class="mt-4 overflow-x-auto ">
          <n-table>
            <thead>
              <tr>
                <th class="w-1/4">{{ $t("property") }}</th>
                <th class="w-1/4">{{ $t("type") }}</th>
                <th class="w-1/4">{{ $t("required") }}</th>
                <th class="w-1/4">{{ $t("description") }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>gameCode</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gbr1") }}</td>
              </tr>
              <tr>
                <td>productCode</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gbr2") }}</td>
              </tr>
              <tr>
                <td>RoundId</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gbr3") }}</td>
              </tr>
              <tr>
                <td>txnId</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gbr4") }}</td>
              </tr>
            </tbody>
          </n-table>
        </div>
      </div>

      <!-- Response -->
      <div class="mt-4">
        <div class="font-bold">Response Description</div>
        <div class="mt-4 overflow-x-auto">
          <n-table>
            <thead>
              <tr>
                <th class="w-1/4">{{ $t("property") }}</th>
                <th class="w-1/4">{{ $t("type") }}</th>
                <th class="w-1/4">{{ $t("required") }}</th>
                <th class="w-1/4">{{ $t("description") }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>data</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gbr5") }}</td>
              </tr>
              <tr>
                <td>err</td>
                <td>string</td>
                <td>Required</td>
                <td>{{ $t("doc.gbr6") }}</td>
              </tr>
            </tbody>
          </n-table>
        </div>
      </div>

      <!-- Request Body  -->
      <div class="mt-4">
        <div class="font-bold mb-4">Request Body</div>
        <div class="bg-black/70 rounded-lg px-4 pt-4">
          <div class="absolute right-0 mr-10">
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button class="text-white" @click="copyToClipboard"
                  ><icon-park-outline-copy
                /></n-button>
              </template>
              {{ $t("copy") }}
            </n-tooltip>
          </div>
          <pre ref="jsonContent" class="font-normal text-white overflow-x-auto">
{
    "gameCode": "string",
    "productCode": "PGSOFT",
    "RoundId": "string",
    "txnId": "string"
}
    </pre
          >
        </div>
      </div>

      <!-- JSON response example  -->
      <div class="mt-4">
        <div class="font-bold mb-4">JSON response example</div>
        <div class="bg-black/70 rounded-lg px-4 pt-4">
          <div class="absolute right-0 mr-10">
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button class="text-white" @click="copyToClipboard"
                  ><icon-park-outline-copy
                /></n-button>
              </template>
              {{ $t("copy") }}
            </n-tooltip>
          </div>
          <pre ref="jsonContent" class="font-normal text-white overflow-x-auto">
{
    "data": {
    "url": "string"
}
    "err": {}
}
    </pre
          >
        </div>
      </div>
    </n-card>
  </div>
</template>
