import{_ as O}from"./add-one-BP9Rv5dm.js";import{d as Q,fB as R,fS as q,ad as K,ac as L,r,a3 as W,fQ as C,U as X,c as Y,o as V,w as n,b as l,af as P,a as m,fD as Z,m as ee,n as te,g as b,t as d,B as le,ab as ne,fT as ae,f as s,i as p,ah as se}from"./index-pY9FjpQW.js";import{_ as oe}from"./Slider-DWNZsvrI.js";import{a as ue}from"./headers-CSI__REg.js";import{_ as ie}from"./FormItem-B4P0FvHJ.js";const re={class:"flex gap-3 mb-10"},me={key:0,class:"flex items-center"},de={class:"flex justify-between items-center mb-2"},_e={class:"text-sm font-medium"},ce={class:"flex justify-between items-center mb-2"},pe={class:"text-sm font-medium"},ve={class:"flex justify-between items-center mb-2"},fe={class:"text-sm font-medium"},ke=Q({__name:"setting",emits:["save"],setup(be,{emit:M}){const{userData:_}=R(),T=M;q({condition_1:"",condition_2:"",condition_3:"",condition_4:""});const g=K(),{t:z}=L(),D=r(!1),A=()=>{const e=Number(o.value)+Number(u.value)+Number(y.value)+Number(N.value)+Number(w.value);if(v.value<=0)return g.error(z("balaceNot0"));if(e!=100)return g.error(z("validlv2tolv6"));const t={balance:v.value,lv1:i.value,lv2:o.value,lv3:u.value,lv4:y.value,lv5:N.value,lv6:w.value,status:D.value};C.post("demo/UpdateSetting",t).then(f=>{f.data.status?(g.success(f.data.mes),j(),T("save",!0)):g.error(f.data.mes)})},c=()=>!((_?.position_type==4&&_?.upline_position_type==3||_?.position_type==4&&_?.upline_position_type==2||_?.position_type==4&&_?.upline_position_type==1)&&JSON.parse(_?.permissions).setting!==2),v=r(0),i=r(0),o=r(0),u=r(0),y=r(0),N=r(0),w=r(0),x=r(null);W(()=>{j(),E()});const j=async()=>{try{const{data:e}=await C.get("demo/Setting");e&&(v.value=e.balance,i.value=e.level1,o.value=e.level2,u.value=e.level3,y.value=e.level4,N.value=e.level5,w.value=e.level6,D.value=e.status)}catch(e){console.log(e)}},h=(e,t)=>{if(Number(e)>100&&t==1)return i.value=100;if(Number(e)>100&&t==2)return o.value=100;if(Number(e)>100&&t==3)return u.value=100;if(Number(e)>100&&t==4)return y.value=100;if(Number(e)>100&&t==5)return N.value=100;if(Number(e)>100&&t==6)return w.value=100},E=async()=>{try{const e=await C.get("PG/Profile");e.data&&(x.value=e.data)}catch(e){console.log(e)}};return X(()=>x.value&&x.value.credit_thb!==void 0?Number(x.value.credit_thb).toFixed(2):"0.00"),(e,t)=>{const f=ue,F=O,G=le,I=Z,k=ae,H=ie,U=ne,$=se,B=oe,S=P,J=P;return V(),Y(I,null,{default:n(()=>[l(J,{vertical:"",class:"flex-1"},{default:n(()=>[m("div",null,[l(I,{justify:"space-between"},{default:n(()=>[m("div",re,[l(f,null,{default:n(()=>[b(d(e.$t("settingDemo")),1)]),_:1})]),c()?(V(),ee("div",me,[l(G,{type:"warning",onClick:A,class:"ml-5"},{icon:n(()=>[l(F)]),default:n(()=>[b(" "+d(e.$t("play")),1)]),_:1})])):te("",!0)]),_:1}),l(U,{class:"mb-3 -mt-5"},{default:n(()=>[l(H,{label:e.$t("credit"),path:"condition_3","label-placement":"left"},{default:n(()=>[l(k,{value:s(v),"onUpdate:value":t[0]||(t[0]=a=>p(v)?v.value=a:null),type:"number",placeholder:""},null,8,["value"])]),_:1},8,["label"])]),_:1}),l(U,{class:"mb-3"},{default:n(()=>[m("div",de,[m("label",_e,d(e.$t("settinglv1")),1),l($,{size:"small",type:"primary"},{default:n(()=>[b(d(s(i))+"%",1)]),_:1})]),l(S,{vertical:"",size:"small",class:"w-full"},{default:n(()=>[l(B,{value:s(i),"onUpdate:value":t[1]||(t[1]=a=>p(i)?i.value=a:null),min:0,max:100,step:1,disabled:!c(),tooltip:!0},null,8,["value","disabled"]),l(k,{value:s(i),"onUpdate:value":t[2]||(t[2]=a=>p(i)?i.value=a:null),type:"number",placeholder:"",disabled:!c(),onInput:t[3]||(t[3]=a=>h(s(i),1))},null,8,["value","disabled"])]),_:1})]),_:1}),l(U,{class:"mb-3"},{default:n(()=>[m("div",ce,[m("label",pe,d(e.$t("settinglv2")),1),l($,{size:"small",type:"primary"},{default:n(()=>[b(d(s(o))+"%",1)]),_:1})]),l(S,{vertical:"",size:"small",class:"w-full"},{default:n(()=>[l(B,{value:s(o),"onUpdate:value":t[4]||(t[4]=a=>p(o)?o.value=a:null),min:0,max:100,step:1,disabled:!c(),tooltip:!0},null,8,["value","disabled"]),l(k,{value:s(o),"onUpdate:value":t[5]||(t[5]=a=>p(o)?o.value=a:null),type:"number",placeholder:"",disabled:!c(),onInput:t[6]||(t[6]=a=>h(s(o),2))},null,8,["value","disabled"])]),_:1})]),_:1}),l(U,{class:"mb-3"},{default:n(()=>[m("div",ve,[m("label",fe,d(e.$t("settinglv3")),1),l($,{size:"small",type:"primary"},{default:n(()=>[b(d(s(u))+"%",1)]),_:1})]),l(S,{vertical:"",size:"small",class:"w-full"},{default:n(()=>[l(B,{value:s(u),"onUpdate:value":t[7]||(t[7]=a=>p(u)?u.value=a:null),min:0,max:100,step:1,disabled:!c(),tooltip:!0},null,8,["value","disabled"]),l(k,{value:s(u),"onUpdate:value":t[8]||(t[8]=a=>p(u)?u.value=a:null),type:"number",placeholder:"",disabled:!c(),onInput:t[9]||(t[9]=a=>h(s(u),3))},null,8,["value","disabled"])]),_:1})]),_:1})])]),_:1})]),_:1})}}});export{ke as _};
