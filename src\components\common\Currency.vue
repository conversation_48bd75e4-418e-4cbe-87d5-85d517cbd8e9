<script setup lang="tsx">
import http from "@/service/axios";
const profile = ref([]);
const GetProfile = async () => {
  const { data: res } = await http.get("v1/Profile/agent");
  console.log(res);
  profile.value = res.data;
};
const options = computed(() => [
  ...(profile.value.reseller_thb === 1 || profile.value.ag_currency === "THB" ? [{
    icon: "/images/country/th.webp",
    currency: Commas(credit_thb.value),
    symbol: "฿",
  }] : []),
  ...(profile.value.reseller_usd === 1 || profile.value.ag_currency === "USD" ? [{
    icon: "/images/country/us.webp",
    currency: Commas(credit_usd.value),
    symbol: "$",
  }] : []),
  ...(profile.value.reseller_krw === 1 || profile.value.ag_currency === "KRW" ? [{
    icon: "/images/country/kr.webp",
    currency: Commas(credit_krw.value),
    symbol: "₩",
  }] : [])
]);


function renderLabel(option) {
  return (
    <div class="flex items-center justify-center gap-2">
      <img src={option.icon} alt="currency icon" class="w-auto h-5" />
      <span>
        {option.currency} {option.symbol}
      </span>
    </div>
  );
}
onMounted(() => {
  Credit();
  GetProfile();
});
const loading = ref(false);
const credit_thb = ref(0);
const credit_krw = ref(0);
const credit_usd = ref(0);
const reload = () => {
    // Credit()
}
const Credit = async () => {
  loading.value = true;
  const { data: res } = await http.get("v1/wallet/credit");
  credit_thb.value = res.credit_thb;
  credit_krw.value = res.credit_krw;
  credit_usd.value = res.credit_usd;
  loading.value = false;
};
const Commas = (x: any) => {
  if (!x || x == "-0") {
    return 0;
  }
  if (x % 1 !== 0) {
    let roundedNumber = Math.round(x * 100) / 100;
    return roundedNumber.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
</script>

<template>
  <div class="block sm:hidden" @click="reload">
    <n-popselect :options="options" :render-label="renderLabel" trigger="click" >
      <CommonWrapper>
        <icon-park-outline-currency />
      </CommonWrapper>
    </n-popselect>
  </div>
</template>

<style scoped>
.flex {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
